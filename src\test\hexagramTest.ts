// 六爻逻辑测试文件
import {
  calculateHexagram,
  calculateSixSpirits,
  calculateSixRelations,
  calculateWorldAndResponse,
  getNajia,
  findReps,
  palace,
  getType,
  GZ5X,
  xkong,
  seekForOrigin,
  calculateChangedHexagram,
  getStemBranch,
  getVoidness,
  getStemBranchSync,
  getAllVoidnessSync,
  calculateHiddenSpirits
} from '../utils/hexagramCalculator';
import { YaoType } from '../components/YaoLine';
import { GUA64 } from '../constants/najia';

// 用户测试案例：2字1背 0字3背 2字1背 2字1背 2字1背 2字1背
// 转换为爻线：阴 阴(动) 阴 阴 阴 阴 = 坤为地卦
const userTestLines: YaoType[] = ['yin', 'yin_changing', 'yin', 'yin', 'yin', 'yin'];

// 测试用例：地天泰卦 (111000)
const testLines: YaoType[] = ['yang', 'yang', 'yang', 'yin', 'yin', 'yin'];

// 额外测试用例：乾为天卦 (111111)
const qianLines: YaoType[] = ['yang', 'yang', 'yang', 'yang', 'yang', 'yang'];

console.log('=== 六爻逻辑测试 ===');

// 用户测试案例分析
console.log('\n=== 用户测试案例分析 ===');
console.log('起卦结果：2字1背 0字3背 2字1背 2字1背 2字1背 2字1背');
console.log('爻线类型：', userTestLines);

// 1. 测试卦象计算
const userHexagram = calculateHexagram(userTestLines);
console.log('1. 卦象计算:', userHexagram);

// 2. 测试世应位置
const userWorldResponse = calculateWorldAndResponse(userTestLines);
console.log('2. 世应位置:', userWorldResponse);

// 3. 测试纳甲配置
const userBinary = userTestLines.map(line => {
  const isYang = line === 'yang' || line === 'yang_changing';
  return isYang ? '1' : '0';
}).join('');
console.log('3. 二进制码:', userBinary);

const userNajia = getNajia(userBinary);
console.log('4. 纳甲配置:', userNajia);

// 5. 测试六亲关系
const userSixRelations = calculateSixRelations(userHexagram.number, userTestLines);
console.log('5. 六亲关系:', userSixRelations);

// 6. 测试卦宫
const userOrigin = seekForOrigin(userTestLines);
console.log('6. 卦宫:', userOrigin);

// 7. 详细调试六亲计算
console.log('\n=== 详细调试六亲计算 ===');
console.log('卦宫:', userOrigin, '属性:', userOrigin === 0 ? '土(2)' : '其他');

for (let i = 0; i < userNajia.length; i++) {
  const ganZhi = userNajia[i];
  const diZhi = ganZhi[1];
  const earthEnum = diZhi === '未' ? 'WEI' :
                   diZhi === '巳' ? 'SI' :
                   diZhi === '卯' ? 'MAO' :
                   diZhi === '丑' ? 'CHOU' :
                   diZhi === '亥' ? 'HAI' :
                   diZhi === '酉' ? 'YOU' : diZhi;

  console.log(`第${i+1}爻: ${ganZhi} -> 地支:${diZhi} -> 枚举:${earthEnum}`);

  // 手动检查六亲
  const property = 2; // 坤宫属土
  let liuqin = '未知';

  // 检查官鬼
  if ([[2, 'YIN'], [2, 'MAO']].some(([p, e]) => p === property && e === earthEnum)) {
    liuqin = '官鬼';
  }
  // 检查妻财
  else if ([[2, 'HAI'], [2, 'ZI']].some(([p, e]) => p === property && e === earthEnum)) {
    liuqin = '妻财';
  }
  // 检查兄弟
  else if ([[2, 'CHEN'], [2, 'XU'], [2, 'CHOU'], [2, 'WEI']].some(([p, e]) => p === property && e === earthEnum)) {
    liuqin = '兄弟';
  }
  // 检查父母
  else if ([[2, 'SI'], [2, 'WU']].some(([p, e]) => p === property && e === earthEnum)) {
    liuqin = '父母';
  }
  // 检查子孙
  else if ([[2, 'SHEN'], [2, 'YOU']].some(([p, e]) => p === property && e === earthEnum)) {
    liuqin = '子孙';
  }

  console.log(`  -> 六亲: ${liuqin}`);
}

// 8. 反向查找：哪个卦象的六爻是申金
console.log('\n=== 反向查找六爻为申金的卦象 ===');
for (let i = 0; i < 64; i++) {
  const binary = i.toString(2).padStart(6, '0');
  const najia = getNajia(binary);
  if (najia[5].includes('申')) {
    const hexagramName = GUA64[binary] || '未知';
    console.log(`卦象: ${hexagramName} (${binary}) -> 六爻: ${najia[5]}`);
  }
}

console.log('\n=== 原有测试案例 ===');

// 1. 测试卦象计算
const hexagram = calculateHexagram(testLines);
console.log('1. 卦象计算:', hexagram);

// 2. 测试世应位置
const worldResponse = calculateWorldAndResponse(testLines);
console.log('2. 世应位置:', worldResponse);

// 3. 测试六神计算（甲子日）
const sixSpirits = calculateSixSpirits('甲');
console.log('3. 六神（甲日起）:', sixSpirits);

// 4. 测试纳甲配置
const binaryString = testLines.map(line => {
  const isYang = line === 'yang' || line === 'yang_changing';
  return isYang ? '1' : '0';
}).join('');
const najia = getNajia(binaryString);
console.log('4. 纳甲配置:', najia);

// 5. 测试卦宫判断
const gong = seekForOrigin(testLines);
console.log('5. 卦宫:', gong);

// 6. 测试六亲计算
const sixRelations = calculateSixRelations(hexagram.number, testLines);
console.log('6. 六亲关系:', sixRelations);

// 7. 测试卦象类型
const hexagramType = getType(binaryString);
console.log('7. 卦象类型:', hexagramType);

// 8. 测试干支五行
const gz5x = GZ5X('甲子');
console.log('8. 干支五行:', gz5x);

// 9. 测试旬空
const voidness = xkong('甲子');
console.log('9. 旬空:', voidness);

// 10. 测试新的干支计算
const testDate = new Date();
getStemBranch(testDate).then(newStemBranch => {
  console.log('10. 新干支计算:', newStemBranch);
}).catch(error => {
  console.error('干支计算失败:', error);
});

// 11. 测试新的空亡计算
getVoidness(testDate).then(newVoidness => {
  console.log('11. 新空亡计算:', newVoidness);
}).catch(error => {
  console.error('空亡计算失败:', error);
});

// 12. 探索lunar-javascript的空亡API
import('lunar-javascript').then(({ Solar }) => {
  const solar = Solar.fromYmdHms(2025, 7, 25, 14, 30, 0);
  const lunar = solar.getLunar();
  const eightChar = lunar.getEightChar();

  console.log('12. 探索空亡API:');
  console.log('八字:', eightChar.toString());

  // 尝试不同的空亡方法
  try {
    console.log('日空亡:', eightChar.getDayXunKong());
  } catch (e) {
    console.log('getDayXunKong 不存在');
  }

  try {
    console.log('年空亡:', eightChar.getYearXunKong());
  } catch (e) {
    console.log('getYearXunKong 不存在');
  }

  try {
    console.log('月空亡:', eightChar.getMonthXunKong());
  } catch (e) {
    console.log('getMonthXunKong 不存在');
  }

  try {
    console.log('时空亡:', eightChar.getTimeXunKong());
  } catch (e) {
    console.log('getTimeXunKong 不存在');
  }

  // 查看所有可用方法
  console.log('EightChar 可用方法:', Object.getOwnPropertyNames(Object.getPrototypeOf(eightChar)));
}).catch(error => {
  console.error('探索API失败:', error);
});

// 13. 测试伏神计算
console.log('13. 伏神计算测试:');
const hiddenSpirits = calculateHiddenSpirits(userTestLines, userSixRelations);
console.log('伏神信息:', hiddenSpirits);

// 分析六亲是否齐全
const relationTypes = ['父母', '官鬼', '兄弟', '子孙', '妻财'];
const existingTypes = userSixRelations.map(rel => rel.substring(0, 2));
const missingRelations = relationTypes.filter(type => !existingTypes.includes(type));
console.log('现有六亲:', existingTypes);
console.log('缺失六亲:', missingRelations);

// 创建一个六亲不全的测试用例
console.log('\n=== 六亲不全的测试用例 ===');
// 使用一个特定的卦象，确保六亲不全
const incompleteTestLines: YaoType[] = ['yang', 'yin', 'yang', 'yin', 'yin', 'yang']; // 随机选择
const incompleteHexagram = calculateHexagram(incompleteTestLines);
const incompleteSixRelations = calculateSixRelations(incompleteHexagram.number, incompleteTestLines);
const incompleteHiddenSpirits = calculateHiddenSpirits(incompleteTestLines, incompleteSixRelations);

console.log('测试卦象:', incompleteHexagram.name);
console.log('六亲关系:', incompleteSixRelations);
const incompleteExistingTypes = incompleteSixRelations.map(rel => rel.substring(0, 2));
const incompleteMissingRelations = relationTypes.filter(type => !incompleteExistingTypes.includes(type));
console.log('现有六亲:', incompleteExistingTypes);
console.log('缺失六亲:', incompleteMissingRelations);
console.log('伏神信息:', incompleteHiddenSpirits);

// 14. 测试新的准确计算器
console.log('14. 准确计算器测试:');
import { getStemBranchAccurate, getAllVoidnessAccurate, lunarCalculator } from '../utils/lunarCalculator';

// 等待库加载
setTimeout(() => {
  console.log('库加载状态:', lunarCalculator.isLibraryLoaded());
  const accurateStemBranch = getStemBranchAccurate(testDate);
  console.log('准确干支计算:', accurateStemBranch);

  const accurateAllVoidness = getAllVoidnessAccurate(testDate);
  console.log('准确空亡计算:', accurateAllVoidness);
}, 1000);

// 10. 测试六亲生克关系
const qin6Test = findReps(0, 'MAO'); // 金宫，卯木 -> 妻财
console.log('10. 六亲生克（金宫卯木）:', qin6Test);

// 11. 测试乾卦
console.log('=== 乾卦测试 ===');
const qianHexagram = calculateHexagram(qianLines);
const qianWorldResponse = calculateWorldAndResponse(qianLines);
const qianSixRelations = calculateSixRelations(qianHexagram.number, qianLines);
console.log('乾卦名称:', qianHexagram.name);
console.log('乾卦世应:', qianWorldResponse);
console.log('乾卦六亲:', qianSixRelations);

console.log('=== 测试完成 ===');

// 测试变卦的六亲地支五行计算
console.log('\n=== 变卦测试 ===');
const testLinesWithChanging: YaoType[] = ['yin', 'yin_changing', 'yin', 'yin', 'yin', 'yin'];
console.log('测试爻线（有动爻）:', testLinesWithChanging);

const changedHexagram = calculateChangedHexagram(testLinesWithChanging);
if (changedHexagram) {
  console.log('变卦名称:', changedHexagram.name);
  console.log('变卦六亲:', changedHexagram.sixRelations);
  console.log('变卦世应:', { worldPosition: changedHexagram.worldPosition, responsePosition: changedHexagram.responsePosition });

  // 详细分析变卦
  const changedLines = testLinesWithChanging.map(line => {
    if (line === 'yang_changing') return 'yin';
    if (line === 'yin_changing') return 'yang';
    return line;
  });
  console.log('变卦爻线:', changedLines);

  const changedBinary = changedLines.map(line => {
    const isYang = line.includes('yang');
    return isYang ? '1' : '0';
  }).join('');
  console.log('变卦二进制:', changedBinary);

  const changedOrigin = seekForOrigin(changedLines as YaoType[]);
  console.log('变卦卦宫:', changedOrigin);
} else {
  console.log('无变卦');
}

// 导出测试函数供其他地方使用
export const runHexagramTests = () => {
  return {
    hexagram,
    worldResponse,
    sixSpirits,
    najia,
    gong,
    sixRelations,
    hexagramType,
    gz5x,
    voidness,
    qin6Test,
    qianHexagram,
    qianWorldResponse,
    qianSixRelations
  };
};
