import React from 'react';
import { Button } from '@/components/ui/button';
import { useLanguageStore, Language } from '@/stores/languageStore';
import { Globe } from 'lucide-react';

export const LanguageSwitcher: React.FC = () => {
  const { currentLanguage, setLanguage } = useLanguageStore();
  
  const toggleLanguage = () => {
    const newLanguage: Language = currentLanguage === 'zh' ? 'en' : 'zh';
    setLanguage(newLanguage);
  };
  
  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={toggleLanguage}
      className="flex items-center space-x-2"
    >
      <Globe className="h-4 w-4" />
      <span className="text-sm font-medium">
        {currentLanguage === 'zh' ? '中文' : 'EN'}
      </span>
    </Button>
  );
};