import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Badge } from './ui/badge';
import { Button } from './ui/button';
import { useToast } from './ui/use-toast';
import { useAuthStore } from '@/stores/authStore';
import { supabase } from '@/integrations/supabase/client';
import { PaymentButton } from './PaymentButton';
import { Loader2, Coins, RefreshCw } from 'lucide-react';
import { useTranslation } from '@/hooks/useTranslation';

interface UserCreditsData {
  credits_remaining: number;
  total_purchased: number;
  last_purchase_at: string | null;
}

export const UserCredits: React.FC = () => {
  const [credits, setCredits] = useState<UserCreditsData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const { user } = useAuthStore();
  const { toast } = useToast();
  const { t } = useTranslation();

  const fetchCredits = async () => {
    if (!user) {
      setIsLoading(false);
      return;
    }

    try {
      const { data, error } = await supabase
        .from('user_credits')
        .select('credits_remaining, total_purchased, last_purchase_at')
        .eq('user_id', user.id)
        .single();

      if (error) {
        console.error("Error fetching credits:", error);
        // 如果是记录不存在，创建新记录
        if (error.code === 'PGRST116') {
          console.log("User credits record not found, creating new record...");
          try {
            const { data: newCredits, error: createError } = await supabase
              .from('user_credits')
              .insert({
                user_id: user.id,
                credits_remaining: 0,
                total_purchased: 0,
                last_purchase_at: null
              })
              .select('credits_remaining, total_purchased, last_purchase_at')
              .single();

            if (createError) {
              console.error("Error creating credits record:", createError);
              throw createError;
            }

            setCredits(newCredits);
            console.log("New credits record created successfully");
          } catch (createError) {
            console.error("Failed to create credits record:", createError);
            // 如果创建失败，设置默认值
            setCredits({ credits_remaining: 0, total_purchased: 0, last_purchase_at: null });
          }
        } else {
          throw error;
        }
      } else {
        setCredits(data || { credits_remaining: 0, total_purchased: 0, last_purchase_at: null });
      }
    } catch (error) {
      console.error("Error fetching credits:", error);
      toast({
        title: "获取余额失败",
        description: "无法获取您的分析次数",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await fetchCredits();
  };

  const handlePaymentSuccess = () => {
    // Refresh credits after successful payment
    fetchCredits();
    toast({
      title: "支付成功",
      description: "您的分析次数已更新",
    });
  };

  useEffect(() => {
    fetchCredits();
  }, [user]);

  if (!user) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <p className="text-muted-foreground">请登录查看您的分析次数</p>
        </CardContent>
      </Card>
    );
  }

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <Loader2 className="h-6 w-6 animate-spin mx-auto" />
          <p className="text-sm text-muted-foreground mt-2">加载中...</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-lg font-medium flex items-center">
          <Coins className="mr-2 h-5 w-5" />
          分析次数
        </CardTitle>
        <Button
          variant="ghost"
          size="sm"
          onClick={handleRefresh}
          disabled={isRefreshing}
        >
          <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
        </Button>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <span className="text-sm text-muted-foreground">剩余次数</span>
          <Badge variant={credits?.credits_remaining && credits.credits_remaining > 0 ? "default" : "secondary"}>
            {credits?.credits_remaining || 0} 次
          </Badge>
        </div>
        
        <div className="flex items-center justify-between">
          <span className="text-sm text-muted-foreground">总购买次数</span>
          <span className="text-sm font-medium">{credits?.total_purchased || 0} 次</span>
        </div>

        {credits?.last_purchase_at && (
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">最后购买</span>
            <span className="text-sm">
              {new Date(credits.last_purchase_at).toLocaleDateString('zh-CN')}
            </span>
          </div>
        )}

        <div className="pt-4 space-y-2">
          <PaymentButton onPaymentSuccess={handlePaymentSuccess} />
          
          {(!credits?.credits_remaining || credits.credits_remaining === 0) && (
            <p className="text-xs text-muted-foreground text-center">
              您当前没有分析次数，请购买后使用专业分析功能
            </p>
          )}
        </div>
      </CardContent>
    </Card>
  );
};