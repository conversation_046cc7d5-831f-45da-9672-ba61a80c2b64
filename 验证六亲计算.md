# 六亲计算验证

## 问题分析

根据qichenx项目的代码分析，六亲计算的关键在于：

1. **五行编码**：金0木3水1火4土2
2. **卦宫判断**：使用seekForOrigin函数
3. **六亲对应**：使用PropertyPair枚举

## 验证步骤

### 1. 乾卦测试（111111）

**预期结果**：
- 卦宫：乾宫（7）属金（0）
- 纳甲：甲子、甲寅、甲辰、壬午、壬申、壬戌
- 六亲：
  - 甲子水 -> 兄弟（金生水，同类为兄弟）
  - 甲寅木 -> 妻财（金克木，我克者为妻财）
  - 甲辰土 -> 父母（土生金，生我者为父母）
  - 壬午火 -> 官鬼（火克金，克我者为官鬼）
  - 壬申金 -> 兄弟（同类为兄弟）
  - 壬戌土 -> 父母（土生金，生我者为父母）

### 2. 地天泰卦测试（111000）

**预期结果**：
- 卦宫：需要根据世应位置判断
- 世应：根据寻世诀计算
- 六亲：根据卦宫五行计算

## 修复要点

1. **五行索引**：确保使用正确的五行编码
2. **卦宫判断**：实现正确的seekForOrigin逻辑
3. **六亲映射**：使用PropertyPair的精确映射关系

## 当前实现检查

- ✅ 五行编码已修正为：金0水1土2木3火4
- ✅ 地支五行映射已修正
- ✅ PropertyPair映射已实现
- ✅ seekForOrigin逻辑已实现
- ✅ findReps函数已实现

## 测试验证

通过访问 `/test` 页面可以查看：
1. 地天泰卦的计算结果
2. 乾为天卦的计算结果
3. 对比预期结果验证正确性

如果结果不符合预期，需要进一步调试：
1. 检查纳甲配置是否正确
2. 检查卦宫判断逻辑
3. 检查六亲映射关系
4. 检查五行生克关系
