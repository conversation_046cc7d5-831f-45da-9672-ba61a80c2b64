import { YaoType } from './YaoLine';
import {
  calculateHexagram,
  calculateSixSpirits,
  calculateSixRelations,
  calculateWorldAndResponse,
  calculateHiddenSpirits,
  getSolarTerm,
  getSpiritsWithBranches,
  calculateChangedHexagram,
  getType,
  HiddenSpirit
} from '@/utils/hexagramCalculator';
import { getStemBranchAccurate, getAllVoidnessAccurate } from '@/utils/lunarCalculator';

export interface Hexagram {
  lines: YaoType[];
  name: string;
  number: number;
  sixSpirits: string[];
  sixRelations: string[];
  worldPosition: number;
  responsePosition: number;
  hexagramType: string;
  hiddenSpirits: HiddenSpirit[]; // 伏神信息
  divinationInfo: {
    date: string;
    solarTerm: string;
    stems: {
      year: string;
      month: string;
      day: string;
      hour: string;
    };
    voidness: {
      year: string[];
      month: string[];
      day: string[];
      hour: string[];
    };
    spirits: string[];
  };
  changedHexagram?: {
    name: string;
    lines: YaoType[];
    sixSpirits: string[];
    sixRelations: string[];
    worldPosition: number;
    responsePosition: number;
  };
}

// 生成随机爻线
const generateRandomLines = (): YaoType[] => {
  const lines: YaoType[] = [];

  for (let i = 0; i < 6; i++) {
    const coins = [
      Math.random() > 0.5 ? 3 : 2,
      Math.random() > 0.5 ? 3 : 2,
      Math.random() > 0.5 ? 3 : 2
    ];
    const sum = coins.reduce((a, b) => a + b, 0);

    if (sum === 6) lines.push('yin_changing');
    else if (sum === 9) lines.push('yang_changing');
    else if (sum === 7) lines.push('yang');
    else lines.push('yin');
  }

  return lines;
};

// 生成完整卦象
export const generateHexagram = (lines?: YaoType[], customDate?: Date): Hexagram => {
  const generatedLines = lines || generateRandomLines();
  const { name, number } = calculateHexagram(generatedLines);
  const { worldPosition, responsePosition } = calculateWorldAndResponse(generatedLines);
  const currentDate = customDate || new Date();
  const stems = getStemBranchAccurate(currentDate);
  const dayStem = stems.day.charAt(0); // 获取日干
  const dayBranch = stems.day.charAt(1); // 获取日支

  const sixRelations = calculateSixRelations(number, generatedLines);
  const hiddenSpirits = calculateHiddenSpirits(generatedLines, sixRelations);

  // 计算变卦
  const changedHexagram = calculateChangedHexagram(generatedLines);

  // 获取卦象类型
  const binaryString = generatedLines.map(line => {
    const isYang = line === 'yang' || line === 'yang_changing';
    return isYang ? '1' : '0';
  }).join('');
  const hexagramType = getType(binaryString);

  return {
    lines: generatedLines,
    name,
    number,
    sixSpirits: calculateSixSpirits(dayStem),
    sixRelations,
    worldPosition,
    responsePosition,
    hexagramType,
    hiddenSpirits, // 伏神信息
    divinationInfo: {
      date: currentDate.toLocaleDateString('zh-CN'),
      solarTerm: getSolarTerm(currentDate),
      stems,
      voidness: getAllVoidnessAccurate(currentDate), // 获取所有空亡信息
      spirits: getSpiritsWithBranches(dayBranch)
    },
    changedHexagram: changedHexagram ? {
      ...changedHexagram,
      sixSpirits: calculateSixSpirits(dayStem)
    } : undefined
  };
};

// 获取变卦
export const getChangedHexagram = (original: Hexagram): Hexagram => {
  const changedLines = original.lines.map(line => {
    if (line === 'yang_changing') return 'yin';
    if (line === 'yin_changing') return 'yang';
    return line;
  });
  
  const { name, number } = calculateHexagram(changedLines as YaoType[]);
  
  return {
    ...original,
    lines: changedLines as YaoType[],
    name,
    number
  };
};