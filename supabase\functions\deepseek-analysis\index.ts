import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from 'jsr:@supabase/supabase-js@2';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

interface HexagramData {
  question: string;
  hexagram: {
    name: string;
    number?: number;
    lines: string[];
    sixSpirits: string[];
    sixRelations: string[];
    worldPosition: number;
    responsePosition: number;
    hexagramType?: string;
    hiddenSpirits?: Array<{
      ganZhi: string;
      wuxing: string;
      position: number;
      relation: string;
      hiddenUnder: number;
    }>;
    divinationInfo: {
      date: string;
      solarTerm: string;
      stems: {
        year: string;
        month: string;
        day: string;
        hour: string;
      };
      voidness: {
        year: string[];
        month: string[];
        day: string[];
        hour: string[];
      };
      spirits: string[];
    };
    changedHexagram?: {
      name: string;
      lines?: string[];
      sixSpirits?: string[];
      sixRelations: string[];
      worldPosition: number;
      responsePosition: number;
    };
  };
  coinResults: Array<{
    coins: number[];
    sum: number;
    lineType: string;
  }>;
  divinationTime: string;
}

Deno.serve(async (req: Request) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // 使用service role key绕过RLS，因为这是从analysis-coordinator调用的
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );

    const requestData = await req.json();
    const recordId = requestData.recordId; // 从analysis-coordinator传递的记录ID

    console.log('🔍 DeepSeek Analysis 开始处理记录:', recordId);

    // 验证记录ID
    if (!recordId) {
      throw new Error('Missing recordId - this function should be called from analysis-coordinator');
    }

    // 从数据库读取完整的记录数据
    console.log('� 从数据库读取记录数据...');
    const { data: record, error: fetchError } = await supabaseClient
      .from('divination_records')
      .select('*')
      .eq('id', recordId)
      .single();

    if (fetchError || !record) {
      console.error('❌ 读取记录失败:', fetchError);
      throw new Error(`Failed to fetch record: ${fetchError?.message || 'Record not found'}`);
    }

    console.log('✅ 成功读取记录:', record.hexagram_name);
    console.log('�📊 记录包含的数据:', {
      question: record.question,
      hexagram_name: record.hexagram_name,
      coin_results: record.coin_results,
      has_hexagram_data: !!record.hexagram_data,
      has_divination_info: !!record.hexagram_data?.divinationInfo
    });

    // 检查记录是否有完整的hexagram_data
    if (!record.hexagram_data || !record.hexagram_data.divinationInfo) {
      console.error('❌ 记录缺少hexagram_data或divinationInfo');
      throw new Error('Missing hexagram_data or divinationInfo in record');
    }

    // 构建HexagramData对象
    const hexagramData: HexagramData = {
      question: record.question,
      hexagram: record.hexagram_data,
      coinResults: record.coin_results.map((sum: number, index: number) => ({
        coins: [], // 这里可以根据sum反推coins，但对分析不重要
        sum,
        lineType: sum === 6 ? 'yin_changing' : sum === 9 ? 'yang_changing' : sum % 2 === 0 ? 'yin' : 'yang'
      })),
      divinationTime: record.divination_time
    };

    // 构建详细的卦象信息
    const hexagramInfo = buildHexagramInfo(hexagramData);

    // 调用DeepSeek API
    console.log('=== 开始调用 DeepSeek API ===');
    console.log('用户问题:', hexagramData.question);
    console.log('卦象名称:', hexagramData.hexagram.name);

    let analysis: string;
    try {
      analysis = await callDeepSeekAPI(hexagramInfo, hexagramData.question);
      console.log('✅ DeepSeek API 调用成功');
    } catch (apiError) {
      console.error('❌ DeepSeek API 调用失败:', apiError);
      throw apiError; // 重新抛出错误，停止执行
    }

    // 更新记录，添加分析结果
    console.log('💾 开始将 DeepSeek 分析结果保存到数据库...');
    console.log('记录ID:', recordId);
    console.log('分析结果长度:', analysis.length);
    console.log('分析结果预览:', analysis.substring(0, 200) + '...');

    const { data: updateData, error: updateError } = await supabaseClient
      .from('divination_records')
      .update({
        interpretation: analysis,
        analysis_status: 'completed',
        analysis_completed_at: new Date().toISOString()
      })
      .eq('id', recordId)
      .select();

    if (updateError) {
      console.error('❌ 保存 DeepSeek 分析结果失败:', updateError);
      console.error('更新错误详情:', JSON.stringify(updateError, null, 2));
      throw updateError; // 抛出错误，让analysis-coordinator知道失败了
    }

    console.log('✅ DeepSeek 分析结果已成功存入数据库');
    console.log('更新的记录ID:', recordId);
    console.log('更新后的数据:', updateData);

    return new Response(
      JSON.stringify({ analysis }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    );

  } catch (error) {
    console.error('Error in deepseek-analysis function:', error);
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      }
    );
  }
});



function buildHexagramInfo(data: HexagramData): string {
  const { hexagram } = data;

  let info = `起卦信息：\n`;
  info += `公历：${hexagram.divinationInfo.date}\n`;
  info += `农历：${hexagram.divinationInfo.stems.year.replace('年', '')}年\n`;
  info += `节气：${hexagram.divinationInfo.solarTerm}\n`;
  info += `干支：${hexagram.divinationInfo.stems.year} ${hexagram.divinationInfo.stems.month} ${hexagram.divinationInfo.stems.day} ${hexagram.divinationInfo.stems.hour}\n`;

  // 空亡信息
  info += `空亡：`;
  info += `${hexagram.divinationInfo.voidness.year.join('')} `;
  info += `${hexagram.divinationInfo.voidness.month.join('')} `;
  info += `${hexagram.divinationInfo.voidness.day.join('')} `;
  info += `${hexagram.divinationInfo.voidness.hour.join('')}\n`;

  // 神煞信息
  if (hexagram.divinationInfo.spirits && hexagram.divinationInfo.spirits.length > 0) {
    info += `神煞：${hexagram.divinationInfo.spirits.join(' ')}\n`;
  }

  info += `\n卦象信息：\n`;
  info += `本卦：${hexagram.name}`;
  if (hexagram.number) {
    info += ` (第${hexagram.number}卦)`;
  }
  if (hexagram.hexagramType) {
    info += ` [${hexagram.hexagramType}]`;
  }
  info += `\n`;

  if (hexagram.changedHexagram) {
    info += `变卦：${hexagram.changedHexagram.name}\n`;
  }

  info += `\n本卦详细信息：\n`;
  for (let i = 5; i >= 0; i--) { // 从上爻到初爻
    const yaoName = ['初', '二', '三', '四', '五', '上'][i];
    const lineType = hexagram.lines[i];
    const spirit = hexagram.sixSpirits[i];
    const relation = hexagram.sixRelations[i];

    // 判断爻的阴阳和动静
    let yaoType = '';
    if (lineType === 'yang') yaoType = '少阳';
    else if (lineType === 'yin') yaoType = '少阴';
    else if (lineType === 'yang_changing') yaoType = '老阳(动爻)';
    else if (lineType === 'yin_changing') yaoType = '老阴(动爻)';

    // 世爻应爻标记（worldPosition和responsePosition是1-6，需要转换为0-5的数组索引）
    let worldResponse = '';
    if (hexagram.worldPosition === i + 1) worldResponse = ' (世爻)';
    if (hexagram.responsePosition === i + 1) worldResponse = ' (应爻)';

    info += `${yaoName}爻：${spirit} ${relation} ${yaoType}${worldResponse}\n`;
  }

  // 伏神信息
  if (hexagram.hiddenSpirits && hexagram.hiddenSpirits.length > 0) {
    info += `\n伏神信息：\n`;
    hexagram.hiddenSpirits.forEach(hidden => {
      const yaoName = ['初', '二', '三', '四', '五', '上'][hidden.position];
      info += `${yaoName}爻伏神：${hidden.relation} ${hidden.ganZhi} (${hidden.wuxing})\n`;
    });
  }

  if (hexagram.changedHexagram) {
    info += `\n变卦详细信息：\n`;
    for (let i = 5; i >= 0; i--) {
      const yaoName = ['初', '二', '三', '四', '五', '上'][i];
      const relation = hexagram.changedHexagram.sixRelations[i];

      // 获取变卦的六神（与本卦相同）
      const spirit = hexagram.changedHexagram.sixSpirits ? hexagram.changedHexagram.sixSpirits[i] : hexagram.sixSpirits[i];

      // 获取变卦的爻性
      let yaoType = '';
      if (hexagram.changedHexagram.lines && hexagram.changedHexagram.lines[i]) {
        const lineType = hexagram.changedHexagram.lines[i];
        if (lineType === 'yang') yaoType = '少阳';
        else if (lineType === 'yin') yaoType = '少阴';
        else if (lineType === 'yang_changing') yaoType = '老阳(动爻)';
        else if (lineType === 'yin_changing') yaoType = '老阴(动爻)';
      }

      let worldResponse = '';
      if (hexagram.changedHexagram.worldPosition === i + 1) worldResponse = ' (世爻)';
      if (hexagram.changedHexagram.responsePosition === i + 1) worldResponse = ' (应爻)';

      info += `${yaoName}爻：${spirit} ${relation} ${yaoType}${worldResponse}\n`;
    }
  }

  return info;
}

async function callDeepSeekAPI(hexagramInfo: string, question: string): Promise<string> {
  const deepseekApiKey = Deno.env.get('DEEPSEEK_API_KEY');

  console.log('=== DeepSeek API 调用详情 ===');
  console.log('API Key 是否配置:', deepseekApiKey ? '是' : '否');
  console.log('API Key 前缀:', deepseekApiKey ? deepseekApiKey.substring(0, 10) + '...' : 'N/A');

  if (!deepseekApiKey) {
    console.log('❌ DeepSeek API key 未配置');
    throw new Error('DeepSeek API key not configured');
  }

  const prompt = `${hexagramInfo}

用户问题：${question}

【重要提醒】：必须严格按照以下步骤顺序进行分析，每一步都要详细完成，不可跳过任何步骤。只有完成所有步骤后才能得出最终结论。

首先列出你看到的本卦里的六个爻和变卦里的六个爻。

然后严格按照以下13个步骤进行六爻卦象解析：

【步骤1】根据用户问询的事件取用神


【步骤2】辨旺衰（用神在日上的生克扶临冲，在月上的生克扶临冲平合休囚）


【步骤3】找特殊爻（月破，日冲，旬空）


【步骤4】看生克（动爻对用神的生克冲合；动爻对世爻的生克冲合；化进，化退，化回头生，化回头克等）


【步骤5】三合局，反吟，伏吟


【步骤6】伏神


【步骤7】动爻通关


【步骤8】独发，独静


【步骤9】十二长生（墓库，绝等）


【步骤10】六合 六冲卦


【步骤11】取象意，六神，爻位，神煞等


【步骤12】根据问题来猜测用户想要问的时间信息。然后根据空亡或者别的信息来计算大致的时间（月或者日，先说申月这种地支类的标准术语，然后转换为当年的实际月份和日子）


【步骤13】综合上面所有详细解释卦象，给用户最终的可以方便理解的答案


【最终结论】：只有在完成上述所有13个步骤后，才能给出最终的占卜结果和建议。`;

  const requestBody = {
    model: 'deepseek-reasoner',
    messages: [
      {
        role: 'system',
        content: '你是一位精通六爻占卜的专业大师，具有深厚的易学功底和丰富的实践经验。请根据提供的卦象信息进行详细的六爻分析。\n\n【关键要求】：\n1. 必须严格按照13个步骤的顺序进行分析\n3. 不可跳过任何步骤，不可合并步骤\n4. 只有完成所有13个步骤后才能给出最终结论\n5. 如果某个步骤不适用，也要说明原因并标注完成'
      },
      {
        role: 'user',
        content: prompt
      }
    ],
    temperature: 0.7,
    max_tokens: 40000
  };

  console.log('📤 发送给 DeepSeek 的请求数据:');
  console.log('- 模型:', requestBody.model);
  console.log('- 温度:', requestBody.temperature);
  console.log('- 最大tokens:', requestBody.max_tokens);
  console.log('- 系统消息长度:', requestBody.messages[0].content.length);
  console.log('- 用户消息长度:', requestBody.messages[1].content.length);
  console.log('- 完整卦象信息:');
  console.log(hexagramInfo);
  console.log('- 完整prompt:');
  console.log(prompt);

  console.log('🚀 开始发送请求到 DeepSeek API...');

  const response = await fetch('https://api.deepseek.com/chat/completions', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${deepseekApiKey}`,
    },
    body: JSON.stringify(requestBody),
  });

  console.log('📥 收到 DeepSeek API 响应:');
  console.log('- 状态码:', response.status);
  console.log('- 状态文本:', response.statusText);
  console.log('- 响应头:', Object.fromEntries(response.headers.entries()));

  if (!response.ok) {
    const errorText = await response.text();
    console.log('❌ DeepSeek API 错误响应:', errorText);
    throw new Error(`DeepSeek API error: ${response.status} - ${errorText}`);
  }

  const result = await response.json();

  console.log('✅ DeepSeek API 成功响应:');
  console.log('- 响应结构:', Object.keys(result));
  console.log('- choices 数量:', result.choices?.length || 0);

  if (result.choices && result.choices[0]) {
    console.log('- 第一个choice结构:', Object.keys(result.choices[0]));
    console.log('- 消息内容长度:', result.choices[0].message?.content?.length || 0);
    console.log('- 完整响应内容:');
    console.log(result.choices[0].message?.content || 'No content');
  }

  if (result.usage) {
    console.log('- Token使用情况:', result.usage);
  }

  console.log('=== DeepSeek API 调用完成 ===');

  return result.choices[0].message.content;
}
