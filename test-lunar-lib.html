<!DOCTYPE html>
<html>
<head>
    <title>测试Lunar库</title>
    <meta charset="utf-8">
</head>
<body>
    <h1>测试Lunar-javascript库</h1>
    <div id="results"></div>

    <script type="module">
        async function testLunarLib() {
            const resultsDiv = document.getElementById('results');
            
            try {
                // 动态导入lunar-javascript
                const { Solar } = await import('https://cdn.skypack.dev/lunar-javascript');
                
                resultsDiv.innerHTML += '<p>✅ Lunar-javascript库加载成功</p>';
                
                // 测试2025年7月26日
                const testDate = new Date(2025, 6, 26, 12, 0, 0);
                resultsDiv.innerHTML += `<p>测试日期: ${testDate.toLocaleString()}</p>`;
                
                const solar = Solar.fromYmdHms(
                    testDate.getFullYear(),
                    testDate.getMonth() + 1,
                    testDate.getDate(),
                    testDate.getHours(),
                    testDate.getMinutes(),
                    testDate.getSeconds()
                );
                
                const lunar = solar.getLunar();
                const eightChar = lunar.getEightChar();
                
                const result = {
                    year: eightChar.getYear() + '年',
                    month: eightChar.getMonth() + '月',
                    day: eightChar.getDay() + '日',
                    hour: eightChar.getTime() + '时'
                };
                
                resultsDiv.innerHTML += `<p><strong>计算结果:</strong> ${result.year} ${result.month} ${result.day} ${result.hour}</p>`;
                resultsDiv.innerHTML += `<p><strong>预期结果:</strong> 乙巳年 癸未月 丙申日 丙午时</p>`;
                
                // 测试晚子时
                const nightDate = new Date(2025, 6, 23, 23, 0, 0);
                resultsDiv.innerHTML += `<hr><p>测试晚子时: ${nightDate.toLocaleString()}</p>`;
                
                const nightSolar = Solar.fromYmdHms(
                    nightDate.getFullYear(),
                    nightDate.getMonth() + 1,
                    nightDate.getDate(),
                    nightDate.getHours(),
                    nightDate.getMinutes(),
                    nightDate.getSeconds()
                );
                
                const nightLunar = nightSolar.getLunar();
                const nightEightChar = nightLunar.getEightChar();
                
                const nightResult = {
                    year: nightEightChar.getYear() + '年',
                    month: nightEightChar.getMonth() + '月',
                    day: nightEightChar.getDay() + '日',
                    hour: nightEightChar.getTime() + '时'
                };
                
                resultsDiv.innerHTML += `<p><strong>23时计算结果:</strong> ${nightResult.year} ${nightResult.month} ${nightResult.day} ${nightResult.hour}</p>`;
                
                // 测试修正后的晚子时（算作下一天）
                const adjustedNightDate = new Date(nightDate);
                adjustedNightDate.setDate(adjustedNightDate.getDate() + 1);
                adjustedNightDate.setHours(0, 0, 0, 0);
                
                const adjustedSolar = Solar.fromYmdHms(
                    adjustedNightDate.getFullYear(),
                    adjustedNightDate.getMonth() + 1,
                    adjustedNightDate.getDate(),
                    adjustedNightDate.getHours(),
                    adjustedNightDate.getMinutes(),
                    adjustedNightDate.getSeconds()
                );
                
                const adjustedLunar = adjustedSolar.getLunar();
                const adjustedEightChar = adjustedLunar.getEightChar();
                
                const adjustedResult = {
                    year: adjustedEightChar.getYear() + '年',
                    month: adjustedEightChar.getMonth() + '月',
                    day: adjustedEightChar.getDay() + '日',
                    hour: adjustedEightChar.getTime() + '时'
                };
                
                resultsDiv.innerHTML += `<p><strong>修正后结果:</strong> ${adjustedResult.year} ${adjustedResult.month} ${adjustedResult.day} ${adjustedResult.hour}</p>`;
                resultsDiv.innerHTML += `<p><strong>预期结果:</strong> 应该是甲午日（7月24日的日干支）</p>`;
                
            } catch (error) {
                resultsDiv.innerHTML += `<p>❌ 错误: ${error.message}</p>`;
                console.error('测试失败:', error);
            }
        }
        
        testLunarLib();
    </script>
</body>
</html>
