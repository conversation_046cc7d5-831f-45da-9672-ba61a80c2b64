import { create } from 'zustand'
import { User } from '@supabase/supabase-js'
import { supabase } from '../lib/supabase'

interface AuthState {
  user: User | null
  loading: boolean
  initialized: boolean
  signIn: (email: string, password: string) => Promise<void>
  signUp: (email: string, password: string) => Promise<void>
  signInWithGoogle: () => Promise<void>
  signOut: () => Promise<void>
  initialize: () => Promise<void>
}

export const useAuthStore = create<AuthState>((set, get) => ({
  user: null,
  loading: true,
  initialized: false,

  signIn: async (email: string, password: string) => {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    })
    if (error) throw error

    // 不需要手动setSession，Supabase会自动管理
    set({ user: data.user })
  },

  signUp: async (email: string, password: string) => {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
    })
    if (error) throw error
    set({ user: data.user })
  },

  signInWithGoogle: async () => {
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: `${window.location.origin}/`
      }
    })
    if (error) throw error
  },

  signOut: async () => {
    try {
      console.log('Starting sign out process...')

      // Always clear local state first
      set({ user: null })

      // Clear localStorage - 使用正确的key格式
      Object.keys(localStorage).forEach(key => {
        if (key.startsWith('sb-') && key.includes('auth-token')) {
          localStorage.removeItem(key)
        }
      })

      // Try to sign out from Supabase
      const { error } = await supabase.auth.signOut()
      if (error) {
        console.warn('Supabase signOut error (but local state cleared):', error.message)
        // Don't throw error since we've already cleared local state
      } else {
        console.log('Successfully signed out from Supabase')
      }

    } catch (error) {
      console.error('Sign out error:', error)
      // Ensure local state is cleared even if there's an error
      set({ user: null })
    }
  },

  initialize: async () => {
    const { initialized } = get()
    if (initialized) {
      console.log('Auth already initialized, skipping...')
      return
    }

    try {
      const { data: { session } } = await supabase.auth.getSession()
      set({ user: session?.user ?? null, loading: false, initialized: true })

      // 只注册一次认证状态监听器
      supabase.auth.onAuthStateChange((event, session) => {
        console.log('Auth state change:', event, session?.user?.email || 'No session')

        // 只更新用户状态，不重复设置initialized
        set({ user: session?.user ?? null, loading: false })

        // 只在登录成功后创建用户档案
        if (event === 'SIGNED_IN' && session?.user) {
          setTimeout(async () => {
            try {
              const { error: profileError } = await supabase
                .from('user_profiles')
                .upsert({
                  id: session.user.id,
                  email: session.user.email,
                  updated_at: new Date().toISOString(),
                })

              if (profileError) {
                console.error('Error creating/updating user profile:', profileError)
              }
            } catch (error) {
              console.error('Error in profile creation process:', error)
            }
          }, 100)
        }
      })
    } catch (error) {
      console.error('Auth initialization error:', error)
      set({ loading: false, initialized: true })
    }
  },
}))
