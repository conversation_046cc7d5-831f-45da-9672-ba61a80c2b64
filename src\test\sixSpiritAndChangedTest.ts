// 六神和变卦测试
import {
  calculateSixSpirits,
  calculateChangedHexagram,
  calculateSixRelations,
  seekForOrigin
} from '../utils/hexagramCalculator';
import { YaoType } from '../components/YaoLine';

console.log('=== 六神和变卦测试 ===');

// 雷地豫卦，第三爻动
const leiDiYuLines: YaoType[] = ['yin', 'yin', 'yin_changing', 'yang', 'yin', 'yin'];

console.log('1. 雷地豫卦爻线:', leiDiYuLines);

// 测试六神计算（戊日）
console.log('\n=== 六神测试 ===');
const sixSpiritsJia = calculateSixSpirits('甲');
console.log('甲日六神:', sixSpiritsJia);

const sixSpiritsWu = calculateSixSpirits('戊');
console.log('戊日六神:', sixSpiritsWu);

// 按传统口诀：甲乙起青龙，丙丁起朱雀，戊起勾陈，己起螣蛇，庚辛起白虎，壬癸起玄武
console.log('期望戊日六神: [勾陈, 螣蛇, 白虎, 玄武, 青龙, 朱雀]');

// 测试变卦计算
console.log('\n=== 变卦测试 ===');
const changedHexagram = calculateChangedHexagram(leiDiYuLines);
if (changedHexagram) {
  console.log('变卦名称:', changedHexagram.name);
  console.log('变卦六亲:', changedHexagram.sixRelations);
  
  // 手动验证变卦的卦宫
  const changedOrigin = seekForOrigin(changedHexagram.lines);
  console.log('变卦卦宫:', changedOrigin);
  
  // 变卦应该是泽水困，应该属于兑宫
  console.log('期望变卦: 泽水困，属于兑宫');
} else {
  console.log('没有变卦');
}

// 测试本卦六亲
console.log('\n=== 本卦六亲测试 ===');
const benguaSixRelations = calculateSixRelations(16, leiDiYuLines);
console.log('本卦六亲:', benguaSixRelations);
