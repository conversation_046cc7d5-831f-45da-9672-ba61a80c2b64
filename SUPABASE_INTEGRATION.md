# Supabase 集成说明

## 已实现的功能

### 1. 用户认证系统
- ✅ 邮箱密码登录/注册
- ✅ Google OAuth 登录（需要在 Supabase 控制台配置）
- ✅ 用户状态管理
- ✅ 自动创建用户档案

### 2. 摇卦记录保存
- ✅ 自动保存摇卦结果到数据库
- ✅ 记录包含：
  - 卦象名称和编号
  - 原卦和变卦
  - 摇卦过程（硬币结果）
  - 摇卦时间
  - 用户问题
  - AI 分析结果

### 3. 个人主页
- ✅ 查看所有历史摇卦记录
- ✅ 摇卦统计信息
- ✅ 记录详情查看
- ✅ 编辑问题和解读
- ✅ 删除记录功能

## 数据库结构

### user_profiles 表
```sql
- id (UUID, 主键, 关联 auth.users)
- email (TEXT)
- username (TEXT)
- created_at (TIMESTAMPTZ)
- updated_at (TIMESTAMPTZ)
```

### divination_records 表
```sql
- id (UUID, 主键)
- user_id (UUID, 外键关联 auth.users)
- hexagram_name (TEXT, 卦象名称)
- hexagram_number (INTEGER, 卦象编号)
- original_hexagram (TEXT, 原卦)
- changed_hexagram (TEXT, 变卦)
- coin_results (INTEGER[], 摇卦结果数组)
- divination_time (TIMESTAMPTZ, 摇卦时间)
- question (TEXT, 用户问题)
- interpretation (TEXT, 解读)
- created_at (TIMESTAMPTZ)
- updated_at (TIMESTAMPTZ)
```

## 安全设置

### Row Level Security (RLS)
- ✅ 所有表都启用了 RLS
- ✅ 用户只能访问自己的数据
- ✅ 防止数据泄露和未授权访问

## 使用方法

### 1. 用户登录
1. 点击右上角"登录"按钮
2. 选择邮箱注册/登录或 Google 登录
3. 登录成功后可以保存摇卦记录

### 2. 保存摇卦记录
1. 在首页输入问题并摇卦
2. 生成卦象后，点击"保存摇卦记录"按钮
3. 记录会自动保存到数据库

### 3. 查看历史记录
1. 登录后点击右上角用户头像
2. 选择"个人主页"
3. 查看所有历史摇卦记录
4. 可以编辑、删除记录

## 配置说明

### Supabase 项目信息
- 项目 URL: https://tpdslcjznbffqnpbuiiy.supabase.co
- 匿名密钥已配置在 `src/lib/supabase.ts`

### Google OAuth 配置（可选）
如需启用 Google 登录，请在 Supabase 控制台配置：
1. 进入 Authentication > Providers
2. 启用 Google 提供商
3. 配置 Google OAuth 客户端 ID 和密钥

## 技术栈

- **前端**: React + TypeScript + Vite
- **UI 组件**: Radix UI + Tailwind CSS
- **状态管理**: Zustand
- **数据库**: Supabase (PostgreSQL)
- **认证**: Supabase Auth
- **实时功能**: Supabase Realtime（可扩展）

## 下一步扩展

可以考虑添加的功能：
- [ ] 摇卦记录分享功能
- [ ] 摇卦记录导出（PDF/图片）
- [ ] 摇卦记录标签和分类
- [ ] 摇卦记录搜索功能
- [ ] 用户头像上传
- [ ] 摇卦记录统计图表
- [ ] 实时通知功能
