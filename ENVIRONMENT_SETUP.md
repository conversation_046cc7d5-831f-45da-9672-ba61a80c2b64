# 环境变量配置指南

## 概述

本项目需要配置前端和后端（Supabase Edge Functions）的环境变量。

## 1. 前端环境变量

在项目根目录创建 `.env.local` 文件：

```bash
# Supabase配置
VITE_SUPABASE_URL=https://your-project-ref.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key-here

# Stripe配置（前端）
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_your-publishable-key-here
```

## 2. Supabase Edge Functions环境变量

在Supabase Dashboard中配置以下环境变量：

```bash
# Supabase配置
SUPABASE_URL=https://your-project-ref.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here

# Stripe配置
STRIPE_SECRET_KEY=sk_test_your-secret-key-here
STRIPE_WEBHOOK_SECRET=whsec_your-webhook-secret-here

# DeepSeek API（可选）
DEEPSEEK_API_KEY=your-deepseek-api-key-here
```

## 3. 如何获取这些密钥

### Supabase密钥

1. 登录 [Supabase Dashboard](https://supabase.com/dashboard)
2. 选择你的项目
3. 进入 **Settings** > **API**
4. 复制以下信息：
   - **Project URL** → `VITE_SUPABASE_URL`
   - **anon/public key** → `VITE_SUPABASE_ANON_KEY`
   - **service_role key** → `SUPABASE_SERVICE_ROLE_KEY`

### Stripe密钥

1. 登录 [Stripe Dashboard](https://dashboard.stripe.com/)
2. 进入 **Developers** > **API keys**
3. 复制以下信息：
   - **Publishable key** → `VITE_STRIPE_PUBLISHABLE_KEY`
   - **Secret key** → `STRIPE_SECRET_KEY`

### Stripe Webhook Secret

1. 在Stripe Dashboard中创建webhook
2. 复制webhook的signing secret → `STRIPE_WEBHOOK_SECRET`

## 4. 配置步骤

### 步骤1: 配置前端环境变量

1. 在项目根目录创建 `.env.local` 文件
2. 添加上述前端环境变量
3. 重启开发服务器

### 步骤2: 配置Supabase环境变量

1. 登录Supabase Dashboard
2. 进入 **Settings** > **Edge Functions**
3. 在 **Environment variables** 部分添加上述后端环境变量
4. 点击 **Save**

### 步骤3: 验证配置

1. 检查前端是否能正常连接Supabase
2. 测试Edge Functions是否正常工作
3. 验证Stripe webhook是否能正确接收事件

## 5. 安全注意事项

### 前端环境变量（.env.local）
- 以 `VITE_` 开头的变量会在构建时嵌入到前端代码中
- 只包含公开的密钥（如publishable keys）
- 不要包含敏感信息

### 后端环境变量（Supabase）
- 包含敏感信息（如secret keys）
- 只在服务器端使用
- 不会暴露给前端

## 6. 常见问题

### 问题1: 环境变量不生效
- 确保重启了开发服务器
- 检查变量名是否正确
- 确认文件路径正确

### 问题2: Supabase连接失败
- 检查URL和密钥是否正确
- 确认项目状态是否正常
- 检查网络连接

### 问题3: Stripe webhook失败
- 确认webhook secret正确
- 检查webhook URL是否正确
- 验证事件类型是否配置正确

## 7. 开发和生产环境

### 开发环境
- 使用测试密钥（以 `test_` 开头）
- 使用本地开发URL

### 生产环境
- 使用生产密钥（以 `live_` 开头）
- 使用生产URL
- 确保所有环境变量都已正确配置

## 8. 验证清单

- [ ] 前端环境变量已配置
- [ ] Supabase环境变量已配置
- [ ] Stripe密钥已获取并配置
- [ ] Webhook已创建并配置
- [ ] 所有Edge Functions都能正常工作
- [ ] 支付流程测试通过 