import React, { useEffect, useState } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { CheckCircle, Loader2, XCircle, Home } from 'lucide-react';

export const PaymentSuccess: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [verificationStatus, setVerificationStatus] = useState<'loading' | 'success' | 'failed'>('loading');
  const [creditsAdded, setCreditsAdded] = useState(0);

  const sessionId = searchParams.get('session_id');

  useEffect(() => {
    const verifyPayment = async () => {
      if (!sessionId) {
        setVerificationStatus('failed');
        toast({
          title: "验证失败",
          description: "缺少支付会话ID",
          variant: "destructive",
        });
        return;
      }

      try {
        // First, try to get the order status from our database
        const { data: orderData, error: orderError } = await supabase
          .from('orders')
          .select('status, credits_purchased')
          .eq('stripe_session_id', sessionId)
          .single();

        if (!orderError && orderData && orderData.status === 'paid') {
          // Payment was already processed by webhook
          setVerificationStatus('success');
          setCreditsAdded(orderData.credits_purchased || 0);
          toast({
            title: "支付成功",
            description: `已为您添加 ${orderData.credits_purchased} 次分析机会`,
          });
          return;
        }

        // Fallback to client-side verification if webhook hasn't processed yet
        console.log("Webhook not processed yet, using client verification...");
        const { data, error } = await supabase.functions.invoke('verify-payment', {
          body: { sessionId }
        });

        if (error) {
          console.error("Payment verification error:", error);
          throw new Error(error.message);
        }

        if (data.success && data.payment_status === 'paid') {
          setVerificationStatus('success');
          setCreditsAdded(data.credits_added || 0);
          toast({
            title: "支付成功",
            description: `已为您添加 ${data.credits_added} 次分析机会`,
          });
        } else {
          setVerificationStatus('failed');
          toast({
            title: "支付未完成",
            description: "您的支付可能未成功完成",
            variant: "destructive",
          });
        }
      } catch (error) {
        console.error("Error verifying payment:", error);
        setVerificationStatus('failed');
        toast({
          title: "验证错误",
          description: error instanceof Error ? error.message : "支付验证失败",
          variant: "destructive",
        });
      }
    };

    verifyPayment();
  }, [sessionId, toast]);

  const handleGoHome = () => {
    navigate('/');
  };

  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4">
            {verificationStatus === 'loading' && (
              <Loader2 className="h-16 w-16 animate-spin text-muted-foreground" />
            )}
            {verificationStatus === 'success' && (
              <CheckCircle className="h-16 w-16 text-green-500" />
            )}
            {verificationStatus === 'failed' && (
              <XCircle className="h-16 w-16 text-red-500" />
            )}
          </div>
          
          <CardTitle className="text-xl">
            {verificationStatus === 'loading' && "验证支付状态..."}
            {verificationStatus === 'success' && "支付成功！"}
            {verificationStatus === 'failed' && "支付失败"}
          </CardTitle>
        </CardHeader>
        
        <CardContent className="text-center space-y-4">
          {verificationStatus === 'loading' && (
            <p className="text-muted-foreground">
              正在验证您的支付状态，请稍候...
            </p>
          )}
          
          {verificationStatus === 'success' && (
            <>
              <p className="text-muted-foreground">
                感谢您的购买！已为您添加 <strong>{creditsAdded}</strong> 次分析机会。
              </p>
              <p className="text-sm text-muted-foreground">
                您现在可以使用专业的六爻占卜分析功能了。
              </p>
            </>
          )}
          
          {verificationStatus === 'failed' && (
            <p className="text-muted-foreground">
              支付可能未成功完成，如有疑问请联系客服。
            </p>
          )}
          
          <Button 
            onClick={handleGoHome} 
            className="w-full mt-6"
            disabled={verificationStatus === 'loading'}
          >
            <Home className="mr-2 h-4 w-4" />
            返回首页
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};