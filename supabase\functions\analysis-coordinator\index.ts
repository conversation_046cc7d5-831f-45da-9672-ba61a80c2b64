import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from 'jsr:@supabase/supabase-js@2';

interface HexagramData {
  question: string;
  hexagram: {
    name: string;
    number?: number;
    lines: string[];
    sixSpirits: string[];
    sixRelations: string[];
    worldPosition: number;
    responsePosition: number;
    hexagramType?: string;
    hiddenSpirits?: Array<{
      ganZhi: string;
      wuxing: string;
      position: number;
      relation: string;
      hiddenUnder: number;
    }>;
    divinationInfo: {
      date: string;
      solarTerm: string;
      stems: {
        year: string;
        month: string;
        day: string;
        hour: string;
      };
      voidness: {
        year: string[];
        month: string[];
        day: string[];
        hour: string[];
      };
      spirits: string[];
    };
    changedHexagram?: {
      name: string;
      lines?: string[];
      sixRelations: string[];
      worldPosition: number;
      responsePosition: number;
    };
  };
  coinResults: Array<{
    coins: number[];
    sum: number;
    lineType: string;
  }>;
  divinationTime: string;
}

interface DivinationRecord {
  id: string;
  user_id: string;
  question: string;
  hexagram_data: HexagramData['hexagram'];
  coin_results: number[];
  divination_time: string;
  analysis_status: string;
}

const MAX_CONCURRENT_ANALYSIS = 10;

Deno.serve(async (req: Request) => {
  try {
    console.log('🚀 Analysis Coordinator started');

    // 创建Supabase客户端，使用service role key绕过RLS
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );

    // 查询pending状态的记录
    console.log('📋 查询pending状态的记录...');
    const { data: pendingRecords, error: queryError } = await supabaseClient
      .from('divination_records')
      .select('*')
      .eq('analysis_status', 'pending')
      .order('created_at', { ascending: true })
      .limit(MAX_CONCURRENT_ANALYSIS);

    if (queryError) {
      console.error('❌ 查询pending记录失败:', queryError);
      throw queryError;
    }

    if (!pendingRecords || pendingRecords.length === 0) {
      console.log('✅ 没有pending的记录需要处理');
      return new Response(
        JSON.stringify({ message: 'No pending records to process', processed: 0 }),
        { headers: { 'Content-Type': 'application/json' } }
      );
    }

    console.log(`📊 找到 ${pendingRecords.length} 条pending记录`);

    // 批量更新状态为analyzing
    const recordIds = pendingRecords.map(record => record.id);
    const { error: updateError } = await supabaseClient
      .from('divination_records')
      .update({
        analysis_status: 'analyzing',
        analysis_started_at: new Date().toISOString()
      })
      .in('id', recordIds);

    if (updateError) {
      console.error('❌ 更新记录状态失败:', updateError);
      throw updateError;
    }

    console.log('✅ 已将记录状态更新为analyzing');

    // 并行处理所有记录，调用deepseek-analysis函数
    const analysisPromises = pendingRecords.map(async (record: DivinationRecord) => {
      try {
        console.log(`🔍 开始分析记录 ${record.id}`);

        // 检查记录是否有完整的hexagram_data
        if (!record.hexagram_data || !record.hexagram_data.divinationInfo) {
          console.error(`❌ 记录 ${record.id} 缺少hexagram_data，跳过分析`);
          throw new Error('Missing hexagram_data - this record was created before the new system');
        }

        // 调用Zeabur上的分析服务，替代原来的deepseek-analysis Edge Function
        console.log(`📞 调用 Zeabur 分析服务处理记录 ${record.id}`);

        const response = await fetch('https://liuyao.zeabur.app/api/analysis', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            recordId: record.id // 只传递记录ID，让Zeabur服务从数据库读取完整数据
          })
        });

        if (!response.ok) {
          const errorText = await response.text();
          console.error(`❌ Zeabur 分析服务调用失败:`, response.status, errorText);
          throw new Error(`Zeabur analysis service error: ${response.status} - ${errorText}`);
        }

        const result = await response.json();
        console.log(`✅ 记录 ${record.id} 分析完成`);
        return { id: record.id, status: 'completed', analysis: result.analysis };

      } catch (error) {
        console.error(`❌ 分析记录 ${record.id} 失败:`, error);

        // 更新记录为failed
        await supabaseClient
          .from('divination_records')
          .update({
            analysis_status: 'failed',
            analysis_completed_at: new Date().toISOString()
          })
          .eq('id', record.id);

        return { id: record.id, status: 'failed', error: error.message };
      }
    });

    // 等待所有分析完成
    const results = await Promise.all(analysisPromises);
    
    const completed = results.filter(r => r.status === 'completed').length;
    const failed = results.filter(r => r.status === 'failed').length;
    
    console.log(`🎉 分析完成: ${completed} 成功, ${failed} 失败`);

    return new Response(
      JSON.stringify({
        message: 'Analysis batch completed',
        processed: pendingRecords.length,
        completed,
        failed,
        results
      }),
      { headers: { 'Content-Type': 'application/json' } }
    );

  } catch (error) {
    console.error('❌ Analysis Coordinator error:', error);
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    );
  }
});


