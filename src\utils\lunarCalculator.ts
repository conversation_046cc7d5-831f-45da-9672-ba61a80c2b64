import { xkong } from './hexagramCalculator';

interface StemBranchInfo {
  year: string;
  month: string;
  day: string;
  hour: string;
}

interface VoidnessInfo {
  year: string[];
  month: string[];
  day: string[];
  hour: string[];
}

class LunarCalculator {
  private lunarLib: any = null;
  private isLoaded = false;
  private isLoading = false;

  constructor() {
    // 自动开始加载
    this.loadLunarLibrary();
  }

  private async loadLunarLibrary() {
    if (this.isLoading || this.isLoaded) return;
    
    this.isLoading = true;
    try {
      const lunarModule = await import('lunar-javascript');
      this.lunarLib = lunarModule;
      this.isLoaded = true;
      console.log('Lunar-javascript library loaded successfully');
    } catch (error) {
      console.error('Failed to load lunar-javascript library:', error);
    } finally {
      this.isLoading = false;
    }
  }

  public getStemBranch(date: Date): StemBranchInfo {
    if (this.isLoaded && this.lunarLib) {
      try {
        const { Solar } = this.lunarLib;

        // 处理晚子时：如果是23时，算作下一天的日干支
        const adjustedDate = new Date(date);
        if (date.getHours() === 23) {
          adjustedDate.setDate(adjustedDate.getDate() + 1);
          adjustedDate.setHours(0, 0, 0, 0);
        }

        const solar = Solar.fromYmdHms(
          adjustedDate.getFullYear(),
          adjustedDate.getMonth() + 1,
          adjustedDate.getDate(),
          adjustedDate.getHours(),
          adjustedDate.getMinutes(),
          adjustedDate.getSeconds()
        );

        const lunar = solar.getLunar();
        const eightChar = lunar.getEightChar();

        return {
          year: eightChar.getYear() + '年',
          month: eightChar.getMonth() + '月',
          day: eightChar.getDay() + '日',
          hour: eightChar.getTime() + '时'
        };
      } catch (error) {
        console.error('Error using lunar-javascript, falling back:', error);
      }
    }

    // 降级到简化计算
    return {
      year: '未知年',
      month: '未知月',
      day: '未知日',
      hour: '未知时'
    };
  }

  public getAllVoidness(date: Date): VoidnessInfo {
    if (this.isLoaded && this.lunarLib) {
      try {
        const { Solar } = this.lunarLib;

        // 处理晚子时：如果是23时，算作下一天
        const adjustedDate = new Date(date);
        if (date.getHours() === 23) {
          adjustedDate.setDate(adjustedDate.getDate() + 1);
          adjustedDate.setHours(0, 0, 0, 0);
        }

        const solar = Solar.fromYmdHms(
          adjustedDate.getFullYear(),
          adjustedDate.getMonth() + 1,
          adjustedDate.getDate(),
          adjustedDate.getHours(),
          adjustedDate.getMinutes(),
          adjustedDate.getSeconds()
        );

        const lunar = solar.getLunar();
        const eightChar = lunar.getEightChar();

        return {
          year: eightChar.getYearXunKong().split(''),
          month: eightChar.getMonthXunKong().split(''),
          day: eightChar.getDayXunKong().split(''),
          hour: eightChar.getTimeXunKong().split('')
        };
      } catch (error) {
        console.error('Error using lunar-javascript for voidness, falling back:', error);
      }
    }

    // 降级到简化计算
    const stemBranch = this.getStemBranch(date);
    const dayGanZhi = stemBranch.day.replace('日', '');
    const voidness = xkong(dayGanZhi);
    const voidnessArray = voidness.split('');

    return {
      year: voidnessArray,
      month: voidnessArray,
      day: voidnessArray,
      hour: voidnessArray
    };
  }

  public getVoidness(date: Date): string[] {
    const allVoidness = this.getAllVoidness(date);
    return allVoidness.day;
  }

  public isLibraryLoaded(): boolean {
    return this.isLoaded;
  }

  public isLibraryLoading(): boolean {
    return this.isLoading;
  }
}

// 创建全局实例
export const lunarCalculator = new LunarCalculator();

// 导出便捷函数
export const getStemBranchAccurate = (date: Date): StemBranchInfo => {
  return lunarCalculator.getStemBranch(date);
};

export const getAllVoidnessAccurate = (date: Date): VoidnessInfo => {
  return lunarCalculator.getAllVoidness(date);
};

export const getVoidnessAccurate = (date: Date): string[] => {
  return lunarCalculator.getVoidness(date);
};
