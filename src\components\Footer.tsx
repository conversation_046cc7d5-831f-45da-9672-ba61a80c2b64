import React from 'react';
import { Separator } from '@/components/ui/separator';
import { useTranslation } from '@/hooks/useTranslation';

export const Footer: React.FC = () => {
  const { t } = useTranslation();
  return (
    <footer className="mt-20 border-t border-border/50 bg-card/20 backdrop-blur-sm">
      <div className="max-w-7xl mx-auto px-6 py-12">
        <div className="grid md:grid-cols-4 gap-8">
          <div className="space-y-4">
            <h3 className="font-bold text-lg gradient-text">{t('appName')}</h3>
            <p className="text-sm text-muted-foreground leading-relaxed">
              {t('tagline')}
            </p>
          </div>
          
          <div className="space-y-4">
            <h4 className="font-semibold">{t('coreFeatures')}</h4>
            <ul className="space-y-2 text-sm text-muted-foreground">
              <li>{t('hexagramPlanning')}</li>
              <li>{t('aiIntelligentAnalysis')}</li>
              <li>{t('historyRecords')}</li>
              <li>{t('shareCollection')}</li>
            </ul>
          </div>
          
          <div className="space-y-4">
            <h4 className="font-semibold">{t('learningResources')}</h4>
            <ul className="space-y-2 text-sm text-muted-foreground">
              <li>{t('hexagramBasics')}</li>
              <li>{t('hexagramInterpretation')}</li>
              <li>{t('caseAnalysis')}</li>
              <li>{t('frequentQuestions')}</li>
            </ul>
          </div>
          
          <div className="space-y-4">
            <h4 className="font-semibold">{t('contactUs')}</h4>
            <ul className="space-y-2 text-sm text-muted-foreground">
              <li>{t('customerSupport')}</li>
              <li>{t('feedbackSuggestions')}</li>
              <li>{t('businessCooperation')}</li>
              <li>{t('privacyPolicy')}</li>
            </ul>
          </div>
        </div>
        
        <Separator className="my-8 bg-border/50" />
        
        <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
          <p className="text-sm text-muted-foreground">
            {t('copyright')}
          </p>
          <p className="text-sm text-muted-foreground">
            {t('tagline')}
          </p>
        </div>
      </div>
    </footer>
  );
};