import { getStemBranchAccurate, getAllVoidnessAccurate, lunarCalculator } from '../utils/lunarCalculator';

console.log('=== Lunar Calculator 测试 ===');

const testDate = new Date();
console.log('测试时间:', testDate.toLocaleString());

// 立即测试（可能使用降级方案）
console.log('\n1. 立即测试（可能是降级方案）:');
console.log('库加载状态:', lunarCalculator.isLibraryLoaded());
console.log('库加载中:', lunarCalculator.isLibraryLoading());

const immediateStemBranch = getStemBranchAccurate(testDate);
console.log('干支计算:', immediateStemBranch);

const immediateVoidness = getAllVoidnessAccurate(testDate);
console.log('空亡计算:', immediateVoidness);

// 等待库加载后再次测试
setTimeout(() => {
  console.log('\n2. 延迟测试（应该使用准确计算）:');
  console.log('库加载状态:', lunarCalculator.isLibraryLoaded());
  console.log('库加载中:', lunarCalculator.isLibraryLoading());

  const delayedStemBranch = getStemBranchAccurate(testDate);
  console.log('干支计算:', delayedStemBranch);

  const delayedVoidness = getAllVoidnessAccurate(testDate);
  console.log('空亡计算:', delayedVoidness);

  // 比较结果
  console.log('\n3. 结果比较:');
  const stemBranchChanged = JSON.stringify(immediateStemBranch) !== JSON.stringify(delayedStemBranch);
  const voidnessChanged = JSON.stringify(immediateVoidness) !== JSON.stringify(delayedVoidness);
  
  console.log('干支结果是否改变:', stemBranchChanged);
  console.log('空亡结果是否改变:', voidnessChanged);
  
  if (stemBranchChanged || voidnessChanged) {
    console.log('✅ 成功使用了更准确的lunar-javascript计算');
  } else {
    console.log('⚠️ 仍在使用降级计算，可能lunar-javascript加载失败');
  }
}, 2000);
