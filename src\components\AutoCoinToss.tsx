import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { useTranslation } from '@/hooks/useTranslation';
import { YaoType } from './YaoLine';

interface CoinResult {
  coins: number[];
  sum: number;
  lineType: YaoType;
}

interface AutoCoinTossProps {
  onComplete: (lines: YaoType[], results: CoinResult[]) => void;
  onCancel: () => void;
}

export const AutoCoinToss: React.FC<AutoCoinTossProps> = ({ onComplete, onCancel }) => {
  const { t } = useTranslation();
  const [currentToss, setCurrentToss] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);
  const [results, setResults] = useState<CoinResult[]>([]);
  const [currentCoins, setCurrentCoins] = useState([2, 2, 2]); // 初始显示背面

  const getYaoType = (sum: number): YaoType => {
    switch (sum) {
      case 6: return 'yin_changing';
      case 7: return 'yang';
      case 8: return 'yin';
      case 9: return 'yang_changing';
      default: return 'yang';
    }
  };

  const handleToss = () => {
    if (currentToss >= 6) return;
    
    setIsAnimating(true);
    
    // 动画效果：硬币随机翻转
    const animationInterval = setInterval(() => {
      setCurrentCoins([
        Math.random() > 0.5 ? 3 : 2,
        Math.random() > 0.5 ? 3 : 2,
        Math.random() > 0.5 ? 3 : 2
      ]);
    }, 60);

    // 0.3秒后停止动画并显示最终结果
    setTimeout(() => {
      clearInterval(animationInterval);

      // 生成最终结果
      const coins = Array.from({ length: 3 }, () => Math.random() > 0.5 ? 3 : 2);
      const sum = coins.reduce((acc, coin) => acc + coin, 0);
      const lineType = getYaoType(sum);

      const newResult: CoinResult = { coins, sum, lineType };
      const newResults = [...results, newResult];

      setCurrentCoins(coins);
      setResults(newResults);
      setCurrentToss(currentToss + 1);
      setIsAnimating(false);

      // 如果完成了6次摇卦，自动提交结果
      if (newResults.length === 6) {
        setTimeout(() => {
          const lines = newResults.map(r => r.lineType);
          onComplete(lines, newResults);
        }, 100);
      }
    }, 300);
  };

  const coinTextCount = (coins: number[]) => coins.filter(c => c === 3).length;
  const coinBackCount = (coins: number[]) => coins.filter(c => c === 2).length;

  return (
    <div className="space-y-6">
      {/* 硬币显示区 */}
      <div className="text-center space-y-4">
        <div className="flex justify-center gap-4">
          {currentCoins.map((coin, index) => (
            <div
              key={index}
              className={`w-16 h-16 rounded-full border-4 border-divination-primary flex items-center justify-center text-2xl font-bold transition-all duration-300 ${
                isAnimating ? 'animate-spin' : ''
              } ${
                coin === 3 
                  ? 'bg-divination-primary text-background' 
                  : 'bg-background text-divination-primary'
              }`}
            >
              {coin === 3 ? t('heads') : t('tails')}
            </div>
          ))}
        </div>
        
        <div className="text-lg">
          {currentToss >= 6 ? t('tossCompleted') : `${currentToss + 1}${t('tossRound')}`}
        </div>
        
        {results.length > 0 && !isAnimating && (
          <div className="text-sm text-muted-foreground">
            {coinTextCount(currentCoins)}{t('heads')}{coinBackCount(currentCoins)}{t('tails')} - {currentCoins.reduce((a, b) => a + b, 0)}
          </div>
        )}
      </div>

      {/* 摇卦按钮 */}
      <div className="text-center">
        <Button
          onClick={handleToss}
          disabled={isAnimating || currentToss >= 6}
          variant="mystical"
          size="lg"
          className="px-8"
        >
          {isAnimating ? t('tossInProgress') : currentToss >= 6 ? t('tossCompleted') : t('tossAction')}
        </Button>
      </div>

      {/* 历史结果 */}
      {results.length > 0 && (
        <div className="space-y-3">
          <h4 className="text-sm font-medium text-center">{t('tossHistory')}</h4>
          <div className="grid grid-cols-6 gap-2 text-center">
            {results.map((result, index) => (
              <div key={index} className="space-y-1 p-2 bg-background/20 rounded text-xs">
                <div className="text-muted-foreground">{index + 1}{t('yaoNumber')}</div>
                <div className="text-divination-primary">
                  {coinTextCount(result.coins)}{t('heads')}{coinBackCount(result.coins)}{t('tails')}
                </div>
                <div className="text-xs">
                  {result.sum === 6 ? t('oldYin') :
                   result.sum === 9 ? t('oldYang') :
                   result.sum === 7 ? t('youngYang') : t('youngYin')}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 取消按钮 */}
      <div className="text-center">
        <Button
          onClick={onCancel}
          variant="outline"
          disabled={isAnimating}
        >
          {t('cancel')}
        </Button>
      </div>
    </div>
  );
};