/**
 * 手动摇卦组件测试
 * 验证摇卦历史记录功能是否正常工作
 */

import { YaoType } from '@/components/YaoLine';

interface CoinResult {
  coins: number[];
  sum: number;
  lineType: YaoType;
}

// 模拟手动摇卦的结果处理逻辑
export const simulateManualCoinInput = (coinInputs: number[][]): CoinResult[] => {
  const results: CoinResult[] = [];
  
  for (const coins of coinInputs) {
    if (coins.length !== 3) {
      throw new Error('每次摇卦必须有3个硬币结果');
    }
    
    const sum = coins.reduce((a, b) => a + b, 0);
    let lineType: YaoType;
    
    if (sum === 6) lineType = 'yin_changing';
    else if (sum === 9) lineType = 'yang_changing';
    else if (sum === 7) lineType = 'yang';
    else lineType = 'yin';
    
    results.push({
      coins: [...coins],
      sum,
      lineType
    });
  }
  
  return results;
};

// 测试用例
export const testManualCoinInput = () => {
  console.log('开始测试手动摇卦功能...');
  
  // 测试用例1: 正常的6次摇卦
  const testCase1 = [
    [3, 3, 3], // 9 - 阳动
    [2, 2, 2], // 6 - 阴动
    [3, 3, 2], // 8 - 阴静
    [3, 2, 2], // 7 - 阳静
    [3, 3, 3], // 9 - 阳动
    [2, 2, 3]  // 7 - 阳静
  ];
  
  try {
    const results = simulateManualCoinInput(testCase1);
    
    console.log('测试用例1结果:');
    results.forEach((result, index) => {
      const coinTextCount = result.coins.filter(c => c === 3).length;
      const coinBackCount = result.coins.filter(c => c === 2).length;
      const yaoName = result.sum === 6 ? '阴动' : 
                     result.sum === 9 ? '阳动' :
                     result.sum === 7 ? '阳静' : '阴静';
      
      console.log(`第${index + 1}爻: ${coinTextCount}字${coinBackCount}背 = ${result.sum} (${yaoName})`);
    });
    
    // 验证结果
    if (results.length === 6) {
      console.log('✅ 测试用例1通过: 成功生成6个爻的结果');
    } else {
      console.log('❌ 测试用例1失败: 结果数量不正确');
    }
    
    // 验证每个结果的正确性
    const expectedTypes: YaoType[] = ['yang_changing', 'yin_changing', 'yin', 'yang', 'yang_changing', 'yang'];
    let allCorrect = true;
    
    results.forEach((result, index) => {
      if (result.lineType !== expectedTypes[index]) {
        console.log(`❌ 第${index + 1}爻类型错误: 期望${expectedTypes[index]}, 实际${result.lineType}`);
        allCorrect = false;
      }
    });
    
    if (allCorrect) {
      console.log('✅ 所有爻的类型判断正确');
    }
    
  } catch (error) {
    console.log('❌ 测试用例1失败:', error);
  }
  
  // 测试用例2: 边界情况
  console.log('\n测试边界情况...');
  
  try {
    // 测试错误输入
    simulateManualCoinInput([[3, 3]]); // 只有2个硬币
  } catch (error) {
    console.log('✅ 正确捕获了错误输入:', error.message);
  }
  
  console.log('\n手动摇卦功能测试完成!');
};

// 如果直接运行此文件，执行测试
if (typeof window === 'undefined') {
  testManualCoinInput();
}
