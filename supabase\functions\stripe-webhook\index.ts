import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import Stripe from 'https://esm.sh/stripe@14.21.0?target=deno';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.39.3';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type, stripe-signature',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
};

const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') || '', {
  apiVersion: '2023-10-16',
});

const supabase = createClient(
  Deno.env.get('SUPABASE_URL') ?? '',
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
);

// Helper function to create payment log
async function createPaymentLog(userId: string, event: Stripe.Event, amount: number, currency: string, planName: string, reportsPurchased: number) {
  try {
    console.log(`[${new Date().toISOString()}] Creating payment log for user: ${userId}`);
    
    const insertData = {
      user_id: userId,
      event_type: event.type,
      payment_status: 'succeeded',
      amount: amount,
      currency: currency,
      plan_name: planName,
      reports_purchased: reportsPurchased,
      metadata: event.data.object
    };
    
    const { data, error } = await supabase
      .from('payment_logs')
      .insert(insertData)
      .select();
    
    if (error) {
      console.error('Payment log insert failed:', error);
      throw error;
    }
    
    console.log('Payment log created successfully:', data);
  } catch (error) {
    console.error('Payment log creation failed:', error);
    throw error;
  }
}

Deno.serve(async (req: Request) => {
  console.log(`[${new Date().toISOString()}] Webhook received: ${req.method} ${req.url}`);
  
  // Test database connection
  try {
    console.log(`[${new Date().toISOString()}] ===== DATABASE TEST: Testing connection =====`);
    const { data: testData, error: testError } = await supabase
      .from('user_credits')
      .select('count')
      .limit(1);
    
    if (testError) {
      console.error(`[${new Date().toISOString()}] ===== DATABASE ERROR: Connection test failed =====`);
      console.error(`[${new Date().toISOString()}] Error:`, testError);
    } else {
      console.log(`[${new Date().toISOString()}] ===== DATABASE SUCCESS: Connection test passed =====`);
    }
  } catch (error) {
    console.error(`[${new Date().toISOString()}] ===== DATABASE EXCEPTION: Connection test failed =====`);
    console.error(`[${new Date().toISOString()}] Exception:`, error);
  }
  
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const body = await req.text();
    console.log(`[${new Date().toISOString()}] Webhook body length: ${body.length}`);
    
    const signature = req.headers.get('stripe-signature');
    console.log(`[${new Date().toISOString()}] Stripe signature present: ${!!signature}`);
    
    if (!signature) {
      console.error('No stripe-signature header found');
      return new Response('No signature', { status: 400, headers: corsHeaders });
    }

    const webhookSecret = Deno.env.get('STRIPE_WEBHOOK_SECRET');
    console.log(`[${new Date().toISOString()}] Webhook secret present: ${!!webhookSecret}`);
    
    if (!webhookSecret) {
      console.error('STRIPE_WEBHOOK_SECRET not configured');
      return new Response('Webhook secret not configured', { status: 500, headers: corsHeaders });
    }

    let event: Stripe.Event;
    
    try {
      // 在Deno环境中，我们可以选择跳过签名验证或使用更简单的方式
      // 对于开发环境，我们可以直接解析事件
      if (Deno.env.get('NODE_ENV') === 'development' || !webhookSecret) {
        console.log('Development mode or no webhook secret - skipping signature verification');
        event = JSON.parse(body) as Stripe.Event;
      } else {
        // 尝试验证签名，如果失败则记录错误但不中断处理
        try {
          event = stripe.webhooks.constructEvent(body, signature, webhookSecret);
          console.log(`[${new Date().toISOString()}] Event verified successfully: ${event.type}`);
        } catch (verifyError) {
          console.error('Webhook signature verification failed:', verifyError);
          console.log('Attempting to parse event without verification...');
          event = JSON.parse(body) as Stripe.Event;
        }
      }
    } catch (err) {
      console.error('Failed to parse webhook event:', err);
      return new Response('Invalid event format', { status: 400, headers: corsHeaders });
    }

    console.log(`[${new Date().toISOString()}] Processing event: ${event.type}`);

    // Handle the event
    switch (event.type) {
      case 'checkout.session.completed':
        const session = event.data.object as Stripe.Checkout.Session;
        console.log(`[${new Date().toISOString()}] Processing checkout session: ${session.id}`);
        
        // Check if payment is successful
        if (session.payment_status === 'paid') {
          console.log(`[${new Date().toISOString()}] Payment successful for session: ${session.id}`);
          
          // Get user ID from session metadata
          const userId = session.metadata?.user_id;
          if (!userId) {
            console.error('No user_id found in session metadata');
            break;
          }
          
          try {
            // Get order details
            const { data: order, error: orderError } = await supabase
              .from('orders')
              .select('credits_purchased')
              .eq('stripe_session_id', session.id)
              .single();

            if (orderError) {
              console.error('Error fetching order:', orderError);
              throw orderError;
            }
            
            if (!order) {
              console.error('Order not found for session:', session.id);
              break;
            }
            
            // Update order status
            const { error: updateOrderError } = await supabase
              .from('orders')
              .update({ status: 'paid' })
              .eq('stripe_session_id', session.id);

            if (updateOrderError) {
              console.error('Error updating order:', updateOrderError);
              throw updateOrderError;
            }
            
            // Update or create user credits
            const { data: existingCredits } = await supabase
              .from('user_credits')
              .select('*')
              .eq('user_id', userId)
              .single();

            if (existingCredits) {
              // Update existing credits
              const { error: creditsError } = await supabase
                .from('user_credits')
                .update({
                  credits_remaining: existingCredits.credits_remaining + order.credits_purchased,
                  total_purchased: existingCredits.total_purchased + order.credits_purchased,
                  last_purchase_at: new Date().toISOString(),
                })
                .eq('user_id', userId);

              if (creditsError) {
                console.error('Error updating credits:', creditsError);
                throw creditsError;
              }
              
              console.log(`Credits updated for user ${userId}: +${order.credits_purchased}`);
            } else {
              // Create new credits record
              const { error: creditsError } = await supabase
                .from('user_credits')
                .insert({
                  user_id: userId,
                  credits_remaining: order.credits_purchased,
                  total_purchased: order.credits_purchased,
                  last_purchase_at: new Date().toISOString(),
                });

              if (creditsError) {
                console.error('Error creating credits:', creditsError);
                throw creditsError;
              }
              
              console.log(`Credits created for user ${userId}: ${order.credits_purchased}`);
            }
            
            // Create payment log
            await createPaymentLog(
              userId,
              event,
              session.amount_total || 0,
              session.currency || 'usd',
              session.metadata?.plan_name || 'Unknown Plan',
              order.credits_purchased
            );
            
            console.log(`[${new Date().toISOString()}] Webhook processed successfully for session: ${session.id}`);
          } catch (error) {
            console.error('Error processing checkout session:', error);
            throw error;
          }
        } else {
          console.log(`Payment status not successful: ${session.payment_status}`);
        }
        break;
        
      case 'payment_intent.succeeded':
        const paymentIntent = event.data.object as Stripe.PaymentIntent;
        console.log(`[${new Date().toISOString()}] Payment intent succeeded: ${paymentIntent.id}`);
        console.log(`[${new Date().toISOString()}] Payment intent data:`, {
          id: paymentIntent.id,
          amount: paymentIntent.amount,
          currency: paymentIntent.currency,
          customer: paymentIntent.customer,
          metadata: paymentIntent.metadata,
          receipt_email: paymentIntent.receipt_email,
          description: paymentIntent.description
        });
        
        // Handle payment intent success
        // This can be used for direct payments or as a backup for checkout sessions
        if (paymentIntent.metadata?.user_id) {
          const userId = paymentIntent.metadata.user_id;
          const credits = parseInt(paymentIntent.metadata.credits_purchased || '0');
          const planName = paymentIntent.metadata.plan_name || 'Unknown Plan';
          
          if (credits > 0) {
            try {
              // Update or create user credits
              const { data: existingCredits } = await supabase
                .from('user_credits')
                .select('*')
                .eq('user_id', userId)
                .single();

              if (existingCredits) {
                // Update existing credits
                const { error: creditsError } = await supabase
                  .from('user_credits')
                  .update({
                    credits_remaining: existingCredits.credits_remaining + credits,
                    total_purchased: existingCredits.total_purchased + credits,
                    last_purchase_at: new Date().toISOString(),
                  })
                  .eq('user_id', userId);

                if (creditsError) {
                  console.error('Error updating credits from payment intent:', creditsError);
                  throw creditsError;
                }
                
                console.log(`Credits updated for user ${userId} from payment intent: +${credits}`);
              } else {
                // Create new credits record
                const { error: creditsError } = await supabase
                  .from('user_credits')
                  .insert({
                    user_id: userId,
                    credits_remaining: credits,
                    total_purchased: credits,
                    last_purchase_at: new Date().toISOString(),
                  });

                if (creditsError) {
                  console.error('Error creating credits from payment intent:', creditsError);
                  throw creditsError;
                }
                
                console.log(`Credits created for user ${userId} from payment intent: ${credits}`);
              }
              
              // Create payment log
              await createPaymentLog(
                userId,
                event,
                paymentIntent.amount,
                paymentIntent.currency,
                planName,
                credits
              );
              
              console.log(`[${new Date().toISOString()}] Payment intent processed successfully for user: ${userId}`);
            } catch (error) {
              console.error('Error processing payment intent:', error);
              throw error;
            }
          }
        }
        break;
        
      case 'payment_intent.payment_failed':
        const failedPayment = event.data.object as Stripe.PaymentIntent;
        console.log(`[${new Date().toISOString()}] Payment failed: ${failedPayment.id}`);
        console.log(`[${new Date().toISOString()}] Failed payment data:`, {
          id: failedPayment.id,
          amount: failedPayment.amount,
          currency: failedPayment.currency,
          customer: failedPayment.customer,
          metadata: failedPayment.metadata,
          last_payment_error: failedPayment.last_payment_error
        });
        
        // Handle payment failure
        if (failedPayment.metadata?.user_id) {
          const userId = failedPayment.metadata.user_id;
          
          try {
            // Update order status to failed if it exists
            const { error: updateError } = await supabase
              .from('orders')
              .update({ 
                status: 'failed',
                updated_at: new Date().toISOString()
              })
              .eq('user_id', userId)
              .eq('stripe_session_id', failedPayment.id);

            if (updateError) {
              console.error('Error updating order status to failed:', updateError);
            } else {
              console.log(`Order status updated to failed for user: ${userId}`);
            }
            
            // Create payment log for failed payment
            await createPaymentLog(
              userId,
              event,
              failedPayment.amount,
              failedPayment.currency,
              failedPayment.metadata?.plan_name || 'Unknown Plan',
              0 // No credits purchased for failed payment
            );
            
            console.log(`[${new Date().toISOString()}] Failed payment logged for user: ${userId}`);
          } catch (error) {
            console.error('Error processing failed payment:', error);
            // Don't throw error for failed payments to avoid webhook retries
          }
        }
        break;
        
      default:
        console.log(`[${new Date().toISOString()}] Unhandled event type: ${event.type}`);
    }

    return new Response(JSON.stringify({ received: true }), {
      status: 200,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });

  } catch (err) {
    console.error(`[${new Date().toISOString()}] Webhook processing failed:`, err);
    return new Response(
      `Webhook processing failed: ${err.message}`,
      { status: 400, headers: corsHeaders }
    );
  }
}); 