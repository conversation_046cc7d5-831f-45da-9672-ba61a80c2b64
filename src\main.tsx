import { createRoot } from 'react-dom/client'
import App from './App.tsx'
import './index.css'

console.log('Main.tsx loading...')

const rootElement = document.getElementById("root")
console.log('Root element found:', !!rootElement)

if (rootElement) {
  try {
    console.log('Creating React root...')
    createRoot(rootElement).render(<App />)
    console.log('React app rendered successfully')
  } catch (error) {
    console.error('Error rendering React app:', error)
  }
} else {
  console.error('Root element not found!')
}
