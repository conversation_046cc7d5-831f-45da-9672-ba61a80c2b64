import React from 'react';
import { HexagramDisplay } from './HexagramDisplay';
import { generateHexagram } from './HexagramUtils';
import { YaoType } from './YaoLine';
import { DivinationCard } from './DivinationCard';

const HiddenSpiritTest: React.FC = () => {
  // 创建一个六亲不全的测试卦象（山火贲）
  const testLines: YaoType[] = ['yang', 'yin', 'yang', 'yin', 'yin', 'yang'];
  const testHexagram = generateHexagram(testLines);

  console.log('测试卦象:', testHexagram);
  console.log('伏神信息:', testHexagram.hiddenSpirits);

  // 分析六亲情况
  const relationTypes = ['父母', '官鬼', '兄弟', '子孙', '妻财'];
  const existingTypes = testHexagram.sixRelations.map(rel => rel.substring(0, 2));
  const missingRelations = relationTypes.filter(type => !existingTypes.includes(type));

  return (
    <div className="container mx-auto p-4 space-y-6">
      <h1 className="text-2xl font-bold mb-4">伏神功能测试</h1>
      
      <DivinationCard title="卦象信息">
        <div className="space-y-2">
          <p><strong>卦名：</strong>{testHexagram.name}</p>
          <p><strong>卦型：</strong>{testHexagram.hexagramType}</p>
          <p><strong>世爻：</strong>第{testHexagram.worldPosition}爻</p>
          <p><strong>应爻：</strong>第{testHexagram.responsePosition}爻</p>
        </div>
      </DivinationCard>

      <DivinationCard title="六亲分析">
        <div className="space-y-4">
          <div>
            <h3 className="font-semibold mb-2">现有六亲：</h3>
            <div className="flex flex-wrap gap-2">
              {testHexagram.sixRelations.map((relation, index) => (
                <span key={index} className="px-2 py-1 bg-blue-100 rounded text-sm">
                  第{index + 1}爻: {relation}
                </span>
              ))}
            </div>
          </div>
          
          <div>
            <h3 className="font-semibold mb-2">六亲类型统计：</h3>
            <div className="flex flex-wrap gap-2">
              {relationTypes.map(type => (
                <span 
                  key={type} 
                  className={`px-2 py-1 rounded text-sm ${
                    existingTypes.includes(type) 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-red-100 text-red-800'
                  }`}
                >
                  {type}: {existingTypes.includes(type) ? '✓' : '✗'}
                </span>
              ))}
            </div>
          </div>

          {missingRelations.length > 0 && (
            <div>
              <h3 className="font-semibold mb-2 text-red-600">缺失六亲：</h3>
              <div className="flex flex-wrap gap-2">
                {missingRelations.map(relation => (
                  <span key={relation} className="px-2 py-1 bg-red-100 text-red-800 rounded text-sm">
                    {relation}
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>
      </DivinationCard>

      <DivinationCard title="伏神信息">
        {testHexagram.hiddenSpirits.length > 0 ? (
          <div className="space-y-3">
            <p className="text-green-600 font-semibold">
              发现 {testHexagram.hiddenSpirits.length} 个伏神：
            </p>
            {testHexagram.hiddenSpirits.map((hs, index) => (
              <div key={index} className="p-3 bg-orange-50 rounded border-l-4 border-orange-400">
                <p><strong>伏神 {index + 1}：</strong></p>
                <p>六亲：{hs.relation}</p>
                <p>干支：{hs.ganZhi}</p>
                <p>五行：{hs.wuxing}</p>
                <p>本宫位置：第{hs.position + 1}爻</p>
                <p>伏在：第{hs.hiddenUnder + 1}爻下</p>
              </div>
            ))}
          </div>
        ) : (
          <p className="text-gray-600">此卦六亲齐全，无伏神。</p>
        )}
      </DivinationCard>

      <DivinationCard title="卦象显示">
        <HexagramDisplay hexagram={testHexagram} />
      </DivinationCard>
    </div>
  );
};

export default HiddenSpiritTest;
