import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from './ui/card';
import { ScrollArea } from './ui/scroll-area';
import { SavedRecordDisplay } from './SavedRecordDisplay';
import { useDivinationRecords } from '@/hooks/useDivinationRecords';
import { useAuthStore } from '@/stores/authStore';
import { Loader2, BookOpen, History } from 'lucide-react';
import { useTranslation } from '@/hooks/useTranslation';

export const SavedRecordsPanel: React.FC = () => {
  const { user } = useAuthStore();
  const { records, loading } = useDivinationRecords();
  const { t } = useTranslation();

  if (!user) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center">
            <BookOpen className="mr-2 h-5 w-5" />
            {t('recordsHistory')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-center text-muted-foreground text-sm">
            {t('loginToViewHistory')}
          </p>
        </CardContent>
      </Card>
    );
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center">
            <History className="mr-2 h-5 w-5" />
            {t('recordsHistory')}
          </CardTitle>
        </CardHeader>
        <CardContent className="flex items-center justify-center p-6">
          <Loader2 className="h-6 w-6 animate-spin" />
        </CardContent>
      </Card>
    );
  }

  if (!records || records.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center">
            <History className="mr-2 h-5 w-5" />
            {t('recordsHistory')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-center text-muted-foreground text-sm">
            暂无占卜记录
          </p>
        </CardContent>
      </Card>
    );
  }

  // 只显示最近的3条记录
  const recentRecords = records.slice(0, 3);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg flex items-center">
          <History className="mr-2 h-5 w-5" />
          最近记录
        </CardTitle>
      </CardHeader>
      <CardContent className="p-0">
        <ScrollArea className="h-64">
          <div className="space-y-2 p-4">
            {recentRecords.map((record) => (
              <div key={record.id} className="border rounded-lg p-3 bg-muted/50">
                <div className="text-sm font-medium mb-1">
                  {record.hexagram_name}
                </div>
                <div className="text-xs text-muted-foreground">
                  {new Date(record.divination_time).toLocaleDateString('zh-CN')}
                </div>
                {record.question && (
                  <div className="text-xs text-muted-foreground mt-1 line-clamp-2">
                    {record.question}
                  </div>
                )}
              </div>
            ))}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
};