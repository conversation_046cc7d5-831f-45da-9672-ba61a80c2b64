// 纳甲六爻排盘系统 - 基于传统纳甲法重新实现

// 八卦序列（按传统顺序）
export const YAOS = ['乾', '兑', '离', '震', '巽', '坎', '艮', '坤'] as const;

// 天干
export const GANS = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'] as const;

// 地支
export const ZHIS = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'] as const;

// 五行 - 按qichenx项目的编码：金0木3水1火4土2
export const XING5 = ['金', '水', '土', '木', '火'] as const;

// 六亲
export const QING6 = ['官鬼', '妻财', '兄弟', '父母', '子孙'] as const;

// 六神
export const SHEN6 = ['青龙', '朱雀', '勾陈', '螣蛇', '白虎', '玄武'] as const;

// 地支对应五行索引 - 按qichenx编码：金0木3水1火4土2
// 子丑寅卯辰巳午未申酉戌亥
export const ZHI5 = [1, 2, 3, 3, 2, 4, 4, 2, 0, 0, 2, 1] as const; // 水土木木土火火土金金土水

// 八卦对应五行索引 - 按二进制值顺序：金0木3水1火4土2
// 坤艮坎巽震离兑乾 (000,001,010,011,100,101,110,111)
export const GUA5 = [2, 2, 1, 3, 3, 4, 0, 0] as const; // 土土水木木火金金

// 纳甲配置 - 八卦的天干地支配置（按二进制值顺序）
// [内卦天干, [内卦地支1,2,3], 外卦天干, [外卦地支1,2,3]]
export const NAJIA = [
  // 坤宫 (000)
  [['乙', '未', '巳', '卯'], ['癸', '丑', '亥', '酉']],
  // 艮宫 (001)
  [['丙', '辰', '午', '申'], ['丙', '戌', '子', '寅']],
  // 坎宫 (010)
  [['戊', '寅', '辰', '午'], ['戊', '申', '戌', '子']],
  // 巽宫 (011)
  [['辛', '丑', '亥', '酉'], ['辛', '未', '巳', '卯']],
  // 震宫 (100)
  [['庚', '子', '寅', '辰'], ['庚', '午', '申', '戌']],
  // 离宫 (101)
  [['己', '卯', '丑', '亥'], ['己', '酉', '未', '巳']],
  // 兑宫 (110)
  [['丁', '巳', '卯', '丑'], ['丁', '亥', '酉', '未']],
  // 乾宫 (111)
  [['甲', '子', '寅', '辰'], ['壬', '午', '申', '戌']]
] as const;

// 旬空对照表
export const KONG = ['子丑', '寅卯', '辰巳', '午未', '申酉', '戌亥'] as const;

// 64卦二进制到卦名映射（自下而上）
export const GUA64: Record<string, string> = {
  '111111': '乾为天', '011111': '天风姤', '101111': '天火同人', '001111': '天山遁',
  '110111': '天泽履', '010111': '天水讼', '100111': '天雷无妄', '000111': '天地否',
  '111110': '泽天夬', '011110': '泽风大过', '101110': '泽火革', '001110': '泽山咸',
  '110110': '兑为泽', '010110': '泽水困', '100110': '泽雷随', '000110': '泽地萃',
  '111101': '火天大有', '011101': '火风鼎', '101101': '离为火', '001101': '火山旅',
  '110101': '火泽睽', '010101': '火水未济', '100101': '火雷噬嗑', '000101': '火地晋',
  '111100': '雷天大壮', '011100': '雷风恒', '101100': '雷火丰', '001100': '雷山小过',
  '110100': '雷泽归妹', '010100': '雷水解', '100100': '震为雷', '000100': '雷地豫',
  '111011': '风天小畜', '011011': '巽为风', '101011': '风火家人', '001011': '风山渐',
  '110011': '风泽中孚', '010011': '风水涣', '100011': '风雷益', '000011': '风地观',
  '111010': '水天需', '011010': '水风井', '101010': '水火既济', '001010': '水山蹇',
  '110010': '水泽节', '010010': '坎为水', '100010': '水雷屯', '000010': '水地比',
  '111001': '山天大畜', '011001': '山风蛊', '101001': '山火贲', '001001': '艮为山',
  '110001': '山泽损', '010001': '山水蒙', '100001': '山雷颐', '000001': '山地剥',
  '111000': '地天泰', '011000': '地风升', '101000': '地火明夷', '001000': '地山谦',
  '110000': '地泽临', '010000': '地水师', '100000': '地雷复', '000000': '坤为地'
};

// 卦名到编号映射
export const GUAS: Record<string, string> = {
  0: '乾', 1: '兑', 2: '离', 3: '震', 4: '巽', 5: '坎', 6: '艮', 7: '坤'
};

// 六合卦名标识
export const LIUHE = ['天地', '雷风', '水火', '山泽'] as const;

// 六亲与五行地支的对应关系 - 基于qichenx项目的PropertyPair
// 格式：[卦宫五行索引, 地支枚举值] -> 六亲
// 金0木3水1火4土2
export const PROPERTY_PAIRS = {
  // 官鬼
  GUAN: [
    [0, 'WU'], [0, 'SI'], [1, 'CHEN'], [1, 'XU'], [1, 'CHOU'], [1, 'WEI'],
    [2, 'YIN'], [2, 'MAO'], [3, 'SHEN'], [3, 'YOU'], [4, 'HAI'], [4, 'ZI']
  ],
  // 妻财
  QI: [
    [0, 'YIN'], [0, 'MAO'], [1, 'SI'], [1, 'WU'], [2, 'HAI'], [2, 'ZI'],
    [3, 'CHEN'], [3, 'XU'], [3, 'CHOU'], [3, 'WEI'], [4, 'SHEN'], [4, 'YOU']
  ],
  // 兄弟
  XIONG: [
    [0, 'SHEN'], [0, 'YOU'], [1, 'ZI'], [1, 'HAI'], [2, 'CHEN'], [2, 'XU'],
    [2, 'CHOU'], [2, 'WEI'], [3, 'YIN'], [3, 'MAO'], [4, 'SI'], [4, 'WU']
  ],
  // 父母
  FU: [
    [0, 'CHEN'], [0, 'XU'], [0, 'CHOU'], [0, 'WEI'], [1, 'SHEN'], [1, 'YOU'],
    [2, 'SI'], [2, 'WU'], [3, 'HAI'], [3, 'ZI'], [4, 'YIN'], [4, 'MAO']
  ],
  // 子孙
  ZI: [
    [0, 'HAI'], [0, 'ZI'], [1, 'YIN'], [1, 'MAO'], [2, 'SHEN'], [2, 'YOU'],
    [3, 'SI'], [3, 'WU'], [4, 'CHEN'], [4, 'XU'], [4, 'CHOU'], [4, 'WEI']
  ]
} as const;

// 地支枚举映射
export const EARTH_ENUM_MAP: Record<string, string> = {
  '子': 'ZI', '丑': 'CHOU', '寅': 'YIN', '卯': 'MAO',
  '辰': 'CHEN', '巳': 'SI', '午': 'WU', '未': 'WEI',
  '申': 'SHEN', '酉': 'YOU', '戌': 'XU', '亥': 'HAI'
};