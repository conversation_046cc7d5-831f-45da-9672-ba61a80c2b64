Directory structure:
└── bopo-najia/
    ├── README.md
    ├── CHANGELOG.md
    ├── LICENSE
    ├── Makefile
    ├── pyproject.toml
    ├── requirements.txt
    ├── sample.py
    ├── tox.ini
    ├── .drone.yml
    ├── .editorconfig
    ├── .pre-commit-config.yaml
    ├── najia/
    │   ├── __init__.py
    │   ├── __main__.py
    │   ├── const.py
    │   ├── najia.py
    │   ├── utils.py
    │   └── data/
    │       ├── guaci.pkl
    │       └── standard.tpl
    └── tests/
        ├── __init__.py
        ├── conftest.py
        ├── test_compile.py
        ├── test_god6.py
        ├── test_gong.py
        ├── test_gua.py
        ├── test_guaci.py
        ├── test_gz5x.py
        ├── test_mark.py
        ├── test_najia.py
        ├── test_qin6.py
        ├── test_xkong.py
        └── data/
            ├── dc.json
            ├── guaci.txt
            └── simple.tpl

================================================
FILE: README.md
================================================
纳甲六爻排盘项目
================

[![image](https://img.shields.io/pypi/v/najia.svg)](https://pypi.python.org/pypi/najia)
[![image](https://img.shields.io/travis/bopo/najia.svg)](https://travis-ci.org/bopo/najia)
[![Documentation Status](https://readthedocs.org/projects/najia/badge/?version=latest)](https://najia.readthedocs.io/en/latest/?badge=latest)
[![Updates](https://pyup.io/repos/github/bopo/najia/shield.svg)](https://pyup.io/repos/github/bopo/najia/)

Python Boilerplate contains all the boilerplate you need to create a
Python package.

-   Free software: MIT license
-   Documentation: <https://najia.readthedocs.io>.

Features
--------

-   全部安易卦爻
-   函数独立编写
-   测试各个函数
-   重新命名函数

阳历，阴历（干支，旬空）

-   卦符: mark (001000)，自下而上
-   卦名: name
-   变爻: bian
-   卦宫: gong
-   六亲: qin6
-   六神: god6
-   世爻: shiy, ying
-   纳甲: naja
-   纳甲五行: dzwx
-   卦宫五行: gowx

修复问题
--------

-   解决: 六神不对
-   解决: 世应也有点小BUG , 地天泰卦的世爻为3, 应爻为6
-   解决: 归魂卦世爻为3 此处返回4, 需要修改

\* 解决: 归魂卦的六亲是不对的,原因是utils.py里
判断六爻卦的卦宫名时,优先判读了if index in (1, 2, 3, 6)
而归魂卦的世爻也在3爻,被这个条件带走了. 解决: elif hun==\'归魂\'
这个条件调到前面即可 \* 解决: 还有一个不知是否算是错误的地方,就是bian
变卦中的六亲,
程序中是按变卦所在的本宫卦来定的,而不是按初始卦所属的本宫卦来定的六亲.

Credits
-------

This package was created with
[Cookiecutter](https://github.com/audreyr/cookiecutter) and the
[audreyr/cookiecutter-pypackage](https://github.com/audreyr/cookiecutter-pypackage)
project template.



================================================
FILE: CHANGELOG.md
================================================
## v2.0.1 (2023-05-07)

### Feat

- **guaci**: 改良卦辞数据库

## v2.0.0 (2023-05-07)

### Fix

- **covert**: update
- **covert**: update
- **covert**: update
- **covert**: update
- **covert**: update

## v1.2.3 (2022-07-28)

## v1.1.3 (2022-07-27)



================================================
FILE: LICENSE
================================================
MIT License

Copyright (c) 2019, najia

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.



================================================
FILE: Makefile
================================================
.PHONY: clean clean-test clean-pyc clean-build docs help
.DEFAULT_GOAL := help

define BROWSER_PYSCRIPT
import os, webbrowser, sys

try:
	from urllib import pathname2url
except:
	from urllib.request import pathname2url

webbrowser.open("file://" + pathname2url(os.path.abspath(sys.argv[1])))
endef
export BROWSER_PYSCRIPT

define PRINT_HELP_PYSCRIPT
import re, sys

for line in sys.stdin:
	match = re.match(r'^([a-zA-Z_-]+):.*?## (.*)$$', line)
	if match:
		target, help = match.groups()
		print("%-20s %s" % (target, help))
endef
export PRINT_HELP_PYSCRIPT

BROWSER := python -c "$$BROWSER_PYSCRIPT"

help:
	@python -c "$$PRINT_HELP_PYSCRIPT" < $(MAKEFILE_LIST)

clean: clean-build clean-pyc clean-test ## remove all build, test, coverage and Python artifacts

clean-build:
	rm -fr build/
	rm -fr dist/
	rm -fr .eggs/
	find . -name '*.egg-info' -exec rm -fr {} +
	find . -name '*.egg' -exec rm -f {} +

clean-pyc:
	find . -name '*.pyc' -exec rm -f {} +
	find . -name '*.pyo' -exec rm -f {} +
	find . -name '*~' -exec rm -f {} +
	find . -name '__pycache__' -exec rm -fr {} +

clean-test:
	rm -fr .tox/
	rm -fr .coverage
	rm -fr htmlcov/
	rm -fr .pytest_cache

lint: ## check style with flake8
	pre-commit run --all-files

test: ## run tests quickly with the default Python
	poetry run pytest -v tests

pypi: dist ## Publish to PyPi
	poetry publish --dry-run --skip-existing -vvv


dist: clean lint ## builds source and wheel package
	#python setup.py sdist
	#python setup.py bdist_wheel
	poetry build
	ls -lht dist

bump: ## bump version.
	cz bump --dry-run --yes -ch -cc --increment patch
	#cz bump --dry-run --yes -ch -cc --increment major

# DO NOT DELETE



================================================
FILE: pyproject.toml
================================================
[tool.poetry]
name = "najia"
version = "2.0.1"
description = ""
authors = ["bopo <<EMAIL>>"]
readme = "README.md"
include = ["CHANGELOG.md"]
repository = "https://github.com/bopo/najia"
homepage = "https://github.com/bopo/najia"
license = "MIT"
packages = [
    { include = "najia" }
]


[tool.poetry.scripts]
najia = 'najia.__main__:main'


[tool.poetry.dependencies]
python = "^3.9"
click = "^8.1.3"
arrow = "^1.2.3"
jinja2 = "^3.1.2"
lunar-python = "^1.3.2"


[[tool.poetry.source]]
name = "mirrors"
url = "https://mirrors.ustc.edu.cn/pypi/web/simple"
default = false
secondary = false

[tool.poetry.group.dev.dependencies]
pytest = "^7.3.1"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"


[tool.pytest.ini_options]
testpaths = "tests"
addopts = "-p no:warnings"
log_cli = 0
log_cli_level = "DEBUG"


[tool.commitizen]
version = "2.0.1"
tag_format = "v$version"
update_changelog_on_bump = true
changelog_file = "CHANGELOG.md"
annotated_tag = true
version_files = [
    "najia/__init__.py:__version__",
    "pyproject.toml:version",
]



================================================
FILE: requirements.txt
================================================
--extra-index-url https://mirrors.ustc.edu.cn/pypi/web/simple

arrow==1.2.3 ; python_version >= "3.9" and python_version < "4.0"
click==8.1.3 ; python_version >= "3.9" and python_version < "4.0"
colorama==0.4.6 ; python_version >= "3.9" and python_version < "4.0" and platform_system == "Windows"
jinja2==3.1.2 ; python_version >= "3.9" and python_version < "4.0"
lunar-python==1.3.2 ; python_version >= "3.9" and python_version < "4.0"
markupsafe==2.1.2 ; python_version >= "3.9" and python_version < "4.0"
python-dateutil==2.8.2 ; python_version >= "3.9" and python_version < "4.0"
six==1.16.0 ; python_version >= "3.9" and python_version < "4.0"



================================================
FILE: sample.py
================================================
from najia.najia import Najia

if __name__ == '__main__':
    params = [2, 2, 1, 2, 4, 2]
    result = Najia(2).compile(params=params, date='2019-12-25 00:20').render()
    print(result)



================================================
FILE: tox.ini
================================================
[tox]
envlist = py36, py37, py38, py39, py310

[travis]
python =
    3.10: py310
    3.9: py39
    3.8: py38
    3.7: py37
    3.6: py36

[testenv]
setenv =
    PYTHONPATH = {toxinidir}
deps =
    -r{toxinidir}/requirements.txt
    pytest
commands =
    python -m pytest -v



================================================
FILE: .drone.yml
================================================
kind: pipeline
name: default

steps:
  - name: test
    image: python:3.8
    commands:
      - pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple
      - pip install pytest -i https://pypi.tuna.tsinghua.edu.cn/simple
      - pytest -v tests



================================================
FILE: .editorconfig
================================================
# http://editorconfig.org

root = true

[*]
indent_style = space
indent_size = 4
trim_trailing_whitespace = true
insert_final_newline = true
charset = utf-8
end_of_line = lf

[*.bat]
indent_style = tab
end_of_line = crlf

[LICENSE]
insert_final_newline = false

[Makefile]
indent_style = tab



================================================
FILE: .pre-commit-config.yaml
================================================
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace  # Trim trailing whitespace
      - id: check-merge-conflict  # Check for files that contain merge conflict strings
      - id: check-case-conflict
      - id: double-quote-string-fixer  # Replace double quoted strings with single quoted strings
      - id: end-of-file-fixer  # Make sure files end in a newline and only a newline
      - id: requirements-txt-fixer  # Sort entries in requirements.txt and remove incorrect entry for pkg-resources==0.0.0
      - id: fix-encoding-pragma  # Remove the coding pragma: # -*- coding: utf-8 -*-
        args: [ "--remove" ]
      - id: mixed-line-ending  # Replace or check mixed line ending
        args: [ "--fix=lf" ]
      - id: check-ast
        name: Check python ast
        description: Simply check whether the files parse as valid python.
        entry: check-ast
        language: python
        types: [ python ]
        exclude: >
          (?x)^(
              setup.py
          )$

  - repo: https://gitee.com/ibopo/pre-commit-hooks
    rev: v1.1.9
    hooks:
      - id: forbid-crlf
      - id: remove-crlf
      - id: forbid-tabs
      - id: remove-tabs

  - repo: https://github.com/asottile/reorder_python_imports
    rev: v3.9.0
    hooks:
      - id: reorder-python-imports

  - repo: https://github.com/python-poetry/poetry
    rev: '1.4.0'  # add version here
    hooks:
#      - id: poetry-check
#      - id: poetry-lock
      - id: poetry-export
        args: ["-f", "requirements.txt", "-o", "requirements.txt", "--without-hashes"]



================================================
FILE: najia/__init__.py
================================================
__author__ = """bopowang"""
__email__ = '<EMAIL>'
__version__ = '2.0.1'

from .najia import Najia



================================================
FILE: najia/__main__.py
================================================
import random
import sys

import click

from . import __version__
from . import Najia


@click.command()
@click.help_option('-h', '--help')
@click.version_option(__version__, '-V', '--version', prog_name='najia', message='%(prog)s: v%(version)s', )
@click.option('-v', '--verbose', count=True, help='卦爻样式')
@click.option('-p', '--params', default=None, help='摇卦参数')
@click.option('-g', '--gender', default='', help='摇卦人性别.')
@click.option('-l', '--lunar', default=False, help='是否阴历.')
@click.option('-t', '--title', default='', help='求卦问卜事情.')
@click.option('-c', '--guaci', is_flag=True, help='是否显示卦辞.')
@click.option('-d', '--date', default=None, help='日期 YYYY-MM-DD hh:mm.')
@click.option('--day', default=None, help='日干支.')
def main(params, gender, lunar, date, title, guaci, day, verbose):
    params = [random.randint(1, 4) for _ in range(0, 6)] if params is None else params
    params = [int(x) for x in params.replace(',', '')] if type(params) == str else params
    params = [int(str(x).replace('0', '4')) for x in params]

    gua = Najia(verbose).compile(params=params, gender=gender, date=date, title=title, guaci=guaci, day=day)
    res = gua.render()

    print(res)

    return 0


if __name__ == '__main__':
    sys.exit(main())  # pragma: no cover



================================================
FILE: najia/const.py
================================================
[Binary file]


================================================
FILE: najia/najia.py
================================================
import json
import logging
import os
from pathlib import Path

import arrow
from jinja2 import Template

from .const import GANS
from .const import GUA5
from .const import GUA64
from .const import GUAS
from .const import SYMBOL
from .const import XING5
from .const import YAOS
from .const import ZHI5
from .const import ZHIS
from .utils import get_god6
from .utils import get_guaci
from .utils import get_najia
from .utils import get_qin6
from .utils import get_type
from .utils import GZ5X
from .utils import palace
from .utils import set_shi_yao

logging.basicConfig(level='INFO')
logger = logging.getLogger(__name__)


class Najia(object):

    def __init__(self, verbose=None):
        self.verbose = (verbose, 2)[verbose > 2] or 0
        self.bian = None  # 变卦
        self.hide = None  # 伏神
        self.data = None

    @staticmethod
    def _gz(cal):
        """
        获取干支
        :param cal:
        :return:
        """
        return GANS[cal.tg] + ZHIS[cal.dz]

    @staticmethod
    def _cn(cal):
        """
        转换中文干支
        :param cal:
        :return:
        """
        return GANS[cal.tg] + ZHIS[cal.dz]

    @staticmethod
    def _daily(date=None):
        """
        计算日期
        :param date:
        :return:
        """
        # lunar = sxtwl.Lunar()
        # daily = lunar.getDayBySolar(date.year, date.month, date.day)
        # hour = lunar.getShiGz(daily.Lday2.tg, date.hour)

        from lunar_python import Solar

        solar = Solar.fromYmdHms(date.year, date.month, date.day, date.hour, 0, 0)
        lunar = solar.getLunar()

        ganzi = lunar.getBaZi()

        result = {
            # 'xkong': xkong(''.join([GANS[daily.Lday2.tg], ZHIS[daily.Lday2.dz]])),
            'xkong': lunar.getDayXunKong(),
            # 'month': daily.Lmonth2,
            # 'year' : daily.Lyear2,
            # 'day'  : daily.Lday2,
            # 'hour' : hour,
            # 'cn'   : {
            #     'month': self._gz(daily.Lmonth2),
            #     'year' : self._gz(daily.Lyear2),
            #     'day'  : self._gz(daily.Lday2),
            #     'hour' : self._gz(hour),
            # },
            'gz': {
                'month': ganzi[1],
                'year': ganzi[0],
                'day': ganzi[2],
                'hour': ganzi[3],
            }
        }
        # pprint(result)
        return result

    @staticmethod
    def _hidden(gong=None, qins=None):
        """
        计算伏神卦

        :param gong:
        :param qins:
        :return:
        """
        if gong is None:
            raise Exception('')

        if qins is None:
            raise Exception('')

        if len(set(qins)) < 5:
            mark = YAOS[gong] * 2

            logger.debug(mark)

            # 六亲
            qin6 = [(get_qin6(XING5[int(GUA5[gong])], ZHI5[ZHIS.index(x[1])])) for x in get_najia(mark)]

            # 干支五行
            qinx = [GZ5X(x) for x in get_najia(mark)]
            seat = [qin6.index(x) for x in list(set(qin6).difference(set(qins)))]

            return {
                'name': GUA64.get(mark),
                'mark': mark,
                'qin6': qin6,
                'qinx': qinx,
                'seat': seat,
            }

        return None

    @staticmethod
    def _transform(params=None, gong=None):
        """
        计算变卦

        :param params:
        :return:
        """

        if params is None:
            raise Exception('')

        if type(params) == str:
            params = [x for x in params]

        if len(params) < 6:
            raise Exception('')

        if 3 in params or 4 in params:
            mark = ''.join(['1' if v in [1, 4] else '0' for v in params])
            qin6 = [(get_qin6(XING5[int(GUA5[gong])], ZHI5[ZHIS.index(x[1])])) for x in get_najia(mark)]
            qinx = [GZ5X(x) for x in get_najia(mark)]

            return {
                'name': GUA64.get(mark),
                'mark': mark,
                'qin6': qin6,
                'qinx': qinx,
                'gong': GUAS[palace(mark, set_shi_yao(mark)[0])],
            }

        return None

    def compile(self, params=None, gender=None, date=None, title=None, guaci=False, **kwargs):
        """
        根据参数编译卦

        :param guaci:
        :param title:
        :param gender:
        :param params:
        :param date:
        :return:
        """

        title = (title, '')[not title]
        solar = arrow.now() if date is None else arrow.get(date)
        lunar = self._daily(solar)

        # gender = '男' if gender == 1 else '女'
        gender = ('', gender)[bool(gender)]

        # 卦码
        mark = ''.join([str(int(p) % 2) for p in params])

        shiy = set_shi_yao(mark)  # 世应爻

        # 卦宫
        gong = palace(mark, shiy[0])  # 卦宫

        # 卦名
        name = GUA64[mark]

        # 六亲
        qin6 = [(get_qin6(XING5[int(GUA5[gong])], ZHI5[ZHIS.index(x[1])])) for x in get_najia(mark)]
        qinx = [GZ5X(x) for x in get_najia(mark)]

        # logger.debug(qin6)

        # 六神
        # god6 = God6(''.join([GANS[lunar['day'].tg], ZHIS[lunar['day'].dz]]))
        god6 = get_god6(lunar['gz']['day'])

        # 动爻位置
        dong = [i for i, x in enumerate(params) if x > 2]
        # logger.debug(dong)

        # 伏神
        hide = self._hidden(gong, qin6)

        # 变卦
        bian = self._transform(params=params, gong=gong)

        self.data = {
            'params': params,
            'gender': gender,
            'title': title,
            'guaci': guaci,
            'solar': solar,
            'lunar': lunar,
            'god6': god6,
            'dong': dong,
            'name': name,
            'mark': mark,
            'gong': GUAS[gong],
            'shiy': shiy,
            'qin6': qin6,
            'qinx': qinx,
            'bian': bian,
            'hide': hide,
        }

        # logger.debug(self.data)

        return self

    def gua_type(self, i):
        return

    def render(self):
        """

        :return:
        """
        tpl = Path(__file__).parent / 'data' / 'standard.tpl'
        tpl = tpl.read_text(encoding='utf-8')

        empty = '\u3000' * 6
        rows = self.data

        # symbal = ['━　━', '━━━━', '━　━', '○→', '×→']
        # yaos = ['▅▅  ▅▅', '▅▅▅▅▅▅', '▅▅  ▅▅', '○→', '×→']
        symbal = SYMBOL[self.verbose]

        rows['dyao'] = [symbal[x] if x in (3, 4) else '' for x in self.data['params']]

        rows['main'] = {}
        rows['main']['mark'] = [symbal[int(x)] for x in self.data['mark']]
        rows['main']['type'] = get_type(self.data['mark'])

        rows['main']['gong'] = rows['gong']
        rows['main']['name'] = rows['name']
        rows['main']['indent'] = '\u3000' * 2

        if rows.get('hide'):
            rows['hide']['qin6'] = [' %s%s ' % (rows['hide']['qin6'][x], rows['hide']['qinx'][x]) if x in rows['hide']['seat'] else empty for x in range(0, 6)]
            rows['main']['indent'] += empty
        else:
            rows['main']['indent'] += '\u3000' * 1
            rows['hide'] = {'qin6': ['  ' for _ in range(0, 6)]}

        rows['main']['display'] = '{indent}{name} ({gong}-{type})'.format(**rows['main'])

        if rows.get('bian'):
            hide = (8, 19)[bool(rows.get('hide'))]
            rows['bian']['type'] = get_type(rows['bian']['mark'])
            rows['bian']['indent'] = (hide - len(rows['main']['display'])) * '\u3000'

            if rows['bian']['qin6']:
                # 变卦六亲问题
                rows['bian']['qin6'] = [f'{rows["bian"]["qin6"][x]}{rows["bian"]["qinx"][x]}' if x in self.data['dong'] else f'  {rows["bian"]["qin6"][x]}{rows["bian"]["qinx"][x]}'
                                        for x in range(0, 6)]

            if rows['bian']['mark']:
                rows['bian']['mark'] = [x for x in rows['bian']['mark']]
                rows['bian']['mark'] = [symbal[int(rows['bian']['mark'][x])] for x in range(0, 6)]
        else:
            rows['bian'] = {'qin6': [' ' for _ in range(0, 6)], 'mark': [' ' for _ in range(0, 6)]}

        shiy = []

        # 显示世应字
        for x in range(0, 6):
            if x == self.data['shiy'][0] - 1:
                shiy.append('世')
            elif x == self.data['shiy'][1] - 1:
                shiy.append('应')
            else:
                shiy.append('  ')

        rows['shiy'] = shiy

        if self.data['guaci']:
            rows['guaci'] = get_guaci(rows['name'])
            # rows['guaci'] = json.load(open(os.path.join(os.path.dirname(__file__), 'data/dd.json'))).get(rows['name'])
            # rows['guaci'] = rows.get('guaci', '').replace('********************', '').replace('　象曰：', '象曰：')

        template = Template(tpl)
        return template.render(**rows)

    def export(self):
        solar, params = self.data
        return solar, params

    def predict(self):
        return



================================================
FILE: najia/utils.py
================================================
import logging
import math
from pathlib import Path

from . import const

logging.basicConfig(level='INFO')
logger = logging.getLogger(__name__)


def GZ5X(gz=''):
    """
    干支五行
    :param gz:
    :return:
    """
    _, z = [i for i in gz]
    zm = const.ZHIS.index(z)

    return gz + const.XING5[const.ZHI5[zm]]


def mark(symbol=None):
    """
    单拆重交 转 二进制卦码
    :param symbol:
    :return:
    """

    res = [str(int(x) % 2) for x in symbol]
    logger.debug(res)

    return res


def xkong(gz='甲子'):
    """
    计算旬空

    :param gz: 甲子 or 3,11
    :return:
    """

    gm, zm = [i for i in gz]

    if type(gz) == str:
        gm = const.GANS.index(gm)
        zm = const.ZHIS.index(zm)

    if gm == zm or zm < gm:
        zm += 12

    xk = int((zm - gm) / 2) - 1

    return const.KONG[xk]


def get_god6(gz=None):
    """
    # 六神, 根据日干五行配对六神五行

    :param gz: 日干支
    :return:
    """

    gm, _ = [i for i in gz]

    if type(gm) is str:
        gm = const.GANS.index(gm)

    num = math.ceil((gm + 1) / 2) - 7

    if gm == 4:
        num = -4

    if gm == 5:
        num = -3

    if gm > 5:
        num += 1

    return const.SHEN6[num:] + const.SHEN6[:num]


'''
寻世诀：
天同二世天变五，地同四世地变初。
本宫六世三世异，人同游魂人变归。

1. 天同人地不同世在二，天不同人地同在五
2. 三才不同世在三
3. 人同其他不同世在四，人不同其他同在三'''


# 世爻初爻是1，二爻是2
# 寻世诀： 天同二世天变五  地同四世地变初  本宫六世三世异  人同游魂人变归
# int('111', 2) => 7
# 世爻 >= 3, 应爻 = 世爻 - 3， index = 5 - 世爻 + 1
# 世爻 <= 3, 应爻 = 世爻 + 3，
# life oneself
def set_shi_yao(symbol=None):
    """
    获取世爻

    :param symbol: 卦的二进制码
    :return: 世爻，应爻，所在卦宫位置
    """
    wai = symbol[3:]  # 外卦
    nei = symbol[:3]  # 内卦

    def shiy(shi, index=None):
        ying = shi - 3 if shi > 3 else shi + 3
        index = shi if index is None else index
        return shi, ying, index

    # 天同二世天变五
    if wai[2] == nei[2]:
        if wai[1] != nei[1] and wai[0] != nei[0]:
            return shiy(2)
    else:
        if wai[1] == nei[1] and wai[0] == nei[0]:
            return shiy(5)

    # 人同游魂人变归
    if wai[1] == nei[1]:
        if wai[0] != nei[0] and wai[2] != nei[2]:
            return shiy(4, 6)  # , Hun
    else:
        # fix 归魂问题
        if wai[0] == nei[0] and wai[2] == nei[2]:
            return shiy(3, 6)  # , Hun

    # 地同四世地变初
    if wai[0] == nei[0]:
        if wai[1] != nei[1] and wai[2] != nei[2]:
            return shiy(4)
    else:
        if wai[1] == nei[1] and wai[2] == nei[2]:
            return shiy(1)

    # 本宫六世
    if wai == nei:
        return shiy(6)

    # 三世异
    return shiy(3)


def get_type(symbol=None):
    if res := soul(symbol):
        return res

    if attack(symbol):
        return '六冲'

    if res := unite(symbol):
        return res

    return ''


def unite(symbol=None):
    name = const.GUA64[symbol]

    for x in const.LIUHE:
        if x in name:
            return '六合'

    return None


def soul(symbol=None):
    wai = symbol[3:]  # 外卦
    nei = symbol[:3]  # 内卦
    hun = ''

    if wai[1] == nei[1]:
        if wai[0] != nei[0] and wai[2] != nei[2]:
            hun = '游魂'
    else:
        if wai[0] == nei[0] and wai[2] == nei[2]:
            hun = '归魂'

    return hun


def palace(symbol=None, index=None):  # inStr -> '111000'  # intNum -> 世爻
    """
    六爻卦的卦宫名

    认宫诀：
    一二三六外卦宫，四五游魂内变更。
    若问归魂何所取，归魂内卦是本宫。

    :param symbol: 卦的二进制码
    :param index: 世爻
    :return:
    """

    wai = symbol[3:]  # 外卦
    nei = symbol[:3]  # 内卦
    hun = ''

    if wai[1] == nei[1]:
        if wai[0] != nei[0] and wai[2] != nei[2]:
            hun = '游魂'
    else:
        if wai[0] == nei[0] and wai[2] == nei[2]:
            hun = '归魂'

    # 归魂内卦是本宫
    if hun == '归魂':
        return const.YAOS.index(nei)

    # 一二三六外卦宫
    if index in (1, 2, 3, 6):
        return const.YAOS.index(wai)

    # 四五游魂内变更
    if index in (4, 5) or hun == '游魂':
        symbol = ''.join([str(int(c) ^ 1) for c in nei])
        return const.YAOS.index(symbol)


# 判断是否六冲卦
# verb
def attack(symbol):
    wai = symbol[3:]  # 外卦
    nei = symbol[:3]  # 内卦

    # 内外卦相同
    if wai == nei:
        return True

    # 天雷无妄 和 雷天大壮
    gua = [nei, wai]

    try:
        if len(set(gua).difference(('100', '111'))) == 0:
            return True
    except TypeError:
        pass

    return False


# 纳甲配干支
def get_najia(symbol=None):
    """
    纳甲配干支

    :param symbol:
    :return:
    """

    wai = symbol[3:]  # 外卦
    nei = symbol[:3]  # 内卦

    wai, nei = const.YAOS.index(wai), const.YAOS.index(nei)

    gan = const.NAJIA[nei][0][0]
    ngz = [f'{gan}{zhi}' for zhi in const.NAJIA[nei][0][1:]]  # 排干支

    gan = const.NAJIA[wai][1][0]
    wgz = [f'{gan}{zhi}' for zhi in const.NAJIA[wai][1][1:]]  # 排干支

    return ngz + wgz


def get_qin6(w1, w2):
    """
    两个五行判断六亲
    水1 # 木2 # 金3 # 火4 # 土5

    :param w1:
    :param w2:
    :return:
    """
    w1 = const.XING5.index(w1) if type(w1) is str else w1
    w2 = const.XING5.index(w2) if type(w2) is str else w2

    ws = w1 - w2
    ws = ws + 5 if ws < 0 else ws
    q6 = const.QING6[ws]

    logger.debug(ws)
    logger.debug(q6)

    return q6


def get_guaci(name=None):
    import pickle

    try:
        result = Path(__file__).parent / 'data' / 'guaci.pkl'
        result = pickle.loads(result.read_bytes())
        result = result.get(name)

        return result
    except Exception as ex:
        logger.exception(ex)



================================================
FILE: najia/data/guaci.pkl
================================================
[Binary file]


================================================
FILE: najia/data/standard.tpl
================================================
{{gender}}测：{{title}}

公历：{{solar.year}}年 {{solar.month}}月 {{solar.day}}日 {{solar.hour}}时 {{solar.minute}}分
干支：{{lunar.gz.year}}年 {{lunar.gz.month}}月 {{lunar.gz.day}}日 {{lunar.gz.hour}}时 （旬空：{{lunar.xkong}})

得「{{name}}」{% if bian.name %}之「{{bian.name}}」{% endif %}卦

{{main.indent}}{{main.gong}}宫:{{main.name}}{% if main.type %} ({{main.type}}){% endif %}{{bian.indent}}{% if bian.name %}{{bian.gong}}宫:{{bian.name}}{% if bian.type %} ({{bian.type}}){% endif %}{% endif %}
{{god6.5}}{{hide.qin6.5}}{{qin6.5}}{{qinx.5}} {{main.mark.5}} {{shiy.5}} {{dyao.5}} {{bian.qin6.5}} {{bian.mark.5}}
{{god6.4}}{{hide.qin6.4}}{{qin6.4}}{{qinx.4}} {{main.mark.4}} {{shiy.4}} {{dyao.4}} {{bian.qin6.4}} {{bian.mark.4}}
{{god6.3}}{{hide.qin6.3}}{{qin6.3}}{{qinx.3}} {{main.mark.3}} {{shiy.3}} {{dyao.3}} {{bian.qin6.3}} {{bian.mark.3}}
{{god6.2}}{{hide.qin6.2}}{{qin6.2}}{{qinx.2}} {{main.mark.2}} {{shiy.2}} {{dyao.2}} {{bian.qin6.2}} {{bian.mark.2}}
{{god6.1}}{{hide.qin6.1}}{{qin6.1}}{{qinx.1}} {{main.mark.1}} {{shiy.1}} {{dyao.1}} {{bian.qin6.1}} {{bian.mark.1}}
{{god6.0}}{{hide.qin6.0}}{{qin6.0}}{{qinx.0}} {{main.mark.0}} {{shiy.0}} {{dyao.0}} {{bian.qin6.0}} {{bian.mark.0}}

{% if guaci %}{{ guaci }}{% endif %}



================================================
FILE: tests/__init__.py
================================================
"""Unit test package for najia."""



================================================
FILE: tests/conftest.py
================================================
[Empty file]


================================================
FILE: tests/test_compile.py
================================================
from najia.utils import xkong


def test_compile():
    assert xkong('甲子') == '戌亥'
    assert xkong([0, 0]) == '戌亥'



================================================
FILE: tests/test_god6.py
================================================
from najia.utils import get_god6


def test_God6():
    """
    【排六神口诀】
     甲乙起青龙 丙丁起朱雀 戊日起勾陈 己日起腾蛇 庚辛起白虎 壬癸起玄武
     1,2      3,4      5         6        7,8      9,10
    :return:
    """

    assert get_god6('甲子')[0] == '青龙'
    assert get_god6('乙丑')[0] == '青龙'
    assert get_god6('丁卯')[0] == '朱雀'
    assert get_god6('丙寅')[0] == '朱雀'


def test_G60():
    assert get_god6('戊卯')[0] == '勾陈'
    assert get_god6('己酉')[0] == '螣蛇'


def test_G61():
    assert get_god6('庚辰')[0] == '白虎'
    assert get_god6('辛巳')[0] == '白虎'

    assert get_god6('壬午')[0] == '玄武'
    assert get_god6('癸未')[0] == '玄武'



================================================
FILE: tests/test_gong.py
================================================
# 六爻卦的卦宫名
from najia.utils import palace


def test_gong():
    assert palace('101101', 6) == 2
    # assert palace("101101", 6) == 2



================================================
FILE: tests/test_gua.py
================================================
from najia.utils import *


def test_qin6():
    zs = [get_qin6('火', const.ZHI5[const.ZHIS.index(x[1])]) for x in get_najia('101101')]
    assert get_qin6('金', '木') == '妻财'


def test_gong():
    assert palace('101101', 6) == 2
    # assert palace("101101", 6) == 2


def test_shen6():
    assert get_god6('戊子')[0] == '勾陈'


# def test_dong():
#     assert setDongYao('101101', 0, 2, 3, 5) == '000000'
#     assert setDongYao('101101', 3) == '101001'
#     # assert revise('101101', 3) == '101001'
#     # assert convert('101101', 3) == '101001'
#     # assert transform('101101', 3) == '101001'


def test_xkong():
    assert xkong('甲子') == '戌亥'
    # assert empty('甲子') == '戌亥'


def test_najia():
    assert get_najia('101101') == ['己卯', '己丑', '己亥', '己酉', '己未', '己巳']
    # assert trunk('101101') == ['己卯', '己丑', '己亥', '己酉', '己未', '己巳']
    # assert branch('101101') == ['己卯', '己丑', '己亥', '己酉', '己未', '己巳']
    # assert cycle('101101') == ['己卯', '己丑', '己亥', '己酉', '己未', '己巳']



================================================
FILE: tests/test_guaci.py
================================================
from najia.utils import get_guaci


def test_guaci():
    assert (get_guaci('乾为天'))



================================================
FILE: tests/test_gz5x.py
================================================
from najia.utils import GZ5X

'''
# 六亲
QING6 = ("兄弟", "父母", "官鬼", "妻财", "子孙")

# 五行
XING5 = ('木', '火', '土', '金', '水')
'''


def test_gzwx():
    assert GZ5X('甲子') == '甲子水'



================================================
FILE: tests/test_mark.py
================================================
from najia.utils import mark


def test_mark():
    symbol = [2, 2, 1, 2, 4, 2]

    assert mark(symbol) == ['0', '0', '1', '0', '0', '0']
    assert mark(''.join([str(x) for x in symbol])) == ['0', '0', '1', '0', '0', '0']



================================================
FILE: tests/test_najia.py
================================================
from najia.utils import get_najia


def test_najia():
    assert get_najia('101101') == ['己卯', '己丑', '己亥', '己酉', '己未', '己巳']



================================================
FILE: tests/test_qin6.py
================================================
from najia.utils import get_qin6

'''
# 六亲
QING6 = ("兄弟", "父母", "官鬼", "妻财", "子孙")

# 五行
XING5 = ('木', '火', '土', '金', '水')
'''


def test_qin6():
    assert get_qin6('金', '木') == '妻财'
    assert get_qin6('木', '金') == '官鬼'
    assert get_qin6('金', '水') == '子孙'
    assert get_qin6('金', '土') == '父母'
    assert get_qin6('金', '金') == '兄弟'



================================================
FILE: tests/test_xkong.py
================================================
from najia.utils import xkong

'''
KONG = ("子丑", "寅卯", "辰巳", "午未", "申酉", "戌亥")
'''


def test_xkong():
    assert xkong([0, 0]) == '戌亥'

    assert xkong('甲子') == '戌亥'
    assert xkong('甲戌') == '申酉'
    assert xkong('甲申') == '午未'
    assert xkong('甲午') == '辰巳'
    assert xkong('甲辰') == '寅卯'
    assert xkong('甲寅') == '子丑'



================================================
FILE: tests/data/dc.json
================================================
[
    [
        "\u300a\u6613\u7ecf\u300b\u7b2c\u4e00\u5366 \u4e7e \u4e7e\u4e3a\u5929 \u4e7e\u4e0a\u4e7e\u4e0b \n\u3000\u3000\u4e7e\uff1a\u5143\uff0c\u4ea8\uff0c\u5229\uff0c\u8d1e\u3002\n",
        "\n\u3000\u3000\u521d\u4e5d\uff1a\u6f5c\u9f99\uff0c\u52ff\u7528\u3002\n\u3000\u3000\u4e5d\u4e8c\uff1a\u89c1\u9f99\u518d\u7530\uff0c\u5229\u89c1\u5927\u4eba\u3002\n\u3000\u3000\u4e5d\u4e09\uff1a\u541b\u5b50\u7ec8\u65e5\u4e7e\u4e7e\uff0c\u5915\u60d5\u82e5\uff0c\u5389\u65e0\u548e\u3002\n\u3000\u3000\u4e5d\u56db\uff1a\u6216\u8dc3\u5728\u6e0a\uff0c\u65e0\u548e\u3002\n\u3000\u3000\u4e5d\u4e94\uff1a\u98de\u9f99\u5728\u5929\uff0c\u5229\u89c1\u5927\u4eba\u3002\n\u3000\u3000\u4e0a\u4e5d\uff1a\u4ea2\u9f99\u6709\u6094\u3002\n\u3000\u3000\u7528\u4e5d\uff1a\u89c1\u7fa4\u9f99\u65e0\u9996\uff0c\u5409\u3002\n"
    ],
    [
        "\n\u300a\u6613\u7ecf\u300b\u7b2c\u4e8c\u5366 \u5764 \u5764\u4e3a\u5730 \u5764\u4e0a\u5764\u4e0b \n\u5764\uff1a\u5143\uff0c\u4ea8\uff0c\u5229\u725d\u9a6c\u4e4b\u8d1e\u3002 \u541b\u5b50\u6709\u6538\u5f80\uff0c\u5148\u8ff7\u540e\u5f97\u4e3b\uff0c\u5229\u897f\u5357\u5f97\u670b\uff0c\u4e1c\u5317\u4e27\u670b\u3002 \u5b89\u8d1e\uff0c\u5409\u3002\n\u5f56\u66f0\uff1a\u81f3\u54c9\u5764\u5143\uff0c\u4e07\u7269\u8d44\u751f\uff0c\u4e43\u987a\u627f\u5929\u3002 \u5764\u539a\u8f7d\u7269\uff0c\u5fb7\u5408\u65e0\u7586\u3002 \u542b\u5f18\u5149\u5927\uff0c\u54c1\u7269\u54b8\u4ea8\u3002 \u725d\u9a6c\u5730\u7c7b\uff0c\u884c\u5730\u65e0\u7586\uff0c\u67d4\u987a\u5229\u8d1e\u3002 \u541b\u5b50\u6538\u884c\uff0c\u5148\u8ff7\u5931\u9053\uff0c\u540e\u987a\u5f97\u5e38\u3002 \u897f\u5357\u5f97\u670b\uff0c\u4e43\u4e0e\u7c7b\u884c\uff1b\u4e1c\u5317\u4e27\u670b\uff0c\u4e43\u7ec8\u6709\u5e86\u3002\u5b89\u8d1e\u4e4b\u5409\uff0c\u5e94\u5730\u65e0\u7586\u3002\n\u8c61\u66f0\uff1a\u5730\u52bf\u5764\uff0c\u541b\u5b50\u4ee5\u539a\u5fb7\u8f7d\u7269\u3002\n",
        "\n\u3000\u3000\u521d\u516d\uff1a\u5c65\u971c\uff0c\u575a\u51b0\u81f3\u3002\n\u3000\u8c61\u66f0\uff1a\u5c65\u971c\u575a\u51b0\uff0c\u9634\u59cb\u51dd\u4e5f\u3002 \u9a6f\u81f4\u5176\u9053\uff0c\u81f3\u575a\u51b0\u4e5f\u3002\n\u3000\u3000\u516d\u4e8c\uff1a\u76f4\uff0c\u65b9\uff0c\u5927\uff0c\u4e0d\u4e60\u65e0\u4e0d\u5229\u3002\n\u3000\u8c61\u66f0\uff1a\u516d\u4e8c\u4e4b\u52a8\uff0c\u76f4\u4ee5\u65b9\u4e5f\u3002 \u4e0d\u4e60\u65e0\u4e0d\u5229\uff0c\u5730\u9053\u5149\u4e5f\u3002\n\u3000\u3000\u516d\u4e09\uff1a\u542b\u7ae0\u53ef\u8d1e\u3002 \u6216\u4ece\u738b\u4e8b\uff0c\u65e0\u6210\u6709\u7ec8\u3002\n\u3000\u8c61\u66f0\uff1a\u542b\u7ae0\u53ef\u8d1e\uff1b\u4ee5\u65f6\u53d1\u4e5f\u3002 \u6216\u4ece\u738b\u4e8b\uff0c\u77e5\u5149\u5927\u4e5f\u3002\n\u3000\u3000\u516d\u56db\uff1a\u62ec\u56ca\uff1b\u65e0\u548e\uff0c\u65e0\u8a89\u3002\n\u3000\u8c61\u66f0\uff1a\u62ec\u56ca\u65e0\u548e\uff0c\u614e\u4e0d\u5bb3\u4e5f\u3002\n\u3000\u3000\u516d\u4e94\uff1a\u9ec4\u88f3\uff0c\u5143\u5409\u3002\n\u3000\u8c61\u66f0\uff1a\u9ec4\u88f3\u5143\u5409\uff0c\u6587\u5728\u4e2d\u4e5f\u3002\n\u3000\u3000\u4e0a\u516d\uff1a\u6218\u9f99\u65bc\u91ce\uff0c\u5176\u8840\u7384\u9ec4\u3002\n\u3000\u8c61\u66f0\uff1a\u6218\u9f99\u65bc\u91ce\uff0c\u5176\u9053\u7a77\u4e5f\u3002\n\u3000\u3000\u7528\u516d\uff1a\u5229\u6c38\u8d1e\u3002\n\u3000\u8c61\u66f0\uff1a\u7528\u516d\u6c38\u8d1e\uff0c\u4ee5\u5927\u7ec8\u4e5f\u3002\n"
    ],
    [
        "\n\u300a\u6613\u7ecf\u300b\u7b2c\u4e09\u5366 \u5c6f \u6c34\u96f7\u5c6f \u574e\u4e0a\u9707\u4e0b\n\u5c6f\uff1a\u5143\uff0c\u4ea8\uff0c\u5229\uff0c\u8d1e\uff0c\u52ff\u7528\uff0c\u6709\u6538\u5f80\uff0c\u5229\u5efa\u4faf\u3002\n\u5f56\u66f0\uff1a\u5c6f\uff0c\u521a\u67d4\u59cb\u4ea4\u800c\u96be\u751f\uff0c\u52a8\u4e4e\u9669\u4e2d\uff0c\u5927\u4ea8\u8d1e\u3002\u96f7\u96e8\u4e4b\u52a8\u6ee1\u76c8\uff0c\u5929\u9020\u8349\u6627\uff0c\u5b9c\u5efa\u4faf\u800c\u4e0d\u5b81\u3002 \n\u8c61\u66f0\uff1a\u4e91\uff0c\u96f7\uff0c\u5c6f\uff1b\u541b\u5b50\u4ee5\u7ecf\u7eb6\u3002\n",
        "\n\u3000\u3000\u521d\u4e5d\uff1a\u78d0\u6853\uff1b\u5229\u5c45\u8d1e\uff0c\u5229\u5efa\u4faf\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u867d\u78d0\u6853\uff0c\u5fd7\u884c\u6b63\u4e5f\u3002 \u4ee5\u8d35\u4e0b\u8d31\uff0c\u5927\u5f97\u6c11\u4e5f\u3002\n\u3000\u3000\u516d\u4e8c\uff1a\u5c6f\u5982\u9085\u5982\uff0c\u4e58\u9a6c\u73ed\u5982\u3002 \u532a\u5bc7\u5a5a\u5abe\uff0c\u5973\u5b50\u8d1e\u4e0d\u5b57\uff0c\u5341\u5e74\u4e43\u5b57\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u516d\u4e8c\u4e4b\u96be\uff0c\u4e58\u521a\u4e5f\u3002 \u5341\u5e74\u4e43\u5b57\uff0c\u53cd\u5e38\u4e5f\u3002\n\u3000\u3000\u516d\u4e09\uff1a\u65e2\u9e7f\u65e0\u865e\uff0c\u60df\u5165\u4e8e\u6797\u4e2d\uff0c\u541b\u5b50\u51e0\u4e0d\u5982\u820d\uff0c\u5f80\u541d\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u65e2\u9e7f\u65e0\u865e\uff0c\u4ee5\u7eb5\u79bd\u4e5f\u3002 \u541b\u5b50\u820d\u4e4b\uff0c\u5f80\u541d\u7a77\u4e5f\u3002\n\u3000\u3000\u516d\u56db\uff1a\u4e58\u9a6c\u73ed\u5982\uff0c\u6c42\u5a5a\u5abe\uff0c\u65e0\u4e0d\u5229\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u6c42\u800c\u5f80\uff0c\u660e\u4e5f\u3002\n\u3000  \u4e5d\u4e94\uff1a\u5c6f\u5176\u818f\uff0c\u5c0f\u8d1e\u5409\uff0c\u5927\u8d1e\u51f6\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5c6f\u5176\u818f\uff0c\u65bd\u672a\u5149\u4e5f\u3002\n\u3000\u3000\u4e0a\u516d\uff1a\u4e58\u9a6c\u73ed\u5982\uff0c\u6ce3\u8840\u6d9f\u5982\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u6ce3\u8840\u6d9f\u5982\uff0c\u4f55\u53ef\u957f\u4e5f\u3002\n"
    ],
    [
        "\n\u300a\u6613\u7ecf\u300b\u7b2c\u56db\u5366 \u8499 \u5c71\u6c34\u8499 \u826e\u4e0a\u574e\u4e0b \u3000\u3000\n\u8499\uff1a\u4ea8\u3002 \u532a\u6211\u6c42\u7ae5\u8499\uff0c\u7ae5\u8499\u6c42\u6211\u3002 \u521d\u566c\u544a\uff0c\u518d\u4e09\u6e0e\uff0c\u6e0e\u5219\u4e0d\u544a\u3002\u5229\u8d1e\u3002\n\u5f56\u66f0\uff1a\u8499\uff0c\u5c71\u4e0b\u6709\u9669\uff0c\u9669\u800c\u6b62\uff0c\u8499\u3002 \u8499\u4ea8\uff0c\u4ee5\u4ea8\u884c\u65f6\u4e2d\u4e5f\u3002\u532a\u6211\u6c42\u7ae5\u8499\uff0c\u7ae5\u8499\u6c42\u6211\uff0c\u5fd7\u5e94\u4e5f\u3002 \u521d\u566c\u544a\uff0c\u4ee5\u521a\u4e2d\u4e5f\u3002\u518d\u4e09\u6e0e\uff0c \u6e0e\u5219\u4e0d\u544a\uff0c\u6e0e\u8499\u4e5f\u3002 \u8499\u4ee5\u517b\u6b63\uff0c\u5723\u529f\u4e5f\u3002\n\u8c61\u66f0\uff1a\u5c71\u4e0b\u51fa\u6cc9\uff0c\u8499\uff1b\u541b\u5b50\u4ee5\u679c\u884c\u80b2\u5fb7\u3002\n",
        "\n\u3000\u3000\u521d\u516d\uff1a\u53d1\u8499\uff0c\u5229\u7528\u5211\u4eba\uff0c\u7528\u8bf4\u684e\u688f\uff0c\u4ee5\u5f80\u541d\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5229\u7528\u5211\u4eba\uff0c\u4ee5\u6b63\u6cd5\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e8c\uff1a\u5305\u8499\u5409\uff1b\u7eb3\u5987\u5409\uff1b\u5b50\u514b\u5bb6\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5b50\u514b\u5bb6\uff0c\u521a\u67d4\u63a5\u4e5f\u3002\n\u3000\u3000\u516d\u4e09\uff1a\u52ff\u7528\u5a36\u5973\uff1b\u89c1\u91d1\u592b\uff0c\u4e0d\u6709\u8eac\uff0c\u65e0\u6538\u5229\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u52ff\u7528\u5a36\u5973\uff0c\u884c\u4e0d\u987a\u4e5f\u3002\n\u3000\u3000\u516d\u56db\uff1a\u56f0\u8499\uff0c\u541d\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u56f0\u8499\u4e4b\u541d\uff0c\u72ec\u8fdc\u5b9e\u4e5f\u3002\n\u3000\u3000\u516d\u4e94\uff1a\u7ae5\u8499\uff0c\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u7ae5\u8499\u4e4b\u5409\uff0c\u987a\u4ee5\u5dfd\u4e5f\u3002\n\u3000\u3000\u4e0a\u4e5d\uff1a\u51fb\u8499\uff1b\u4e0d\u5229\u4e3a\u5bc7\uff0c\u5229\u5fa1\u5bc7\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5229\u7528\u5fa1\u5bc7\uff0c\u4e0a\u4e0b\u987a\u4e5f\u3002\n"
    ],
    [
        "\n\u300a\u6613\u7ecf\u300b\u7b2c\u4e94\u5366 \u9700 \u6c34\u5929\u9700 \u574e\u4e0a\u4e7e\u4e0b \n\u3000\u3000\u9700\uff1a\u6709\u5b5a\uff0c\u5149\u4ea8\uff0c\u8d1e\u5409\u3002 \u5229\u6d89\u5927\u5ddd\u3002\n\u5f56\u66f0\uff1a\u9700\uff0c\u987b\u4e5f\uff1b\u9669\u5728\u524d\u4e5f\u3002 \u521a\u5065\u800c\u4e0d\u9677\uff0c\u5176\u4e49\u4e0d\u56f0\u7a77\u77e3\u3002 \u9700\u6709\u5b5a\uff0c\u5149\u4ea8\uff0c\u8d1e\u5409\u3002 \u4f4d\u4e4e\u5929\u4f4d\uff0c\u4ee5\u6b63\u4e2d\u4e5f\u3002 \u5229\u6d89\u5927\u5ddd\uff0c\u5f80\u6709\u529f\u4e5f\u3002\n\u8c61\u66f0\uff1a\u4e91\u4e0a\u65bc\u5929\uff0c\u9700\uff1b\u541b\u5b50\u4ee5\u996e\u98df\u5bb4\u4e50\u3002\n",
        "\n\u3000\u3000\u521d\u4e5d\uff1a\u9700\u4e8e\u90ca\u3002 \u5229\u7528\u6052\uff0c\u65e0\u548e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u9700\u4e8e\u90ca\uff0c\u4e0d\u72af\u96be\u884c\u4e5f\u3002 \u5229\u7528\u6052\uff0c\u65e0\u548e\uff1b\u672a\u5931\u5e38\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e8c\uff1a\u9700\u4e8e\u6c99\u3002 \u5c0f\u6709\u8a00\uff0c\u7ec8\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u9700\u4e8e\u6c99\uff0c\u884d\u5728\u4e2d\u4e5f\u3002 \u867d\u5c0f\u6709\u8a00\uff0c\u4ee5\u7ec8\u5409\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e09\uff1a\u9700\u4e8e\u6ce5\uff0c\u81f4\u5bc7\u81f3\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u9700\u4e8e\u6ce5\uff0c\u707e\u5728\u5916\u4e5f\u3002 \u81ea\u6211\u81f4\u5bc7\uff0c\u656c\u614e\u4e0d\u8d25\u4e5f\u3002\n\u3000\u3000\u516d\u56db\uff1a\u9700\u4e8e\u8840\uff0c\u51fa\u81ea\u7a74\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u9700\u4e8e\u8840\uff0c\u987a\u4ee5\u542c\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e94\uff1a\u9700\u4e8e\u9152\u98df\uff0c\u8d1e\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u9152\u98df\u8d1e\u5409\uff0c\u4ee5\u4e2d\u6b63\u4e5f\u3002\n\u3000\u3000\u4e0a\u516d\uff1a\u5165\u4e8e\u7a74\uff0c\u6709\u4e0d\u901f\u4e4b\u5ba2\u4e09\u4eba\u6765\uff0c\u656c\u4e4b\u7ec8\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u4e0d\u901f\u4e4b\u5ba2\u6765\uff0c\u656c\u4e4b\u7ec8\u5409\u3002 \u867d\u4e0d\u5f53\u4f4d\uff0c\u672a\u5927\u5931\u4e5f\u3002\n"
    ],
    [
        "\n\u300a\u6613\u7ecf\u300b\u7b2c\u516d\u5366 \u8bbc \u5929\u6c34\u8bbc \u4e7e\u4e0a\u574e\u4e0b \n\u3000\u3000\u8bbc\uff1a\u6709\u5b5a\uff0c\u7a92\u3002 \u60d5\u4e2d\u5409\u3002 \u7ec8\u51f6\u3002 \u5229\u89c1\u5927\u4eba\uff0c\u4e0d\u5229\u6d89\u5927\u5ddd\u3002\n\u5f56\u66f0\uff1a\u8bbc\uff0c\u4e0a\u521a\u4e0b\u9669\uff0c\u9669\u800c\u5065\u8bbc\u3002\u8bbc\u6709\u5b5a\u7a92\uff0c\u60d5\u4e2d\u5409\uff0c\u521a\u6765\u800c\u5f97\u4e2d\u4e5f\u3002\u7ec8\u51f6\uff1b\u8bbc\u4e0d\u53ef\u6210\u4e5f\u3002 \u5229\u89c1\u5927\u4eba\uff1b\u5c1a\u4e2d\u6b63\u4e5f\u3002\u4e0d\u5229\u6d89\u5927\u5ddd\uff1b\u5165\u4e8e\u6e0a\u4e5f\u3002\n\u8c61\u66f0\uff1a\u5929\u4e0e\u6c34\u8fdd\u884c\uff0c\u8bbc\uff1b\u541b\u5b50\u4ee5\u4f5c\u4e8b\u8c0b\u59cb\u3002\n",
        "\n\u3000\u3000\u521d\u516d\uff1a\u4e0d\u6c38\u6240\u4e8b\uff0c\u5c0f\u6709\u8a00\uff0c\u7ec8\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u4e0d\u6c38\u6240\u4e8b\uff0c\u8bbc\u4e0d\u53ef\u957f\u4e5f\u3002 \u867d\u6709\u5c0f\u8a00\uff0c\u5176\u8fa9\u660e\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e8c\uff1a\u4e0d\u514b\u8bbc\uff0c\u5f52\u800c\u900b\uff0c\u5176\u9091\u4eba\u4e09\u767e\u6237\uff0c\u65e0\u771a\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u4e0d\u514b\u8bbc\uff0c\u5f52\u800c\u900b\u4e5f\u3002 \u81ea\u4e0b\u8bbc\u4e0a\uff0c\u60a3\u81f3\u6387\u4e5f\u3002\n\u3000\u3000\u516d\u4e09\uff1a\u98df\u65e7\u5fb7\uff0c\u8d1e\u5389\uff0c\u7ec8\u5409\uff0c\u6216\u4ece\u738b\u4e8b\uff0c\u65e0\u6210\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u98df\u65e7\u5fb7\uff0c\u4ece\u4e0a\u5409\u4e5f\u3002\n\u3000\u3000\u4e5d\u56db\uff1a\u4e0d\u514b\u8bbc\uff0c\u590d\u81ea\u547d\uff0c\u6e1d\u5b89\u8d1e\uff0c\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u590d\u5373\u547d\uff0c\u6e1d\u5b89\u8d1e\uff1b\u4e0d\u5931\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e94\uff1a\u8bbc\u5143\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u8bbc\u5143\u5409\uff0c\u4ee5\u4e2d\u6b63\u4e5f\u3002\n\u3000\u3000\u4e0a\u4e5d\uff1a\u6216\u9521\u4e4b\u97b6\u5e26\uff0c\u7ec8\u671d\u4e09\u892b\u4e4b\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u4ee5\u8bbc\u53d7\u670d\uff0c\u4ea6\u4e0d\u8db3\u656c\u4e5f\u3002\n"
    ],
    [
        "\n\u300a\u6613\u7ecf\u300b\u7b2c\u4e03\u5366 \u5e08 \u5730\u6c34\u5e08 \u5764\u4e0a\u574e\u4e0b \u3000\u3000\n\u5e08\uff1a\u8d1e\uff0c\u4e08\u4eba\uff0c\u5409\u65e0\u548e\u3002\n\u5f56\u66f0\uff1a\u5e08\uff0c\u4f17\u4e5f\uff0c\u8d1e\u6b63\u4e5f\uff0c\u80fd\u4ee5\u4f17\u6b63\uff0c\u53ef\u4ee5\u738b\u77e3\u3002 \u521a\u4e2d\u800c\u5e94\uff0c\u884c\u9669\u800c\u987a\uff0c\u4ee5\u6b64\u6bd2\u5929\u4e0b\uff0c\u800c\u6c11\u4ece\u4e4b\uff0c\u5409\u53c8\u4f55\u548e\u77e3\u3002\n\u8c61\u66f0\uff1a\u5730\u4e2d\u6709\u6c34\uff0c\u5e08\uff1b\u541b\u5b50\u4ee5\u5bb9\u6c11\u755c\u4f17\u3002\n",
        "\n\u3000\u3000\u521d\u516d\uff1a\u5e08\u51fa\u4ee5\u5f8b\uff0c\u5426\u81e7\u51f6\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5e08\u51fa\u4ee5\u5f8b\uff0c\u5931\u5f8b\u51f6\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e8c\uff1a\u5728\u5e08\u4e2d\uff0c\u5409\u65e0\u548e\uff0c\u738b\u4e09\u9521\u547d\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5728\u5e08\u4e2d\u5409\uff0c\u627f\u5929\u5ba0\u4e5f\u3002 \u738b\u4e09\u9521\u547d\uff0c\u6000\u4e07\u90a6\u4e5f\u3002\n\u3000\u3000\u516d\u4e09\uff1a\u5e08\u6216\u8206\u5c38\uff0c\u51f6\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5e08\u6216\u8206\u5c38\uff0c\u5927\u65e0\u529f\u4e5f\u3002\n\u3000\u3000\u516d\u56db\uff1a\u5e08\u5de6\u6b21\uff0c\u65e0\u548e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5de6\u6b21\u65e0\u548e\uff0c\u672a\u5931\u5e38\u4e5f\u3002\n\u3000\u3000\u516d\u4e94\uff1a\u7530\u6709\u79bd\uff0c\u5229\u6267\u8a00\uff0c\u65e0\u548e\u3002\u957f\u5b50\u5e05\u5e08\uff0c\u5f1f\u5b50\u8206\u5c38\uff0c\u8d1e\u51f6\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u957f\u5b50\u5e05\u5e08\uff0c\u4ee5\u4e2d\u884c\u4e5f\u3002\u5f1f\u5b50\u8206\u5e08\uff0c\u4f7f\u4e0d\u5f53\u4e5f\u3002\n\u3000\u3000\u4e0a\u516d\uff1a\u5927\u541b\u6709\u547d\uff0c\u5f00\u56fd\u627f\u5bb6\uff0c\u5c0f\u4eba\u52ff\u7528\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5927\u541b\u6709\u547d\uff0c\u4ee5\u6b63\u529f\u4e5f\u3002 \u5c0f\u4eba\u52ff\u7528\uff0c\u5fc5\u4e71\u90a6\u4e5f\u3002\n"
    ],
    [
        "\n\u300a\u6613\u7ecf\u300b\u7b2c\u516b\u5366 \u6bd4 \u6c34\u5730\u6bd4 \u574e\u4e0a\u4e0b\u5764 \u3000\u3000\n\u6bd4\uff1a\u5409\u3002 \u539f\u7b6e\u5143\u6c38\u8d1e\uff0c\u65e0\u548e\u3002 \u4e0d\u5b81\u65b9\u6765\uff0c\u540e\u592b\u51f6\u3002\n\u5f56\u66f0\uff1a\u6bd4\uff0c\u5409\u4e5f\uff0c\u6bd4\uff0c\u8f85\u4e5f\uff0c\u4e0b\u987a\u4ece\u4e5f\u3002 \u539f\u7b6e\u5143\u6c38\u8d1e\uff0c\u65e0\u548e\uff0c\u4ee5\u521a\u4e2d\u4e5f\u3002\u4e0d\u5b81\u65b9\u6765\uff0c\u4e0a\u4e0b\u5e94\u4e5f\u3002 \u540e\u592b\u51f6\uff0c\u5176\u9053\u7a77\u4e5f\u3002\n\u8c61\u66f0\uff1a\u5730\u4e0a\u6709\u6c34\uff0c\u6bd4\uff1b\u5148\u738b\u4ee5\u5efa\u4e07\u56fd\uff0c\u4eb2\u8bf8\u4faf\u3002\n",
        "\n\u3000\u3000\u521d\u516d\uff1a\u6709\u5b5a\u6bd4\u4e4b\uff0c\u65e0\u548e\u3002 \u6709\u5b5a\u76c8\u7f36\uff0c\u7ec8\u6765\u6709\u4ed6\uff0c\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u6bd4\u4e4b\u521d\u516d\uff0c\u6709\u4ed6\u5409\u4e5f\u3002\n\u3000\u3000\u516d\u4e8c\uff1a\u6bd4\u4e4b\u81ea\u5185\uff0c\u8d1e\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u6bd4\u4e4b\u81ea\u5185\uff0c\u4e0d\u81ea\u5931\u4e5f\u3002\n\u3000\u3000\u516d\u4e09\uff1a\u6bd4\u4e4b\u532a\u4eba\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u6bd4\u4e4b\u532a\u4eba\uff0c\u4e0d\u4ea6\u4f24\u4e4e\uff01\n\u3000\u3000\u516d\u56db\uff1a\u5916\u6bd4\u4e4b\uff0c\u8d1e\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5916\u6bd4\u65bc\u8d24\uff0c\u4ee5\u4ece\u4e0a\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e94\uff1a\u663e\u6bd4\uff0c\u738b\u7528\u4e09\u9a71\uff0c\u5931\u524d\u79bd\u3002 \u9091\u4eba\u4e0d\u8beb\uff0c\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u663e\u6bd4\u4e4b\u5409\uff0c\u4f4d\u6b63\u4e2d\u4e5f\u3002\u820d\u9006\u53d6\u987a\uff0c\u5931\u524d\u79bd\u4e5f\u3002 \u9091\u4eba\u4e0d\u8beb\uff0c\u4e0a\u4f7f\u4e2d\u4e5f\u3002\n\u3000\u3000\u4e0a\u516d\uff1a\u6bd4\u4e4b\u65e0\u9996\uff0c\u51f6\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u6bd4\u4e4b\u65e0\u9996\uff0c\u65e0\u6240\u7ec8\u4e5f\u3002\n"
    ],
    [
        "\n\u300a\u6613\u7ecf\u300b\u7b2c\u4e5d\u5366 \u5c0f\u755c \u98ce\u5929\u5c0f\u755c \u5dfd\u4e0a\u4e7e\u4e0b \n\u3000\u3000\u5c0f\u755c\uff1a\u4ea8\u3002 \u5bc6\u4e91\u4e0d\u96e8\uff0c\u81ea\u6211\u897f\u90ca\u3002\n\u5f56\u66f0\uff1a\u5c0f\u755c\uff1b \u67d4\u5f97\u4f4d\uff0c\u800c\u4e0a\u4e0b\u5e94\u4e4b\uff0c\u66f0\u5c0f\u755c\u3002 \u5065\u800c\u5dfd\uff0c\u521a\u4e2d\u800c\u5fd7\u884c\uff0c\u4e43\u4ea8\u3002 \u5bc6\u4e91\u4e0d\u96e8\uff0c\u5c1a\u5f80\u4e5f\u3002 \u81ea\u6211\u897f\u90ca\uff0c\u65bd\u672a\u884c\u4e5f\u3002\n\u8c61\u66f0\uff1a\u98ce\u884c\u5929\u4e0a\uff0c\u5c0f\u755c\uff1b\u541b\u5b50\u4ee5\u61ff\u6587\u5fb7\u3002\n",
        "\n\u3000\u3000\u521d\u4e5d\uff1a\u590d\u81ea\u9053\uff0c\u4f55\u5176\u548e\uff0c\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u590d\u81ea\u9053\uff0c\u5176\u4e49\u5409\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e8c\uff1a\u7275\u590d\uff0c\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u7275\u590d\u5728\u4e2d\uff0c\u4ea6\u4e0d\u81ea\u5931\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e09\uff1a\u8206\u8bf4\u8f90\uff0c\u592b\u59bb\u53cd\u76ee\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u592b\u59bb\u53cd\u76ee\uff0c\u4e0d\u80fd\u6b63\u5ba4\u4e5f\u3002\n\u3000\u3000\u516d\u56db\uff1a\u6709\u5b5a\uff0c\u8840\u53bb\u60d5\u51fa\uff0c\u65e0\u548e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u6709\u5b5a\u60d5\u51fa\uff0c\u4e0a\u5408\u5fd7\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e94\uff1a\u6709\u5b5a\u631b\u5982\uff0c\u5bcc\u4ee5\u5176\u90bb\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u6709\u5b5a\u631b\u5982\uff0c\u4e0d\u72ec\u5bcc\u4e5f\u3002\n\u3000\u3000\u4e0a\u4e5d\uff1a\u65e2\u96e8\u65e2\u5904\uff0c\u5c1a\u5fb7\u8f7d\uff0c\u5987\u8d1e\u5389\u3002 \u6708\u51e0\u671b\uff0c\u541b\u5b50\u5f81\u51f6\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u65e2\u96e8\u65e2\u5904\uff0c\u5fb7\u79ef\u8f7d\u4e5f\u3002 \u541b\u5b50\u5f81\u51f6\uff0c\u6709\u6240\u7591\u4e5f\u3002\n"
    ],
    [
        "\n\u300a\u6613\u7ecf\u300b\u7b2c\u5341\u5366 \u5c65 \u5929\u6cfd\u5c65 \u4e7e\u4e0a\u5151\u4e0b \u3000\u3000\n\u5c65\uff1a\u5c65\u864e\u5c3e\uff0c\u4e0d\u54a5\u4eba\uff0c\u4ea8\u3002\n\u5f56\u66f0\uff1a\u5c65\uff0c\u67d4\u5c65\u521a\u4e5f\u3002\u8bf4\u800c\u5e94\u4e4e\u4e7e\uff0c\u662f\u4ee5\u5c65\u864e\u5c3e\uff0c\u4e0d\u54a5\u4eba\uff0c\u4ea8\u3002\u521a\u4e2d\u6b63\uff0c\u5c65\u5e1d\u4f4d\u800c\u4e0d\u759a\uff0c\u5149\u660e\u4e5f\u3002\n\u8c61\u66f0\uff1a\u4e0a\u5929\u4e0b\u6cfd\uff0c\u5c65\uff1b\u541b\u5b50\u4ee5\u8fa8\u4e0a\u4e0b\uff0c\u5b89\u6c11\u5fd7\u3002\n",
        "\n\u3000\u3000\u521d\u4e5d\uff1a\u7d20\u5c65\uff0c\u5f80\u65e0\u548e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u7d20\u5c65\u4e4b\u5f80\uff0c\u72ec\u884c\u613f\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e8c\uff1a\u5c65\u9053\u5766\u5766\uff0c\u5e7d\u4eba\u8d1e\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5e7d\u4eba\u8d1e\u5409\uff0c\u4e2d\u4e0d\u81ea\u4e71\u4e5f\u3002\n\u3000\u3000\u516d\u4e09\uff1a\u7707\u80fd\u89c6\uff0c\u8ddb\u80fd\u5c65\uff0c\u5c65\u864e\u5c3e\uff0c\u54a5\u4eba\uff0c\u51f6\u3002 \u6b66\u4eba\u4e3a\u4e8e\u5927\u541b\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u7707\u80fd\u89c6\uff1b\u4e0d\u8db3\u4ee5\u6709\u660e\u4e5f\u3002\u8ddb\u80fd\u5c65\uff1b\u4e0d\u8db3\u4ee5\u4e0e\u884c\u4e5f\u3002\u54a5\u4eba\u4e4b\u51f6\uff1b\u4f4d\u4e0d\u5f53\u4e5f\u3002 \u6b66\u4eba\u4e3a\u4e8e\u5927\u541b\uff1b\u5fd7\u521a\u4e5f\u3002\n\u3000\u3000\u4e5d\u56db\uff1a\u5c65\u864e\u5c3e\uff0c\u612c\u612c\u7ec8\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u612c\u612c\u7ec8\u5409\uff0c\u5fd7\u884c\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e94\uff1a\u592c\u5c65\uff0c\u8d1e\u5389\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u592c\u5c65\u8d1e\u5389\uff0c\u4f4d\u6b63\u5f53\u4e5f\u3002\n\u3000\u3000\u4e0a\u4e5d\uff1a\u89c6\u5c65\u8003\u7965\uff0c\u5176\u65cb\u5143\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5143\u5409\u5728\u4e0a\uff0c\u5927\u6709\u5e86\u4e5f\u3002\n"
    ],
    [
        "\n\u300a\u6613\u7ecf\u300b\u7b2c\u5341\u4e00\u5366 \u6cf0 \u5730\u5929\u6cf0 \u5764\u4e0a\u4e7e\u4e0b \n\u3000\u3000\u6cf0\uff1a\u5c0f\u5f80\u5927\u6765\uff0c\u5409\u4ea8\u3002\n\u5f56\u66f0\uff1a\u6cf0\uff0c\u5c0f\u5f80\u5927\u6765\uff0c\u5409\u4ea8\u3002\u5219\u662f\u5929\u5730\u4ea4\uff0c\u800c\u4e07\u7269\u901a\u4e5f\uff1b\u4e0a\u4e0b\u4ea4\uff0c\u800c\u5176\u5fd7\u540c\u4e5f\u3002\u5185\u9633\u800c\u5916\u9634\uff0c\u5185\u5065\u800c\u5916\u987a\uff0c\u5185\u541b\u5b50\u800c\u5916\u5c0f\u4eba\uff0c\u541b\u5b50\u9053\u957f\uff0c\u5c0f\u4eba\u9053\u6d88\u4e5f\u3002\n\u8c61\u66f0\uff1a\u5929\u5730\u4ea4\u6cf0\uff0c\u540e\u4ee5\u8d22\uff08\u88c1\uff09\u6210\u5929\u5730\u4e4b\u9053\uff0c\u8f85\u76f8\u5929\u5730\u4e4b\u5b9c\uff0c\u4ee5\u5de6\u53f3\u6c11\u3002\n",
        "\n\u3000\u3000\u521d\u4e5d\uff1a\u62d4\u8305\u8339\uff0c\u4ee5\u5176\u5924\uff0c\u5f81\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u62d4\u8305\u5f81\u5409\uff0c\u5fd7\u5728\u5916\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e8c\uff1a\u5305\u8352\uff0c\u7528\u51af\u6cb3\uff0c\u4e0d\u9050\u9057\uff0c\u670b\u4ea1\uff0c\u5f97\u5c1a\u4e8e\u4e2d\u884c\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5305\u8352\uff0c\u5f97\u5c1a\u4e8e\u4e2d\u884c\uff0c\u4ee5\u5149\u5927\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e09\uff1a\u65e0\u5e73\u4e0d\u9642\uff0c\u65e0\u5f80\u4e0d\u590d\uff0c\u8270\u8d1e\u65e0\u548e\u3002 \u52ff\u6064\u5176\u5b5a\uff0c\u4e8e\u98df\u6709\u798f\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u65e0\u5f80\u4e0d\u590d\uff0c\u5929\u5730\u9645\u4e5f\u3002\n\u3000\u3000\u516d\u56db\uff1a\u7fe9\u7fe9\u4e0d\u5bcc\uff0c\u4ee5\u5176\u90bb\uff0c\u4e0d\u6212\u4ee5\u5b5a\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u7fe9\u7fe9\u4e0d\u5bcc\uff0c\u7686\u5931\u5b9e\u4e5f\u3002 \u4e0d\u6212\u4ee5\u5b5a\uff0c\u4e2d\u5fc3\u613f\u4e5f\u3002\n\u3000\u3000\u516d\u4e94\uff1a\u5e1d\u4e59\u5f52\u59b9\uff0c\u4ee5\u7949\u5143\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u4ee5\u7949\u5143\u5409\uff0c\u4e2d\u4ee5\u884c\u613f\u4e5f\u3002\n\u3000\u3000\u4e0a\u516d\uff1a\u57ce\u590d\u4e8e\u968d\uff0c\u52ff\u7528\u5e08\u3002 \u81ea\u9091\u544a\u547d\uff0c\u8d1e\u541d\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u57ce\u590d\u4e8e\u968d\uff0c\u5176\u547d\u4e71\u4e5f\u3002\n"
    ],
    [
        "\n\u300a\u6613\u7ecf\u300b\u7b2c\u5341\u4e8c\u5366 \u5426 \u5929\u5730\u5426 \u4e7e\u4e0a\u5764\u4e0b \n\u3000\u3000\u5426\uff1a\u5426\u4e4b\u532a\u4eba\uff0c\u4e0d\u5229\u541b\u5b50\u8d1e\uff0c\u5927\u5f80\u5c0f\u6765\u3002\n\u5f56\u66f0\uff1a\u5426\u4e4b\u532a\u4eba\uff0c\u4e0d\u5229\u541b\u5b50\u8d1e\u3002 \u5927\u5f80\u5c0f\u6765\uff0c \u5219\u662f\u5929\u5730\u4e0d\u4ea4\uff0c\u800c\u4e07\u7269\u4e0d\u901a\u4e5f\uff1b\u4e0a\u4e0b\u4e0d\u4ea4\uff0c\u800c\u5929\u4e0b\u65e0\u90a6\u4e5f\u3002\u5185\u9634\u800c\u5916\u9633\uff0c\u5185\u67d4\u800c\u5916\u521a\uff0c\u5185\u5c0f\u4eba\u800c\u5916\u541b\u5b50\u3002 \u5c0f\u4eba\u9053\u957f\uff0c\u541b\u5b50\u9053\u6d88\u4e5f\u3002\n\u8c61\u66f0\uff1a\u5929\u5730\u4e0d\u4ea4\uff0c\u5426\uff1b\u541b\u5b50\u4ee5\u4fed\u5fb7\u8f9f\u96be\uff0c\u4e0d\u53ef\u8363\u4ee5\u7984\u3002\n",
        "\n\u3000\u3000\u521d\u516d\uff1a\u62d4\u8305\u8339\uff0c\u4ee5\u5176\u5924\uff0c\u8d1e\u5409\u4ea8\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u62d4\u8305\u8d1e\u5409\uff0c\u5fd7\u5728\u541b\u4e5f\u3002\n\u3000\u3000\u516d\u4e8c\uff1a\u5305\u627f\u3002 \u5c0f\u4eba\u5409\uff0c\u5927\u4eba\u5426\u4ea8\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5927\u4eba\u5426\u4ea8\uff0c\u4e0d\u4e71\u7fa4\u4e5f\u3002\n\u3000\u3000\u516d\u4e09\uff1a\u5305\u7f9e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5305\u7f9e\uff0c\u4f4d\u4e0d\u5f53\u4e5f\u3002\n\u3000\u3000\u4e5d\u56db\uff1a\u6709\u547d\u65e0\u548e\uff0c\u7574\u79bb\u7949\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u6709\u547d\u65e0\u548e\uff0c\u5fd7\u884c\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e94\uff1a\u4f11\u5426\uff0c\u5927\u4eba\u5409\u3002 \u5176\u4ea1\u5176\u4ea1\uff0c\u7cfb\u4e8e\u82de\u6851\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5927\u4eba\u4e4b\u5409\uff0c\u4f4d\u6b63\u5f53\u4e5f\u3002\n\u3000\u3000\u4e0a\u4e5d\uff1a\u503e\u5426\uff0c\u5148\u5426\u540e\u559c\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5426\u7ec8\u5219\u503e\uff0c\u4f55\u53ef\u957f\u4e5f\u3002\n"
    ],
    [
        "\n\u300a\u6613\u7ecf\u300b\u7b2c\u5341\u4e09\u5366 \u540c\u4eba \u5929\u706b\u540c\u4eba \u4e7e\u4e0a\u79bb\u4e0b \u3000\u3000\n\u540c\u4eba\uff1a\u540c\u4eba\u4e8e\u91ce\uff0c\u4ea8\u3002 \u5229\u6d89\u5927\u5ddd\uff0c\u5229\u541b\u5b50\u8d1e\u3002\n\u5f56\u66f0\uff1a\u540c\u4eba\uff0c\u67d4\u5f97\u4f4d\u5f97\u4e2d\uff0c\u800c\u5e94\u4e4e\u4e7e\uff0c\u66f0\u540c\u4eba\u3002 \u540c\u4eba\u66f0\uff0c\u540c\u4eba\u4e8e\u91ce\uff0c\u4ea8\u3002\u5229\u6d89\u5927\u5ddd\uff0c\u4e7e\u884c\u4e5f\u3002 \u6587\u660e\u4ee5\u5065\uff0c\u4e2d\u6b63\u800c\u5e94\uff0c\u541b\u5b50\u6b63\u4e5f\u3002 \u552f\u541b\u5b50\u4e3a\u80fd\u901a\u5929\u4e0b\u4e4b\u5fd7\u3002\n\u8c61\u66f0\uff1a\u5929\u4e0e\u706b\uff0c\u540c\u4eba\uff1b\u541b\u5b50\u4ee5\u7c7b\u65cf\u8fa8\u7269\u3002\n",
        "\n\u3000\u3000\u521d\u4e5d\uff1a\u540c\u4eba\u4e8e\u95e8\uff0c\u65e0\u548e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u51fa\u95e8\u540c\u4eba\uff0c\u53c8\u8c01\u548e\u4e5f\u3002\n\u3000\u3000\u516d\u4e8c\uff1a\u540c\u4eba\u4e8e\u5b97\uff0c\u541d\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u540c\u4eba\u4e8e\u5b97\uff0c\u541d\u9053\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e09\uff1a\u4f0f\u620e\u4e8e\u83bd\uff0c\u5347\u5176\u9ad8\u9675\uff0c\u4e09\u5c81\u4e0d\u5174\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u4f0f\u620e\u4e8e\u83bd\uff0c\u654c\u521a\u4e5f\u3002 \u4e09\u5c81\u4e0d\u5174\uff0c\u5b89\u884c\u4e5f\u3002\n\u3000\u3000\u4e5d\u56db\uff1a\u4e58\u5176\u5889\uff0c\u5f17\u514b\u653b\uff0c\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u4e58\u5176\u5889\uff0c\u4e49\u5f17\u514b\u4e5f\uff0c\u5176\u5409\uff0c\u5219\u56f0\u800c\u53cd\u5219\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e94\uff1a\u540c\u4eba\uff0c\u5148\u53f7\u5555\u800c\u540e\u7b11\u3002 \u5927\u5e08\u514b\u76f8\u9047\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u540c\u4eba\u4e4b\u5148\uff0c\u4ee5\u4e2d\u76f4\u4e5f\u3002 \u5927\u5e08\u76f8\u9047\uff0c\u8a00\u76f8\u514b\u4e5f\u3002\n\u3000\u3000\u4e0a\u4e5d\uff1a\u540c\u4eba\u4e8e\u90ca\uff0c\u65e0\u6094\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u540c\u4eba\u4e8e\u90ca\uff0c\u5fd7\u672a\u5f97\u4e5f\u3002\n"
    ],
    [
        "\n\u300a\u6613\u7ecf\u300b\u7b2c\u5341\u56db\u5366 \u5927\u6709 \u706b\u5929\u5927\u6709 \u79bb\u4e0a\u4e7e\u4e0b \u3000\u3000\n\u5927\u6709\uff1a\u5143\u4ea8\u3002 \n\u5f56\u66f0\uff1a\u5927\u6709\uff0c\u67d4\u5f97\u5c0a\u4f4d\uff0c\u5927\u4e2d\u800c\u4e0a\u4e0b\u5e94\u4e4b\uff0c\u66f0\u5927\u6709\u3002\u5176\u5fb7\u521a\u5065\u800c\u6587\u660e\uff0c\u5e94\u4e4e\u5929\u800c\u65f6\u884c\uff0c\u662f\u4ee5\u5143\u4ea8\u3002\n\u8c61\u66f0\uff1a\u706b\u5728\u5929\u4e0a\uff0c\u5927\u6709\uff1b\u541b\u5b50\u4ee5\u7aed\u6076\u626c\u5584\uff0c\u987a\u5929\u4f11\u547d\u3002\n",
        "\n\u3000\u3000\u521d\u4e5d\uff1a\u65e0\u4ea4\u5bb3\uff0c\u532a\u548e\uff0c\u8270\u5219\u65e0\u548e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5927\u6709\u521d\u4e5d\uff0c\u65e0\u4ea4\u5bb3\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e8c\uff1a\u5927\u8f66\u4ee5\u8f7d\uff0c\u6709\u6538\u5f80\uff0c\u65e0\u548e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5927\u8f66\u4ee5\u8f7d\uff0c\u79ef\u4e2d\u4e0d\u8d25\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e09\uff1a\u516c\u7528\u4ea8\u4e8e\u5929\u5b50\uff0c\u5c0f\u4eba\u5f17\u514b\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u516c\u7528\u4ea8\u4e8e\u5929\u5b50\uff0c\u5c0f\u4eba\u5bb3\u4e5f\u3002\n\u3000\u3000\u4e5d\u56db\uff1a\u532a\u5176\u5f6d\uff0c\u65e0\u548e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u532a\u5176\u5f6d\uff0c\u65e0\u548e\uff1b\u660e\u8fa8\u6670\u4e5f\u3002\n\u3000\u3000\u516d\u4e94\uff1a\u53a5\u5b5a\u4ea4\u5982\uff0c\u5a01\u5982\uff1b\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u53a5\u5b5a\u4ea4\u5982\uff0c\u4fe1\u4ee5\u53d1\u5fd7\u4e5f\u3002 \u5a01\u5982\u4e4b\u5409\uff0c\u6613\u800c\u65e0\u5907\u4e5f\u3002\n\u3000\u3000\u4e0a\u4e5d\uff1a\u81ea\u5929\u4f51\u4e4b\uff0c\u5409\u65e0\u4e0d\u5229\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5927\u6709\u4e0a\u5409\uff0c\u81ea\u5929\u4f51\u4e5f\u3002\n"
    ],
    [
        "\n \u300a\u6613\u7ecf\u300b\u7b2c\u5341\u4e94\u5366 \u8c26 \u5730\u5c71\u8c26 \u5764\u4e0a\u826e\u4e0b \u3000\u3000\n \u8c26\uff1a\u4ea8\uff0c\u541b\u5b50\u6709\u7ec8\u3002\n\u5f56\u66f0\uff1a\u8c26\uff0c\u4ea8\uff0c\u5929\u9053\u4e0b\u6d4e\u800c\u5149\u660e\uff0c\u5730\u9053\u5351\u800c\u4e0a\u884c\u3002\u5929\u9053\u4e8f\u76c8\u800c\u76ca\u8c26\uff0c\u5730\u9053\u53d8\u76c8\u800c\u6d41\u8c26\uff0c\u9b3c\u795e\u5bb3\u76c8\u800c\u798f\u8c26\uff0c\u4eba\u9053\u6076\u76c8\u800c\u597d\u8c26\u3002\u8c26\u5c0a\u800c\u5149\uff0c\u5351\u800c\u4e0d\u53ef\u8e30\uff0c\u541b\u5b50\u4e4b\u7ec8\u4e5f\u3002\n\u8c61\u66f0\uff1a\u5730\u4e2d\u6709\u5c71\uff0c\u8c26\uff1b\u541b\u5b50\u4ee5\u88d2\u591a\u76ca\u5be1\uff0c\u79f0\u7269\u5e73\u65bd\u3002\n",
        "\n\u3000\u3000\u521d\u516d\uff1a\u8c26\u8c26\u541b\u5b50\uff0c\u7528\u6d89\u5927\u5ddd\uff0c\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u8c26\u8c26\u541b\u5b50\uff0c\u5351\u4ee5\u81ea\u7267\u4e5f\u3002\n\u3000\u3000\u516d\u4e8c\uff1a\u9e23\u8c26\uff0c\u8d1e\u5409\u3002\n\u3000\u8c61\u66f0\uff1a\u9e23\u8c26\u8d1e\u5409\uff0c\u4e2d\u5fc3\u5f97\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e09\uff1a\u52b3\u8c26\u541b\u5b50\uff0c\u6709\u7ec8\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u52b3\u8c26\u541b\u5b50\uff0c\u4e07\u6c11\u670d\u4e5f\u3002\n\u3000\u3000\u516d\u56db\uff1a\u65e0\u4e0d\u5229\uff0c\u649d\u8c26\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u65e0\u4e0d\u5229\uff0c\u649d\u8c26\uff1b\u4e0d\u8fdd\u5219\u4e5f\u3002\n\u3000\u3000\u516d\u4e94\uff1a\u4e0d\u5bcc\uff0c\u4ee5\u5176\u90bb\uff0c\u5229\u7528\u4fb5\u4f10\uff0c\u65e0\u4e0d\u5229\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5229\u7528\u4fb5\u4f10\uff0c\u5f81\u4e0d\u670d\u4e5f\u3002\n\u3000\u3000\u4e0a\u516d\uff1a\u9e23\u8c26\uff0c\u5229\u7528\u884c\u5e08\uff0c\u5f81\u9091\u56fd\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u9e23\u8c26\uff0c\u5fd7\u672a\u5f97\u4e5f\u3002 \u53ef\u7528\u884c\u5e08\uff0c\u5f81\u9091\u56fd\u4e5f\u3002\n"
    ],
    [
        "\n \u300a\u6613\u7ecf\u300b\u7b2c\u5341\u516d\u5366 \u8c6b \u96f7\u5730\u8c6b \u9707\u4e0a\u5764\u4e0b \n\u3000\u3000\u8c6b\uff1a\u5229\u5efa\u4faf\u884c\u5e08\u3002\n\u5f56\u66f0\uff1a\u8c6b\uff0c\u521a\u5e94\u800c\u5fd7\u884c\uff0c\u987a\u4ee5\u52a8\uff0c\u8c6b\u3002\u8c6b\uff0c\u987a\u4ee5\u52a8\uff0c\u6545\u5929\u5730\u5982\u4e4b\uff0c\u800c\u51b5\u5efa\u4faf\u884c\u5e08\u4e4e\uff1f\u5929\u5730\u4ee5\u987a\u52a8\uff0c\u6545\u65e5\u6708\u4e0d\u8fc7\uff0c\u800c\u56db\u65f6\u4e0d\u5fd2\uff1b\u5723\u4eba\u4ee5\u987a\u52a8\uff0c\u5219\u5211\u7f5a\u6e05\u800c\u6c11\u670d\u3002 \u8c6b\u4e4b\u65f6\u4e49\u5927\u77e3\u54c9\uff01\n\u8c61\u66f0\uff1a\u96f7\u51fa\u5730\u594b\uff0c\u8c6b\u3002 \u5148\u738b\u4ee5\u4f5c\u4e50\u5d07\u5fb7\uff0c\u6bb7\u8350\u4e4b\u4e0a\u5e1d\uff0c\u4ee5\u914d\u7956\u8003\u3002\n",
        "\n\u3000\u3000\u521d\u516d\uff1a\u9e23\u8c6b\uff0c\u51f6\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u521d\u516d\u9e23\u8c6b\uff0c\u5fd7\u7a77\u51f6\u4e5f\u3002\n\u3000\u3000\u516d\u4e8c\uff1a\u4ecb\u4e8e\u77f3\uff0c\u4e0d\u7ec8\u65e5\uff0c\u8d1e\u5409\u3002\n\u3000\u8c61\u66f0\uff1a\u4e0d\u7ec8\u65e5\uff0c\u8d1e\u5409\uff1b\u4ee5\u4e2d\u6b63\u4e5f\u3002\n\u3000\u3000\u516d\u4e09\uff1a\u76f1\u8c6b\uff0c\u6094\u3002 \u8fdf\u6709\u6094\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u76f1\u8c6b\u6709\u6094\uff0c\u4f4d\u4e0d\u5f53\u4e5f\u3002\n\u3000\u3000\u4e5d\u56db\uff1a\u7531\u8c6b\uff0c\u5927\u6709\u5f97\u3002\u52ff\u7591\u3002 \u670b\u76cd\u7c2a\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u7531\u8c6b\uff0c\u5927\u6709\u5f97\uff1b\u5fd7\u5927\u884c\u4e5f\u3002\n\u3000\u3000\u516d\u4e94\uff1a\u8d1e\u75be\uff0c\u6052\u4e0d\u6b7b\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u516d\u4e94\u8d1e\u75be\uff0c\u4e58\u521a\u4e5f\u3002 \u6052\u4e0d\u6b7b\uff0c\u4e2d\u672a\u4ea1\u4e5f\u3002\n\u3000\u3000\u4e0a\u516d\uff1a\u51a5\u8c6b\uff0c\u6210\u6709\u6e1d\uff0c\u65e0\u548e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u51a5\u8c6b\u5728\u4e0a\uff0c\u4f55\u53ef\u957f\u4e5f\u3002\n"
    ],
    [
        "\n\u300a\u6613\u7ecf\u300b\u7b2c\u5341\u4e03\u5366 \u968f \u6cfd\u96f7\u968f \u5151\u4e0a\u9707\u4e0b \n\u3000\u3000\u968f\uff1a\u5143\u4ea8\u5229\u8d1e\uff0c\u65e0\u548e\u3002\n\u5f56\u66f0\uff1a\u968f\uff0c\u521a\u6765\u800c\u4e0b\u67d4\uff0c\u52a8\u800c\u8bf4\uff0c\u968f\u3002\u5927\u4ea8\u8d1e\uff0c\u65e0\u548e\uff0c\u800c\u5929\u4e0b\u968f\u65f6\uff0c\u968f\u4e4b\u65f6\u4e49\u5927\u77e3\u54c9\uff01\n\u8c61\u66f0\uff1a\u6cfd\u4e2d\u6709\u96f7\uff0c\u968f\uff1b\u541b\u5b50\u4ee5\ufffd\u674c\u5955\u80d9\u7f26\u2170\ufffd\n",
        "\n\u3000\u3000\u521d\u4e5d\uff1a\u5b98\u6709\u6e1d\uff0c\u8d1e\u5409\u3002 \u51fa\u95e8\u4ea4\u6709\u529f\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5b98\u6709\u6e1d\uff0c\u4ece\u6b63\u5409\u4e5f\u3002 \u51fa\u95e8\u4ea4\u6709\u529f\uff0c\u4e0d\u5931\u4e5f\u3002\n\u3000\u3000\u516d\u4e8c\uff1a\u7cfb\u5c0f\u5b50\uff0c\u5931\u4e08\u592b\u3002\n\u3000\u8c61\u66f0\uff1a\u7cfb\u5c0f\u5b50\uff0c\u5f17\u517c\u4e0e\u4e5f\u3002\n\u3000\u3000\u516d\u4e09\uff1a\u7cfb\u4e08\u592b\uff0c\u5931\u5c0f\u5b50\u3002 \u968f\u6709\u6c42\u5f97\uff0c\u5229\u5c45\u8d1e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u7cfb\u4e08\u592b\uff0c\u5fd7\u820d\u4e0b\u4e5f\u3002\n\u3000\u3000\u4e5d\u56db\uff1a\u968f\u6709\u83b7\uff0c\u8d1e\u51f6\u3002\u6709\u5b5a\u5728\u9053\uff0c\u4ee5\u660e\uff0c\u4f55\u548e\u3002 \n\u3000\u3000\u8c61\u66f0\uff1a\u968f\u6709\u83b7\uff0c\u5176\u4e49\u51f6\u4e5f\u3002 \u6709\u5b5a\u5728\u9053\uff0c\u660e\u529f\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e94\uff1a\u5b5a\u4e8e\u5609\uff0c\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5b5a\u4e8e\u5609\uff0c\u5409\uff1b\u4f4d\u6b63\u4e2d\u4e5f\u3002\n\u3000\u3000\u4e0a\u516d\uff1a\u62d8\u7cfb\u4e4b\uff0c\u4e43\u4ece\u7ef4\u4e4b\u3002 \u738b\u7528\u4ea8\u4e8e\u897f\u5c71\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u62d8\u7cfb\u4e4b\uff0c\u4e0a\u7a77\u4e5f\u3002\n"
    ],
    [
        "\n\u300a\u6613\u7ecf\u300b\u7b2c\u5341\u516b\u5366 \u86ca \u5c71\u98ce\u86ca \u826e\u4e0a\u5dfd\u4e0b \u3000\n\u86ca\uff1a\u5143\u4ea8\uff0c\u5229\u6d89\u5927\u5ddd\u3002 \u5148\u7532\u4e09\u65e5\uff0c\u540e\u7532\u4e09\u65e5\u3002\n\u5f56\u66f0\uff1a\u86ca\uff0c\u521a\u4e0a\u800c\u67d4\u4e0b\uff0c\u5dfd\u800c\u6b62\uff0c\u86ca\u3002 \u86ca\uff0c\u5143\u4ea8\uff0c\u800c\u5929\u4e0b\u6cbb\u4e5f\u3002 \u5229\u6d89\u5927\u5ddd\uff0c\u5f80\u6709\u4e8b\u4e5f\u3002 \u5148\u7532\u4e09\u65e5\uff0c\u540e\u7532\u4e09\u65e5\uff0c\u7ec8\u5219\u6709\u59cb\uff0c\u5929\u884c\u4e5f\u3002\n\u8c61\u66f0\uff1a\u5c71\u4e0b\u6709\u98ce\uff0c\u86ca\uff1b\u541b\u5b50\u4ee5\u632f\u6c11\u80b2\u5fb7\u3002\n",
        "\n\u3000\u3000\u521d\u516d\uff1a\u5e72\u7236\u4e4b\u86ca\uff0c\u6709\u5b50\uff0c\u8003\u65e0\u548e\uff0c\u5389\u7ec8\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5e72\u7236\u4e4b\u86ca\uff0c\u610f\u627f\u8003\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e8c\uff1a\u5e72\u6bcd\u4e4b\u86ca\uff0c\u4e0d\u53ef\u8d1e\u3002\n\u3000\u8c61\u66f0\uff1a\u5e72\u6bcd\u4e4b\u86ca\uff0c\u5f97\u4e2d\u9053\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e09\uff1a\u5e72\u7236\u5c0f\u6709\u6666\uff0c\u65e0\u5927\u548e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5e72\u7236\u4e4b\u86ca\uff0c\u7ec8\u65e0\u548e\u4e5f\u3002\n\u3000\u3000\u516d\u56db\uff1a\u88d5\u7236\u4e4b\u86ca\uff0c\u5f80\u89c1\u541d\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u88d5\u7236\u4e4b\u86ca\uff0c\u5f80\u672a\u5f97\u4e5f\u3002\n\u3000\u3000\u516d\u4e94\uff1a\u5e72\u7236\u4e4b\u86ca\uff0c\u7528\u8a89\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5e72\u7236\u4e4b\u86ca\uff1b\u627f\u4ee5\u5fb7\u4e5f\u3002\n\u3000\u3000\u4e0a\u4e5d\uff1a\u4e0d\u4e8b\u738b\u4faf\uff0c\u9ad8\u5c1a\u5176\u4e8b\u3002 \n\u3000\u3000\u8c61\u66f0\uff1a\u4e0d\u4e8b\u738b\u4faf\uff0c\u5fd7\u53ef\u5219\u4e5f\u3002\n"
    ],
    [
        "\n\u300a\u6613\u7ecf\u300b\u7b2c\u5341\u4e5d\u5366 \u4e34 \u5730\u6cfd\u4e34 \u5764\u4e0a\u5151\u4e0b \n\u3000\u3000\u4e34\uff1a\u5143\uff0c\u4ea8\uff0c\u5229\uff0c\u8d1e\u3002 \u81f3\u4e8e\u516b\u6708\u6709\u51f6\u3002\n\u5f56\u66f0\uff1a\u4e34\uff0c\u521a\u6d78\u800c\u957f\u3002 \u8bf4\u800c\u987a\uff0c\u521a\u4e2d\u800c\u5e94\uff0c\u5927\u4ea8\u4ee5\u6b63\uff0c\u5929\u4e4b\u9053\u4e5f\u3002 \u81f3\u4e8e\u516b\u6708\u6709\u51f6\uff0c\u6d88\u4e0d\u4e45\u4e5f\u3002\n\u8c61\u66f0\uff1a\u6cfd\u4e0a\u6709\u5730\uff0c\u4e34\uff1b \u541b\u5b50\u4ee5\u6559\u601d\u65e0\u7a77\uff0c\u5bb9\u4fdd\u6c11\u65e0\u7586\u3002\n",
        "\n\u3000\u3000\u521d\u4e5d\uff1a\u54b8\u4e34\uff0c\u8d1e\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u54b8\u4e34\u8d1e\u5409\uff0c\u5fd7\u884c\u6b63\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e8c\uff1a\u54b8\u4e34\uff0c\u5409\u65e0\u4e0d\u5229\u3002\n\u3000\u8c61\u66f0\uff1a\u54b8\u4e34\uff0c\u5409\u65e0\u4e0d\u5229\uff1b\u672a\u987a\u547d\u4e5f\u3002\n\u3000\u3000\u516d\u4e09\uff1a\u7518\u4e34\uff0c\u65e0\u6538\u5229\u3002 \u65e2\u5fe7\u4e4b\uff0c\u65e0\u548e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u7518\u4e34\uff0c\u4f4d\u4e0d\u5f53\u4e5f\u3002 \u65e2\u5fe7\u4e4b\uff0c\u548e\u4e0d\u957f\u4e5f\u3002\n\u3000\u3000\u516d\u56db\uff1a\u81f3\u4e34\uff0c\u65e0\u548e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u81f3\u4e34\u65e0\u548e\uff0c\u4f4d\u5f53\u4e5f\u3002\n\u3000\u3000\u516d\u4e94\uff1a\u77e5\u4e34\uff0c\u5927\u541b\u4e4b\u5b9c\uff0c\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5927\u541b\u4e4b\u5b9c\uff0c\u884c\u4e2d\u4e4b\u8c13\u4e5f\u3002\n\u3000\u3000\u4e0a\u516d\uff1a\u6566\u4e34\uff0c\u5409\u65e0\u548e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u6566\u4e34\u4e4b\u5409\uff0c\u5fd7\u5728\u5185\u4e5f\u3002\n"
    ],
    [
        "\n\u300a\u6613\u7ecf\u300b\u7b2c\u4e8c\u5341\u5366 \u89c2 \u98ce\u5730\u89c2 \u5dfd\u4e0a\u5764\u4e0b \n\u3000\u3000\u89c2\uff1a\u76e5\u800c\u4e0d\u8350\uff0c\u6709\u5b5a\u9899\u82e5\u3002\n\u5f56\u66f0\uff1a\u5927\u89c2\u5728\u4e0a\uff0c\u987a\u800c\u5dfd\uff0c\u4e2d\u6b63\u4ee5\u89c2\u5929\u4e0b\u3002\u89c2\uff0c\u76e5\u800c\u4e0d\u8350\uff0c\u6709\u5b5a\u9899\u82e5\uff0c\u4e0b\u89c2\u800c\u5316\u4e5f\u3002 \u89c2\u5929\u4e4b\u795e\u9053\uff0c\u800c\u56db\u65f6\u4e0d\u5fd2\uff0c \u5723\u4eba\u4ee5\u795e\u9053\u8bbe\u6559\uff0c\u800c\u5929\u4e0b\u670d\u77e3\u3002\n\u8c61\u66f0\uff1a\u98ce\u884c\u5730\u4e0a\uff0c\u89c2\uff1b\u5148\u738b\u4ee5\u7701\u65b9\uff0c\u89c2\u6c11\u8bbe\u6559\u3002\n",
        "\n\u3000\u3000\u521d\u516d\uff1a\u7ae5\u89c2\uff0c\u5c0f\u4eba\u65e0\u548e\uff0c\u541b\u5b50\u541d\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u521d\u516d\u7ae5\u89c2\uff0c\u5c0f\u4eba\u9053\u4e5f\u3002\n\u3000\u3000\u516d\u4e8c\uff1a\u7aa5\u89c2\uff0c\u5229\u5973\u8d1e\u3002\n\u3000\u8c61\u66f0\uff1a\u7aa5\u89c2\u5973\u8d1e\uff0c\u4ea6\u53ef\u4e11\u4e5f\u3002\n\u3000\u3000\u516d\u4e09\uff1a\u89c2\u6211\u751f\uff0c\u8fdb\u9000\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u89c2\u6211\u751f\uff0c\u8fdb\u9000\uff1b\u672a\u5931\u9053\u4e5f\u3002\n\u3000\u3000\u516d\u56db\uff1a\u89c2\u56fd\u4e4b\u5149\uff0c\u5229\u7528\u5bbe\u4e8e\u738b\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u89c2\u56fd\u4e4b\u5149\uff0c\u5c1a\u5bbe\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e94\uff1a\u89c2\u6211\u751f\uff0c\u541b\u5b50\u65e0\u548e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u89c2\u6211\u751f\uff0c\u89c2\u6c11\u4e5f\u3002\n\u3000\u3000\u4e0a\u4e5d\uff1a\u89c2\u5176\u751f\uff0c\u541b\u5b50\u65e0\u548e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u89c2\u5176\u751f\uff0c\u5fd7\u672a\u5e73\u4e5f\u3002\n"
    ],
    [
        "\n\u300a\u6613\u7ecf\u300b\u7b2c\u4e8c\u5341\u4e00\u5366 \u566c\u55d1 \u706b\u96f7\u566c\u55d1 \u79bb\u4e0a\u9707\u4e0b \n\u3000\u566c\u55d1\uff1a\u4ea8\u3002 \u5229\u7528\u72f1\u3002\n\u5f56\u66f0\uff1a\u9890\u4e2d\u6709\u7269\uff0c\u66f0\u566c\u55d1\uff0c\u566c\u55d1\u800c\u4ea8\u3002\u521a\u67d4\u5206\uff0c\u52a8\u800c\u660e\uff0c\u96f7\u7535\u5408\u800c\u7ae0\u3002\u67d4\u5f97\u4e2d\u800c\u4e0a\u884c\uff0c\u867d\u4e0d\u5f53\u4f4d\uff0c\u5229\u7528\u72f1\u4e5f\u3002\n\u8c61\u66f0\uff1a\u96f7\u7535\u566c\u55d1\uff1b\u5148\u738b\u4ee5\u660e\u7f5a\u6555\u6cd5\u3002\n\u3000\u3000",
        "\n  \u521d\u4e5d\uff1a\u5c65\u6821\u706d\u8dbe\uff0c\u65e0\u548e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5c65\u6821\u706d\u8dbe\uff0c\u4e0d\u884c\u4e5f\u3002\n\u3000\u3000\u516d\u4e8c\uff1a\u566c\u80a4\u706d\u9f3b\uff0c\u65e0\u548e\u3002\n\u3000\u8c61\u66f0\uff1a\u566c\u80a4\u706d\u9f3b\uff0c\u4e58\u521a\u4e5f\u3002\n\u3000\u3000\u516d\u4e09\uff1a\u566c\u814a\u8089\uff0c\u9047\u6bd2\uff1b\u5c0f\u541d\uff0c\u65e0\u548e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u9047\u6bd2\uff0c\u4f4d\u4e0d\u5f53\u4e5f\u3002\n\u3000\u3000\u4e5d\u56db\uff1a\u566c\u4e7e\u80cf\uff0c\u5f97\u91d1\u77e2\uff0c\u5229\u8270\u8d1e\uff0c\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5229\u8270\u8d1e\u5409\uff0c\u672a\u5149\u4e5f\u3002\n\u3000\u3000\u516d\u4e94\uff1a\u566c\u4e7e\u8089\uff0c\u5f97\u9ec4\u91d1\uff0c\u8d1e\u5389\uff0c\u65e0\u548e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u8d1e\u5389\u65e0\u548e\uff0c\u5f97\u5f53\u4e5f\u3002\n\u3000\u3000\u4e0a\u4e5d\uff1a\u4f55\u6821\u706d\u8033\uff0c\u51f6\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u4f55\u6821\u706d\u8033\uff0c\u806a\u4e0d\u660e\u4e5f\u3002\n"
    ],
    [
        "\n\u300a\u6613\u7ecf\u300b\u7b2c\u4e8c\u5341\u4e8c\u5366 \u8d32 \u5c71\u706b\u8d32 \u826e\u4e0a\u79bb\u4e0b \u3000\u3000\n\u8d32\uff1a\u4ea8\u3002 \u5c0f\u5229\u6709\u6240\u5f80\u3002\n\u5f56\u66f0\uff1a\u8d32\uff0c\u4ea8\uff1b\u67d4\u6765\u800c\u6587\u521a\uff0c\u6545\u4ea8\u3002\u5206\u521a\u4e0a\u800c\u6587\u67d4\uff0c\u6545\u5c0f\u5229\u6709\u6538\u5f80\u3002\u5929\u6587\u4e5f\uff1b\u6587\u660e\u4ee5\u6b62\uff0c\u4eba\u6587\u4e5f\u3002\u89c2\u4e4e\u5929\u6587\uff0c\u4ee5\u5bdf\u65f6\u53d8\uff1b\u89c2\u4e4e\u4eba\u6587\uff0c\u4ee5\u5316\u6210\u5929\u4e0b\u3002\n\u8c61\u66f0\uff1a\u5c71\u4e0b\u6709\u706b\uff0c\u8d32\uff1b\u541b\u5b50\u4ee5\u660e\u5eb6\u653f\uff0c\u65e0\u6562\u6298\u72f1\u3002\n",
        "\n\u3000\u3000\u521d\u4e5d\uff1a\u8d32\u5176\u8dbe\uff0c\u820d\u8f66\u800c\u5f92\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u820d\u8f66\u800c\u5f92\uff0c\u4e49\u5f17\u4e58\u4e5f\u3002\n\u3000\u3000\u516d\u4e8c\uff1a\u8d32\u5176\u987b\u3002\n\u3000\u8c61\u66f0\uff1a\u8d32\u5176\u987b\uff0c\u4e0e\u4e0a\u5174\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e09\uff1a\u8d32\u5982\u6fe1\u5982\uff0c\u6c38\u8d1e\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u6c38\u8d1e\u4e4b\u5409\uff0c\u7ec8\u83ab\u4e4b\u9675\u4e5f\u3002\n\u3000\u3000\u516d\u56db\uff1a\u8d32\u5982\u76a4\u5982\uff0c\u767d\u9a6c\u7ff0\u5982\uff0c\u532a\u5bc7\u5a5a\u5abe\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u516d\u56db\uff0c\u5f53\u4f4d\u7591\u4e5f\u3002 \u532a\u5bc7\u5a5a\u5abe\uff0c\u7ec8\u65e0\u5c24\u4e5f\u3002\n\u3000\u3000\u516d\u4e94\uff1a\u8d32\u4e8e\u4e18\u56ed\uff0c\u675f\u5e1b\u620b\u620b\uff0c\u541d\uff0c\u7ec8\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u516d\u4e94\u4e4b\u5409\uff0c\u6709\u559c\u4e5f\u3002\n\u3000\u3000\u4e0a\u4e5d\uff1a\u767d\u8d32\uff0c\u65e0\u548e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u767d\u8d32\u65e0\u548e\uff0c\u4e0a\u5f97\u5fd7\u4e5f\u3002\n"
    ],
    [
        "\n \u300a\u6613\u7ecf\u300b\u7b2c\u4e8c\u5341\u4e09\u5366 \u5265 \u5c71\u5730\u5265 \u826e\u4e0a\u5764\u4e0b \n\u5265\uff1a\u4e0d\u5229\u6709\u6538\u5f80\u3002\n\u5f56\u66f0\uff1a\u5265\uff0c\u5265\u4e5f\uff0c\u67d4\u53d8\u521a\u4e5f\u3002 \u4e0d\u5229\u6709\u6538\u5f80\uff0c\u5c0f\u4eba\u957f\u4e5f\u3002 \u987a\u800c\u6b62\u4e4b\uff0c\u89c2\u8c61\u4e5f\u3002 \u541b\u5b50\u5c1a\u6d88\u606f\u76c8\u865a\uff0c\u5929\u884c\u4e5f\u3002\n\u8c61\u66f0\uff1a\u5c71\u9644\u5730\u4e0a\uff0c\u5265\uff1b\u4e0a\u4ee5\u539a\u4e0b\uff0c\u5b89\u5b85\u3002\n"
    ],
    [
        "\n",
        "\u3000\u3000\n\u521d\u516d\uff1a\u5265\u7240\u4ee5\u8db3\uff0c\u8511\u8d1e\u51f6\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5265\u7240\u4ee5\u8db3\uff0c\u4ee5\u706d\u4e0b\u4e5f\u3002\n\u3000\u3000\u516d\u4e8c\uff1a\u5265\u7240\u4ee5\u8fa8\uff0c\u8511\u8d1e\u51f6\u3002\n\u3000\u8c61\u66f0\uff1a\u5265\u7240\u4ee5\u8fa8\uff0c\u672a\u6709\u4e0e\u4e5f\u3002\n\u3000\u3000\u516d\u4e09\uff1a\u5265\u4e4b\uff0c\u65e0\u548e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5265\u4e4b\u65e0\u548e\uff0c\u5931\u4e0a\u4e0b\u4e5f\u3002\n\u3000\u3000\u516d\u56db\uff1a\u5265\u7240\u4ee5\u80a4\uff0c\u51f6\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5265\u7240\u4ee5\u80a4\uff0c\u5207\u8fd1\u707e\u4e5f\u3002\n\u3000\u3000\u516d\u4e94\uff1a\u8d2f\u9c7c\uff0c\u4ee5\u5bab\u4eba\u5ba0\uff0c\u65e0\u4e0d\u5229\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u4ee5\u5bab\u4eba\u5ba0\uff0c\u7ec8\u65e0\u5c24\u4e5f\u3002\n\u3000\u3000\u4e0a\u4e5d\uff1a\u7855\u679c\u4e0d\u98df\uff0c\u541b\u5b50\u5f97\u8206\uff0c\u5c0f\u4eba\u5265\u5e90\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u541b\u5b50\u5f97\u8206\uff0c\u6c11\u6240\u8f7d\u4e5f\u3002 \u5c0f\u4eba\u5265\u5e90\uff0c\u7ec8\u4e0d\u53ef\u7528\u4e5f\u3002\n"
    ],
    [
        "\n \u300a\u6613\u7ecf\u300b\u7b2c\u4e8c\u5341\u56db\u5366 \u590d \u5730\u96f7\u590d \u5764\u4e0a\u9707\u4e0b \n\u3000\u3000\u590d\uff1a\u4ea8\u3002 \u51fa\u5165\u65e0\u75be\uff0c\u670b\u6765\u65e0\u548e\u3002 \u53cd\u590d\u5176\u9053\uff0c\u4e03\u65e5\u6765\u590d\uff0c\u5229\u6709\u6538\u5f80\u3002\n\u5f56\u66f0\uff1a\u590d\u4ea8\uff1b\u521a\u53cd\uff0c\u52a8\u800c\u4ee5\u987a\u884c\uff0c\u662f\u4ee5\u51fa\u5165\u65e0\u75be\uff0c\u670b\u6765\u65e0\u548e\u3002 \u53cd\u590d\u5176\u9053\uff0c\u4e03\u65e5\u6765\u590d\uff0c\u5929\u884c\u4e5f\u3002 \u5229\u6709\u6538\u5f80\uff0c\u521a\u957f\u4e5f\u3002 \u590d\u5176\u89c1\u5929\u5730\u4e4b\u5fc3\u4e4e\uff1f\n\u8c61\u66f0\uff1a\u96f7\u5728\u5730\u4e2d\uff0c\u590d\uff1b\u5148\u738b\u4ee5\u81f3\u65e5\u95ed\u5173\uff0c\u5546\u65c5\u4e0d\u884c\uff0c\u540e\u4e0d\u7701\u65b9\u3002\n",
        "\n\u3000\u3000\u521d\u4e5d\uff1a\u4e0d\u590d\u8fdc\uff0c\u65e0\u53ea\u6094\uff0c\u5143\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u4e0d\u8fdc\u4e4b\u590d\uff0c\u4ee5\u4fee\u8eab\u4e5f\u3002\n\u3000\u3000\u516d\u4e8c\uff1a\u4f11\u590d\uff0c\u5409\u3002\n\u3000\u8c61\u66f0\uff1a\u4f11\u590d\u4e4b\u5409\uff0c\u4ee5\u4e0b\u4ec1\u4e5f\u3002\n\u3000\u3000\u516d\u4e09\uff1a\u9891\u590d\uff0c\u5389\u65e0\u548e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u9891\u590d\u4e4b\u5389\uff0c\u4e49\u65e0\u548e\u4e5f\u3002\n\u3000\u3000\u516d\u56db\uff1a\u4e2d\u884c\u72ec\u590d\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u4e2d\u884c\u72ec\u590d\uff0c\u4ee5\u4ece\u9053\u4e5f\u3002\n\u3000\u3000\u516d\u4e94\uff1a\u6566\u590d\uff0c\u65e0\u6094\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u6566\u590d\u65e0\u6094\uff0c\u4e2d\u4ee5\u81ea\u8003\u4e5f\u3002\n\u4e0a\u516d\uff1a\u8ff7\u590d\uff0c\u51f6\uff0c\u6709\u707e\u771a\u3002\u7528\u884c\u5e08\uff0c\u7ec8\u6709\u5927\u8d25\uff0c\u4ee5\u5176\u56fd\u541b\uff0c\u51f6\uff1b\u81f3\u4e8e\n\u5341\u5e74\uff0c\u4e0d\u514b\u5f81\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u8ff7\u590d\u4e4b\u51f6\uff0c\u53cd\u541b\u9053\u4e5f\u3002\n"
    ],
    [
        "\n\u300a\u6613\u7ecf\u300b\u7b2c\u4e8c\u5341\u4e94\u5366 \u65e0\u5984 \u5929\u96f7\u65e0\u5984 \u4e7e\u4e0a\u9707\u4e0b \u3000\n\u65e0\u5984\uff1a\u5143\uff0c\u4ea8\uff0c\u5229\uff0c\u8d1e\u3002 \u5176\u532a\u6b63\u6709\u771a\uff0c\u4e0d\u5229\u6709\u6538\u5f80\u3002\n\u5f56\u66f0\uff1a\u65e0\u5984\uff0c\u521a\u81ea\u5916\u6765\uff0c\u800c\u4e3a\u4e3b\u65bc\u5185\u3002\u52a8\u800c\u5065\uff0c\u521a\u4e2d\u800c\u5e94\uff0c\u5927\u4ea8\u4ee5\u6b63\uff0c\u5929\n\u4e4b\u547d\u4e5f\u3002 \u5176\u532a\u6b63\u6709\u771a\uff0c\u4e0d\u5229\u6709\u6538\u5f80\u3002\u65e0\u5984\u4e4b\u5f80\uff0c\u4f55\u4e4b\u77e3\uff1f \u5929\u547d\u4e0d\n\u4f51\uff0c\u884c\u77e3\u54c9\uff1f \n\u8c61\u66f0\uff1a\u5929\u4e0b\u96f7\u884c\uff0c\u7269\u4e0e\u65e0\u5984\uff1b\u5148\u738b\u4ee5\u8302\u5bf9\u65f6\uff0c\u80b2\u4e07\u7269\u3002\n",
        "\n\u3000\u3000\u521d\u4e5d\uff1a\u65e0\u5984\uff0c\u5f80\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u65e0\u5984\u4e4b\u5f80\uff0c\u5f97\u5fd7\u4e5f\u3002\n\u3000\u3000\u516d\u4e8c\uff1a\u4e0d\u8015\u83b7\uff0c\u4e0d\u83d1\u7572\uff0c\u5219\u5229\u6709\u6538\u5f80\u3002\n\u3000\u8c61\u66f0\uff1a\u4e0d\u8015\u83b7\uff0c\u672a\u5bcc\u4e5f\u3002\n\u3000\u3000\u516d\u4e09\uff1a\u65e0\u5984\u4e4b\u707e\uff0c\u6216\u7cfb\u4e4b\u725b\uff0c\u884c\u4eba\u4e4b\u5f97\uff0c\u9091\u4eba\u4e4b\u707e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u884c\u4eba\u5f97\u725b\uff0c\u9091\u4eba\u707e\u4e5f\u3002\n\u3000\u3000\u4e5d\u56db\uff1a\u53ef\u8d1e\uff0c\u65e0\u548e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u53ef\u8d1e\u65e0\u548e\uff0c\u56fa\u6709\u4e4b\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e94\uff1a\u65e0\u5984\u4e4b\u75be\uff0c\u52ff\u836f\u6709\u559c\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u65e0\u5984\u4e4b\u836f\uff0c\u4e0d\u53ef\u8bd5\u4e5f\u3002\n\u3000\u3000\u4e0a\u4e5d\uff1a\u65e0\u5984\uff0c\u884c\u6709\u771a\uff0c\u65e0\u6538\u5229\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u65e0\u5984\u4e4b\u884c\uff0c\u7a77\u4e4b\u707e\u4e5f\u3002\n"
    ],
    [
        "\n\u300a\u6613\u7ecf\u300b\u7b2c\u4e8c\u5341\u516d\u5366 \u5927\u755c \u5c71\u5929\u5927\u755c \u826e\u4e0a\u4e7e\u4e0b \n\u5927\u755c\uff1a\u5229\u8d1e\uff0c\u4e0d\u5bb6\u98df\u5409\uff0c\u5229\u6d89\u5927\u5ddd\u3002\n\u5f56\u66f0\uff1a\u5927\u755c\uff0c\u521a\u5065\u7b03\u5b9e\u8f89\u5149\uff0c\u65e5\u65b0\u5176\u5fb7\uff0c\u521a\u4e0a\u800c\u5c1a\u8d24\u3002 \u80fd\u6b62\u5065\uff0c\u5927\u6b63\u4e5f\u3002\n\u4e0d\u5bb6\u98df\u5409\uff0c\u517b\u8d24\u4e5f\u3002 \u5229\u6d89\u5927\u5ddd\uff0c\u5e94\u4e4e\u5929\u4e5f\u3002\n\u8c61\u66f0\uff1a\u5929\u5728\u5c71\u4e2d\uff0c\u5927\u755c\uff1b\u541b\u5b50\u4ee5\u591a\u8bc6\u524d\u8a00\u5f80\u884c\uff0c\u4ee5\u755c\u5176\u5fb7\u3002\n",
        "\n\u3000\u3000\u521d\u4e5d\uff1a\u6709\u5389\u5229\u5df2\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u6709\u5389\u5229\u5df2\uff0c\u4e0d\u72af\u707e\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e8c\uff1a\u8206\u8bf4\u8f90\u3002\n\u3000\u8c61\u66f0\uff1a\u8206\u8bf4\u8f90\uff0c\u4e2d\u65e0\u5c24\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e09\uff1a\u826f\u9a6c\u9010\uff0c\u5229\u8270\u8d1e\u3002 \u66f0\u95f2\u8206\u536b\uff0c\u5229\u6709\u6538\u5f80\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5229\u6709\u6538\u5f80\uff0c\u4e0a\u5408\u5fd7\u4e5f\u3002\n\u3000\u3000\u516d\u56db\uff1a\u7ae5\u8c55\u4e4b\u727f\uff0c\u5143\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u516d\u56db\u5143\u5409\uff0c\u6709\u559c\u4e5f\u3002\n\u3000\u3000\u516d\u4e94\uff1a\u8c6e\u8c55\u4e4b\u7259\uff0c\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u516d\u4e94\u4e4b\u5409\uff0c\u6709\u5e86\u4e5f\u3002\n\u3000\u3000\u4e0a\u4e5d\uff1a\u4f55\u5929\u4e4b\u8862\uff0c\u4ea8\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u4f55\u5929\u4e4b\u8862\uff0c\u9053\u5927\u884c\u4e5f\u3002\n"
    ],
    [
        "\n\u300a\u6613\u7ecf\u300b\u7b2c\u4e8c\u5341\u4e03\u5366 \u9890 \u5c71\u96f7\u9890 \u826e\u4e0a\u9707\u4e0b \n\u3000\u3000\u9890\uff1a\u8d1e\u5409\u3002 \u89c2\u9890\uff0c\u81ea\u6c42\u53e3\u5b9e\u3002\n\u5f56\u66f0\uff1a\u9890\u8d1e\u5409\uff0c\u517b\u6b63\u5219\u5409\u4e5f\u3002 \u89c2\u9890\uff0c\u89c2\u5176\u6240\u517b\u4e5f\uff1b \u81ea\u6c42\u53e3\u5b9e\uff0c\u89c2\u5176\u81ea\u517b\n\u4e5f\u3002 \u5929\u5730\u517b\u4e07\u7269\uff0c\u5723\u4eba\u517b\u8d24\uff0c\u4ee5\u53ca\u4e07\u6c11\uff1b\u9890\u4e4b\u65f6\u4e49\u5927\u77e3\u54c9\uff01\n\u8c61\u66f0\uff1a\u5c71\u4e0b\u6709\u96f7\uff0c\u9890\uff1b\u541b\u5b50\u4ee5\u614e\u8a00\u8bed\uff0c\u8282\u996e\u98df\u3002\n",
        "\n\u3000\u3000\u521d\u4e5d\uff1a\u820d\u5c14\u7075\u9f9f\uff0c\u89c2\u6211\u6735\u9890\uff0c\u51f6\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u89c2\u6211\u6735\u9890\uff0c\u4ea6\u4e0d\u8db3\u8d35\u4e5f\u3002\n\u3000\u3000\u516d\u4e8c\uff1a\u98a0\u9890\uff0c\u62c2\u7ecf\uff0c\u4e8e\u4e18\u9890\uff0c\u5f81\u51f6\u3002\n\u3000\u8c61\u66f0\uff1a\u516d\u4e8c\u5f81\u51f6\uff0c\u884c\u5931\u7c7b\u4e5f\u3002\n\u3000\u3000\u516d\u4e09\uff1a\u62c2\u9890\uff0c\u8d1e\u51f6\uff0c\u5341\u5e74\u52ff\u7528\uff0c\u65e0\u6538\u5229\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5341\u5e74\u52ff\u7528\uff0c\u9053\u5927\u6096\u4e5f\u3002\n\u3000\u3000\u516d\u56db\uff1a\u98a0\u9890\u5409\uff0c\u864e\u89c6\u7708\u7708\uff0c\u5176\u6b32\u9010\u9010\uff0c\u65e0\u548e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u98a0\u9890\u4e4b\u5409\uff0c\u4e0a\u65bd\u5149\u4e5f\u3002\n\u3000\u3000\u516d\u4e94\uff1a\u62c2\u7ecf\uff0c\u5c45\u8d1e\u5409\uff0c\u4e0d\u53ef\u6d89\u5927\u5ddd\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5c45\u8d1e\u4e4b\u5409\uff0c\u987a\u4ee5\u4ece\u4e0a\u4e5f\u3002\n\u3000 \u4e0a\u4e5d\uff1a\u7531\u9890\uff0c\u5389\u5409\uff0c\u5229\u6d89\u5927\u5ddd\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u7531\u9890\u5389\u5409\uff0c\u5927\u6709\u5e86\u4e5f\u3002\n"
    ],
    [
        "\n\u300a\u6613\u7ecf\u300b\u7b2c\u4e8c\u5341\u516b\u5366 \u5927\u8fc7 \u6cfd\u98ce\u5927\u8fc7 \u5151\u4e0a\u5dfd\u4e0b \n\u3000\u5927\u8fc7\uff1a\u680b\u6861\uff0c\u5229\u6709\u6538\u5f80\uff0c\u4ea8\u3002\n\u5f56\u66f0\uff1a\u5927\u8fc7\uff0c\u5927\u8005\u8fc7\u4e5f\u3002 \u680b\u6861\uff0c\u672c\u672b\u5f31\u4e5f\u3002 \u521a\u8fc7\u800c\u4e2d\uff0c\u5dfd\u800c\u8bf4\u884c\uff0c\u5229\u6709\n\u6538\u5f80\uff0c\u4e43\u4ea8\u3002 \u5927\u8fc7\u4e4b\u65f6\u4e49\u5927\u77e3\u54c9\uff01\n\u8c61\u66f0\uff1a\u6cfd\u706d\u6728\uff0c\u5927\u8fc7\uff1b\u541b\u5b50\u4ee5\u72ec\u7acb\u4e0d\u60e7\uff0c\u906f\u4e16\u65e0\u95f7\u3002\n",
        "\n\u3000\u3000\u521d\u516d\uff1a\u85c9\u7528\u767d\u8305\uff0c\u65e0\u548e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u85c9\u7528\u767d\u8305\uff0c\u67d4\u5728\u4e0b\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e8c\uff1a\u67af\u6768\u751f\u79ed\uff0c\u8001\u592b\u5f97\u5176\u5973\u59bb\uff0c\u65e0\u4e0d\u5229\u3002\n\u3000\u8c61\u66f0\uff1a\u8001\u592b\u5973\u59bb\uff0c\u8fc7\u4ee5\u76f8\u4e0e\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e09\uff1a\u680b\u6861\uff0c\u51f6\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u680b\u6861\u4e4b\u51f6\uff0c\u4e0d\u53ef\u4ee5\u6709\u8f85\u4e5f\u3002\n\u3000\u3000\u4e5d\u56db\uff1a\u680b\u9686\uff0c\u5409\uff1b\u6709\u5b83\u541d\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u680b\u9686\u4e4b\u5409\uff0c\u4e0d\u6861\u4e4e\u4e0b\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e94\uff1a\u67af\u6768\u751f\u534e\uff0c\u8001\u5987\u5f97\u58eb\u592b\uff0c\u65e0\u548e\u65e0\u8a89\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u67af\u6768\u751f\u534e\uff0c\u4f55\u53ef\u4e45\u4e5f\u3002 \u8001\u5987\u58eb\u592b\uff0c\u4ea6\u53ef\u4e11\u4e5f\u3002\n\u3000\u3000\u4e0a\u516d\uff1a\u8fc7\u6d89\u706d\u9876\uff0c\u51f6\uff0c\u65e0\u548e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u8fc7\u6d89\u4e4b\u51f6\uff0c\u4e0d\u53ef\u548e\u4e5f\u3002\n"
    ],
    [
        "\n \u300a\u6613\u7ecf\u300b\u7b2c\u4e8c\u5341\u4e5d\u5366 \u574e \u574e\u4e3a\u6c34 \u574e\u4e0a\u574e\u4e0b \n\u3000\u3000\u574e\uff1a\u4e60\u574e\uff0c\u6709\u5b5a\uff0c\u7ef4\u5fc3\u4ea8\uff0c\u884c\u6709\u5c1a\u3002\n\u5f56\u66f0\uff1a\u4e60\u574e\uff0c\u91cd\u9669\u4e5f\u3002 \u6c34\u6d41\u800c\u4e0d\u76c8\uff0c\u884c\u9669\u800c\u4e0d\u5931\u5176\u4fe1\u3002 \u7ef4\u5fc3\u4ea8\uff0c\u4e43\u4ee5\u521a\n\u4e2d\u4e5f\u3002 \u884c\u6709\u5c1a\uff0c\u5f80\u6709\u529f\u4e5f\u3002 \u5929\u9669\u4e0d\u53ef\u5347\u4e5f\uff0c\u5730\u9669\u5c71\u5ddd\u4e18\u9675\u4e5f\uff0c\u738b\n\u516c\u8bbe\u9669\u4ee5\u5b88\u5176\u56fd\uff0c\u574e\u4e4b\u65f6\u7528\u5927\u77e3\u54c9\uff01\n\u8c61\u66f0\uff1a\u6c34\u6d0a\u81f3\uff0c\u4e60\u574e\uff1b\u541b\u5b50\u4ee5\u5e38\u5fb7\u884c\uff0c\u4e60\u6559\u4e8b\u3002\n",
        "\n\u3000\u3000\u521d\u516d\uff1a\u4e60\u574e\uff0c\u5165\u4e8e\u574e\u7a9e\uff0c\u51f6\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u4e60\u574e\u5165\u574e\uff0c\u5931\u9053\u51f6\u4e5f\u3002\n\u3002\u3000\u3000\u4e5d\u4e8c\uff1a\u574e\u6709\u9669\uff0c\u6c42\u5c0f\u5f97\u3002\n\u3000\u8c61\u66f0\uff1a\u6c42\u5c0f\u5f97\uff0c\u672a\u51fa\u4e2d\u4e5f\u3002\n\u3002\u3000\u3000\u516d\u4e09\uff1a\u6765\u4e4b\u574e\u574e\uff0c\u9669\u4e14\u6795\uff0c\u5165\u4e8e\u574e\u7a9e\uff0c\u52ff\u7528\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u6765\u4e4b\u574e\u574e\uff0c\u7ec8\u65e0\u529f\u4e5f\u3002\n\u3000\u3000\u516d\u56db\uff1a\u6a3d\u9152\u7c0b\u8d30\uff0c\u7528\u7f36\uff0c\u7eb3\u7ea6\u81ea\u7256\uff0c\u7ec8\u65e0\u548e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u6a3d\u9152\u7c0b\u8d30\uff0c\u521a\u67d4\u9645\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e94\uff1a\u574e\u4e0d\u76c8\uff0c\u53ea\u65e2\u5e73\uff0c\u65e0\u548e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u574e\u4e0d\u76c8\uff0c\u4e2d\u672a\u5927\u4e5f\u3002\n\u3000 \u4e0a\u516d\uff1a\ufffd\u949f\u6ca1\u7efdg\uff0c\u7f6e\u4e8e\u4e1b\u68d8\uff0c\u4e09\u5c81\u4e0d\u5f97\uff0c\u51f6\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u4e0a\u516d\u5931\u9053\uff0c\u51f6\u4e09\u5c81\u4e5f\u3002\n"
    ],
    [
        "\n\u300a\u6613\u7ecf\u300b\u7b2c\u4e09\u5341\u5366 \u79bb \u79bb\u4e3a\u706b \u79bb\u4e0a\u79bb\u4e0b \u3000\u3000\n\u79bb\uff1a\u5229\u8d1e\uff0c\u4ea8\u3002 \u755c\u725d\u725b\uff0c\u5409\u3002\n\u5f56\u66f0\uff1a\u79bb\uff0c\u4e3d\u4e5f\uff1b\u65e5\u6708\u4e3d\u4e4e\u5929\uff0c\u767e\u8c37\u8349\u6728\u4e3d\u4e4e\u571f\uff0c\u91cd\u660e\u4ee5\u4e3d\u4e4e\u6b63\uff0c\u4e43\u5316\u6210\n\u5929\u4e0b\u3002 \u67d4\u4e3d\u4e4e\u4e2d\u6b63\uff0c\u6545\u4ea8\uff1b\u662f\u4ee5\u755c\u725d\u725b\u5409\u4e5f\u3002\n\u8c61\u66f0\uff1a\u660e\u4e24\u4f5c\u79bb\uff0c\u5927\u4eba\u4ee5\u7ee7\u660e\u7167\u4e8e\u56db\u65b9\u3002\n",
        "\n\u3000\u3000\u521d\u4e5d\uff1a\u5c65\u9519\u7136\uff0c\u656c\u4e4b\u65e0\u548e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5c65\u9519\u4e4b\u656c\uff0c\u4ee5\u8f9f\u548e\u4e5f\u3002\n\u3000\u3000\u516d\u4e8c\uff1a\u9ec4\u79bb\uff0c\u5143\u5409\u3002\n\u3000\u8c61\u66f0\uff1a\u9ec4\u79bb\u5143\u5409\uff0c\u5f97\u4e2d\u9053\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e09\uff1a\u65e5\u6603\u4e4b\u79bb\uff0c\u4e0d\u9f13\u7f36\u800c\u6b4c\uff0c\u5219\u5927\u800b\u4e4b\u55df\uff0c\u51f6\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u65e5\u6603\u4e4b\u79bb\uff0c\u4f55\u53ef\u4e45\u4e5f\u3002\n\u3000\u3000\u4e5d\u56db\uff1a\u7a81\u5982\u5176\u6765\u5982\uff0c\u711a\u5982\uff0c\u6b7b\u5982\uff0c\u5f03\u5982\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u7a81\u5982\u5176\u6765\u5982\uff0c\u65e0\u6240\u5bb9\u4e5f\u3002\n\u3000\u3000\u516d\u4e94\uff1a\u51fa\u6d95\u6cb1\u82e5\uff0c\u621a\u55df\u82e5\uff0c\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u516d\u4e94\u4e4b\u5409\uff0c\u79bb\u738b\u516c\u4e5f\u3002\n\u3000\u3000\u4e0a\u4e5d\uff1a\u738b\u7528\u51fa\u5f81\uff0c\u6709\u5609\u6298\u9996\uff0c\u83b7\u5176\u532a\u4e11\uff0c\u65e0\u548e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u738b\u7528\u51fa\u5f81\uff0c\u4ee5\u6b63\u90a6\u4e5f\u3002\n"
    ],
    [
        "\n"
    ],
    [
        "\n\u300a\u6613\u7ecf\u300b\u7b2c\u4e09\u5341\u4e00\u5366 \u54b8 \u6cfd\u5c71\u54b8 \u5151\u4e0a\u826e\u4e0b \n"
    ],
    [
        "\n\u3000\u3000\u54b8\uff1a\u4ea8\uff0c\u5229\u8d1e\uff0c\u53d6\u5973\u5409\u3002\n"
    ],
    [
        "\n\u5f56\u66f0\uff1a\u54b8\uff0c\u611f\u4e5f\u3002\u67d4\u4e0a\u800c\u521a\u4e0b\uff0c\u4e8c\u6c14\u611f\u5e94\u4ee5\u76f8\u4e0e\uff0c\u6b62\u800c\u8bf4\uff0c\u7537\u4e0b\u5973\uff0c\u662f\u4ee5\n\u4ea8\u5229\u8d1e\uff0c\u53d6\u5973\u5409\u4e5f\u3002\u5929\u5730\u611f\u800c\u4e07\u7269\u5316\u751f\uff0c\u5723\u4eba\u611f\u4eba\u5fc3\u800c\u5929\u4e0b\u548c\u5e73\uff1b\n\u89c2\u5176\u6240\u611f\uff0c\u800c\u5929\u5730\u4e07\u7269\u4e4b\u60c5\u53ef\u89c1\u77e3\uff01\n"
    ],
    [
        "\n\u8c61\u66f0\uff1a\u5c71\u4e0a\u6709\u6cfd\uff0c\u54b8\uff1b\u541b\u5b50\u4ee5\u865a\u53d7\u4eba\u3002\n",
        "\n\u3000\u3000\u521d\u516d\uff1a\u54b8\u5176\u62c7\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u54b8\u5176\u62c7\uff0c\u5fd7\u5728\u5916\u4e5f\u3002\n"
    ],
    [
        "\n\u3000\u3000\u516d\u4e8c\uff1a\u54b8\u5176\u8153\uff0c\u51f6\uff0c\u5c45\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u867d\u51f6\uff0c\u5c45\u5409\uff0c\u987a\u4e0d\u5bb3\u4e5f\u3002\n"
    ],
    [
        "\n\u3000\u3000\u4e5d\u4e09\uff1a\u54b8\u5176\u80a1\uff0c\u6267\u5176\u968f\uff0c\u5f80\u541d\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u54b8\u5176\u80a1\uff0c\u4ea6\u4e0d\u5904\u4e5f\u3002 \u5fd7\u5728\u968f\u4eba\uff0c\u6240\u6267\u4e0b\u4e5f\u3002\n"
    ],
    [
        "\n\u3000\u3000\u4e5d\u56db\uff1a\u8d1e\u5409\u6094\u4ea1\uff0c\u61a7\u61a7\u5f80\u6765\uff0c\u670b\u4ece\u5c14\u601d\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u8d1e\u5409\u6094\u4ea1\uff0c\u672a\u611f\u5bb3\u4e5f\u3002 \u61a7\u61a7\u5f80\u6765\uff0c\u672a\u5149\u5927\u4e5f\u3002\n"
    ],
    [
        "\n\u3000\u3000\u4e5d\u4e94\uff1a\u54b8\u5176\u8122\uff0c\u65e0\u6094\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u54b8\u5176\u8122\uff0c\u5fd7\u672b\u4e5f\u3002\n"
    ],
    [
        "\n\u3000\u3000\u4e0a\u516d\uff1a\u54b8\u5176\u8f85\uff0c\u988a\uff0c\u820c\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u54b8\u5176\u8f85\uff0c\u988a\uff0c\u820c\uff0c\u6ed5\u53e3\u8bf4\u4e5f\u3002\n"
    ],
    [
        "\n\u300a\u6613\u7ecf\u300b\u7b2c\u4e09\u5341\u4e8c\u5366 \u6052 \u96f7\u98ce\u6052 \u9707\u4e0a\u5dfd\u4e0b \n\u3000\u3000\u6052\uff1a\u4ea8\uff0c\u65e0\u548e\uff0c\u5229\u8d1e\uff0c\u5229\u6709\u6538\u5f80\u3002\n\u5f56\u66f0\uff1a\u6052\uff0c\u4e45\u4e5f\u3002 \u521a\u4e0a\u800c\u67d4\u4e0b\uff0c\u96f7\u98ce\u76f8\u4e0e\uff0c\u5dfd\u800c\u52a8\uff0c\u521a\u67d4\u7686\u5e94\uff0c\u6052\u3002 \u6052\n\u4ea8\u65e0\u548e\uff0c\u5229\u8d1e\uff1b \u4e45\u65bc\u5176\u9053\u4e5f\uff0c\u5929\u5730\u4e4b\u9053\uff0c\u6052\u4e45\u800c\u4e0d\u5df2\u4e5f\u3002 \u5229\u6709\u6538\n\u5f80\uff0c\u7ec8\u5219\u6709\u59cb\u4e5f\u3002\u65e5\u6708\u5f97\u5929\uff0c\u800c\u80fd\u4e45\u7167\uff0c\u56db\u65f6\u53d8\u5316\uff0c\u800c\u80fd\u4e45\u6210\uff0c\u5723\n\u4eba\u4e45\u65bc\u5176\u9053\uff0c\u800c\u5929\u4e0b\u5316\u6210\uff1b\u89c2\u5176\u6240\u6052\uff0c\u800c\u5929\u5730\u4e07\u7269\u4e4b\u60c5\u53ef\u89c1\u77e3\uff01\n\u8c61\u66f0\uff1a\u96f7\u98ce\uff0c\u6052\uff1b\u541b\u5b50\u4ee5\u7acb\u4e0d\u6613\u65b9\u3002sm.aa963.com\n",
        "\n\u3000\u3000\u521d\u516d\uff1a\u6d5a\u6052\uff0c\u8d1e\u51f6\uff0c\u65e0\u6538\u5229\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u6d5a\u6052\u4e4b\u51f6\uff0c\u59cb\u6c42\u6df1\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e8c\uff1a\u6094\u4ea1\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u4e5d\u4e8c\u6094\u4ea1\uff0c\u80fd\u4e45\u4e2d\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e09\uff1a\u4e0d\u6052\u5176\u5fb7\uff0c\u6216\u627f\u4e4b\u7f9e\uff0c\u8d1e\u541d\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u4e0d\u6052\u5176\u5fb7\uff0c\u65e0\u6240\u5bb9\u4e5f\u3002\n\u3000\u3000\u4e5d\u56db\uff1a\u7530\u65e0\u79bd\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u4e45\u975e\u5176\u4f4d\uff0c\u5b89\u5f97\u79bd\u4e5f\u3002\n\u3000\u3000\u516d\u4e94\uff1a\u6052\u5176\u5fb7\uff0c\u8d1e\uff0c\u5987\u4eba\u5409\uff0c\u592b\u5b50\u51f6\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5987\u4eba\u8d1e\u5409\uff0c\u4ece\u4e00\u800c\u7ec8\u4e5f\u3002 \u592b\u5b50\u5236\u4e49\uff0c\u4ece\u5987\u51f6\u4e5f\u3002\n\u3000\u3000\u4e0a\u516d\uff1a\u632f\u6052\uff0c\u51f6\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u632f\u6052\u5728\u4e0a\uff0c\u5927\u65e0\u529f\u4e5f\u3002\n"
    ],
    [
        "\n\u300a\u6613\u7ecf\u300b\u7b2c\u4e09\u5341\u4e09\u5366 \u906f \u5929\u5c71\u9041 \u4e7e\u4e0a\u826e\u4e0b \n\u3000\u3000\u906f\uff1a\u4ea8\uff0c\u5c0f\u5229\u8d1e\u3002\n\u5f56\u66f0\uff1a\u906f\u4ea8\uff0c\u906f\u800c\u4ea8\u4e5f\u3002 \u521a\u5f53\u4f4d\u800c\u5e94\uff0c\u4e0e\u65f6\u884c\u4e5f\u3002 \u5c0f\u5229\u8d1e\uff0c\u6d78\u800c\u957f\u4e5f\u3002\u906f\u4e4b\u65f6\u4e49\u5927\u77e3\u54c9\uff01\n\u8c61\u66f0\uff1a\u5929\u4e0b\u6709\u5c71\uff0c\u906f\uff1b\u541b\u5b50\u4ee5\u8fdc\u5c0f\u4eba\uff0c\u4e0d\u6076\u800c\u4e25\u3002\n",
        "\n\u3000\u3000\u521d\u516d\uff1a\u906f\u5c3e\uff0c\u5389\uff0c\u52ff\u7528\u6709\u6538\u5f80\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u906f\u5c3e\u4e4b\u5389\uff0c\u4e0d\u5f80\u4f55\u707e\u4e5f\u3002\n\u3000\u3000\u516d\u4e8c\uff1a\u6267\u4e4b\u7528\u9ec4\u725b\u4e4b\u9769\uff0c\u83ab\u4e4b\u80dc\u8bf4\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u6267\u7528\u9ec4\u725b\uff0c\u56fa\u5fd7\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e09\uff1a\u7cfb\u906f\uff0c\u6709\u75be\u5389\uff0c\u755c\u81e3\u59be\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u7cfb\u906f\u4e4b\u5389\uff0c\u6709\u75be\u60eb\u4e5f\u3002 \u755c\u81e3\u59be\u5409\uff0c\u4e0d\u53ef\u5927\u4e8b\u4e5f\u3002\n\u3000\u3000\u4e5d\u56db\uff1a\u597d\u906f\u541b\u5b50\u5409\uff0c\u5c0f\u4eba\u5426\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u541b\u5b50\u597d\u906f\uff0c\u5c0f\u4eba\u5426\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e94\uff1a\u5609\u906f\uff0c\u8d1e\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5609\u906f\u8d1e\u5409\uff0c\u4ee5\u6b63\u5fd7\u4e5f\u3002\n\u3000\u3000\u4e0a\u4e5d\uff1a\u80a5\u906f\uff0c\u65e0\u4e0d\u5229\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u80a5\u906f\uff0c\u65e0\u4e0d\u5229\uff1b\u65e0\u6240\u7591\u4e5f\u3002\n"
    ],
    [
        "\n"
    ],
    [
        "\n \u300a\u6613\u7ecf\u300b\u7b2c\u4e09\u5341\u56db\u5366 \u5927\u58ee \u96f7\u5929\u5927\u58ee \u9707\u4e0a\u4e7e\u4e0b \u3000\u3000\n \u5927\u58ee\uff1a\u5229\u8d1e\u3002\n\u5f56\u66f0\uff1a\u5927\u58ee\uff0c\u5927\u8005\u58ee\u4e5f\u3002 \u521a\u4ee5\u52a8\uff0c\u6545\u58ee\u3002 \u5927\u58ee\u5229\u8d1e\uff1b\u5927\u8005\u6b63\u4e5f\u3002\u6b63\u5927\u800c\n\u5929\u5730\u4e4b\u60c5\u53ef\u89c1\u77e3\uff01\n\u8c61\u66f0\uff1a\u96f7\u5728\u5929\u4e0a\uff0c\u5927\u58ee\uff1b\u541b\u5b50\u4ee5\u975e\u793c\u52ff\u5c65\u3002\n",
        "\n\u3000\u3000\u521d\u4e5d\uff1a\u58ee\u4e8e\u8dbe\uff0c\u5f81\u51f6\uff0c\u6709\u5b5a\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u58ee\u4e8e\u8dbe\uff0c\u5176\u5b5a\u7a77\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e8c\uff1a\u8d1e\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u4e5d\u4e8c\u8d1e\u5409\uff0c\u4ee5\u4e2d\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e09\uff1a\u5c0f\u4eba\u7528\u58ee\uff0c\u541b\u5b50\u7528\u7f54\uff0c\u8d1e\u5389\u3002 \u7f9d\u7f8a\u89e6\u85e9\uff0c\u7fb8\u5176\u89d2\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5c0f\u4eba\u7528\u58ee\uff0c\u541b\u5b50\u7f54\u4e5f\u3002\n\u3000\u3000\u4e5d\u56db\uff1a\u8d1e\u5409\u6094\u4ea1\uff0c\u85e9\u51b3\u4e0d\u7fb8\uff0c\u58ee\u4e8e\u5927\u8206\u4e4b\u8f39\n\u3000\u3000\u8c61\u66f0\uff1a\u85e9\u51b3\u4e0d\u7fb8\uff0c\u5c1a\u5f80\u4e5f\u3002\n\u3000\u3000\u516d\u4e94\uff1a\u4e27\u7f8a\u4e8e\u6613\uff0c\u65e0\u6094\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u4e27\u7f8a\u4e8e\u6613\uff0c\u4f4d\u4e0d\u5f53\u4e5f\u3002\n\u3000\u3000\u4e0a\u516d\uff1a\u7f9d\u7f8a\u89e6\u85e9\uff0c\u4e0d\u80fd\u9000\uff0c\u4e0d\u80fd\u9042\uff0c\u65e0\u6538\u5229\uff0c\u8270\u5219\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u4e0d\u80fd\u9000\uff0c\u4e0d\u80fd\u9042\uff0c\u4e0d\u7965\u4e5f\u3002 \u8270\u5219\u5409\uff0c\u548e\u4e0d\u957f\u4e5f\u3002\n"
    ],
    [
        "\n\u300a\u6613\u7ecf\u300b\u7b2c\u4e09\u5341\u4e94\u5366 \u664b \u706b\u5730\u664b \u79bb\u4e0a\u5764\u4e0b \n\u3000\u3000\u664b\uff1a\u5eb7\u4faf\u7528\u9521\u9a6c\u8543\u5eb6\uff0c\u663c\u65e5\u4e09\u63a5\u3002\n\u5f56\u66f0\uff1a\u664b\uff0c\u8fdb\u4e5f\u3002 \u660e\u51fa\u5730\u4e0a\uff0c\u987a\u800c\u4e3d\u4e4e\u5927\u660e\uff0c\u67d4\u8fdb\u800c\u4e0a\u884c\u3002 \u662f\u4ee5\u5eb7\u4faf\u7528\u9521\u9a6c\u8543\u5eb6\uff0c\u663c\u65e5\u4e09\u63a5\u4e5f\u3002\n\u8c61\u66f0\uff1a\u660e\u51fa\u5730\u4e0a\uff0c\u664b\uff1b\u541b\u5b50\u4ee5\u81ea\u662d\u660e\u5fb7\u3002\n",
        "\n\u3000\u3000\u521d\u516d\uff1a\u664b\u5982\uff0c\u6467\u5982\uff0c\u8d1e\u5409\u3002 \u7f54\u5b5a\uff0c\u88d5\u65e0\u548e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u664b\u5982\uff0c\u6467\u5982\uff1b\u72ec\u884c\u6b63\u4e5f\u3002 \u88d5\u65e0\u548e\uff1b\u672a\u53d7\u547d\u4e5f\u3002\n\u3000\u3000\u516d\u4e8c\uff1a\u664b\u5982\uff0c\u6101\u5982\uff0c\u8d1e\u5409\u3002 \u53d7\u5179\u4ecb\u798f\uff0c\u4e8e\u5176\u738b\u6bcd\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u53d7\u4e4b\u4ecb\u798f\uff0c\u4ee5\u4e2d\u6b63\u4e5f\u3002\n\u3000\u3000\u516d\u4e09\uff1a\u4f17\u5141\uff0c\u6094\u4ea1\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u4f17\u5141\u4e4b\uff0c\u5fd7\u4e0a\u884c\u4e5f\u3002\n\u3000\u3000\u4e5d\u56db\uff1a\u664b\u5982\u7855\u9f20\uff0c\u8d1e\u5389\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u7855\u9f20\u8d1e\u5389\uff0c\u4f4d\u4e0d\u5f53\u4e5f\u3002\n\u3000\u3000\u516d\u4e94\uff1a\u6094\u4ea1\uff0c\u5931\u5f97\u52ff\u6064\uff0c\u5f80\u5409\u65e0\u4e0d\u5229\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5931\u5f97\u52ff\u6064\uff0c\u5f80\u6709\u5e86\u4e5f\u3002\n\u3000\u3000\u4e0a\u4e5d\uff1a\u664b\u5176\u89d2\uff0c\u7ef4\u7528\u4f10\u9091\uff0c\u5389\u5409\u65e0\u548e\uff0c\u8d1e\u541d\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u7ef4\u7528\u4f10\u9091\uff0c\u9053\u672a\u5149\u4e5f\u3002\n"
    ],
    [
        "\n\u300a\u6613\u7ecf\u300b\u7b2c\u4e09\u5341\u516d\u5366 \u660e\u5937 \u5730\u706b\u660e\u5937 \u5764\u4e0a\u79bb\u4e0b \u3000\u3000\n\u660e\u5937\uff1a\u5229\u8270\u8d1e\u3002\n\u5f56\u66f0\uff1a\u660e\u5165\u5730\u4e2d\uff0c\u660e\u5937\u3002 \u5185\u6587\u660e\u800c\u5916\u67d4\u987a\uff0c\u4ee5\u8499\u5927\u96be\uff0c\u6587\u738b\u4ee5\u4e4b\u3002 \u5229\u8270\u8d1e\uff0c\u6666\u5176\u660e\u4e5f\uff0c\u5185\u96be\u800c\u80fd\u6b63\u5176\u5fd7\uff0c\u7b95\u5b50\u4ee5\u4e4b\u3002\n\u8c61\u66f0\uff1a\u660e\u5165\u5730\u4e2d\uff0c\u660e\u5937\uff1b\u541b\u5b50\u4ee5\u8385\u4f17\uff0c\u7528\u6666\u800c\u660e\u3002\n",
        "\n\u3000\u3000\u521d\u4e5d\uff1a\u660e\u5937\u4e8e\u98de\uff0c\u5782\u5176\u7ffc\u3002 \u541b\u5b50\u4e8e\u884c\uff0c\u4e09\u65e5\u4e0d\u98df\uff0c \u6709\u6538\u5f80\uff0c\u4e3b\u4eba\u6709\u8a00\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u541b\u5b50\u4e8e\u884c\uff0c\u4e49\u4e0d\u98df\u4e5f\u3002\n\u3000\u3000\u516d\u4e8c\uff1a\u660e\u5937\uff0c\u5937\u4e8e\u5de6\u80a1\uff0c\u7528\u62ef\u9a6c\u58ee\uff0c\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u516d\u4e8c\u4e4b\u5409\uff0c\u987a\u4ee5\u5219\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e09\uff1a\u660e\u5937\u4e8e\u5357\u72e9\uff0c\u5f97\u5176\u5927\u9996\uff0c\u4e0d\u53ef\u75be\u8d1e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5357\u72e9\u4e4b\u5fd7\uff0c\u4e43\u5927\u5f97\u4e5f\u3002\n\u3000\u3000\u516d\u56db\uff1a\u5165\u4e8e\u5de6\u8179\uff0c\u83b7\u660e\u5937\u4e4b\u5fc3\uff0c\u51fa\u4e8e\u95e8\u5ead\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5165\u4e8e\u5de6\u8179\uff0c\u83b7\u5fc3\u610f\u4e5f\u3002\n\u3000\u3000\u516d\u4e94\uff1a\u7b95\u5b50\u4e4b\u660e\u5937\uff0c\u5229\u8d1e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u7b95\u5b50\u4e4b\u8d1e\uff0c\u660e\u4e0d\u53ef\u606f\u4e5f\u3002\n\u3000\u3000\u4e0a\u516d\uff1a\u4e0d\u660e\u6666\uff0c\u521d\u767b\u4e8e\u5929\uff0c\u540e\u5165\u4e8e\u5730\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u521d\u767b\u4e8e\u5929\uff0c\u7167\u56db\u56fd\u4e5f\u3002 \u540e\u5165\u4e8e\u5730\uff0c\u5931\u5219\u4e5f\u3002\n"
    ],
    [
        "\n\u300a\u6613\u7ecf\u300b\u7b2c\u4e09\u5341\u4e03\u5366 \u5bb6\u4eba \u98ce\u706b\u5bb6\u4eba \u5dfd\u4e0a\u79bb\u4e0b\n\u5bb6\u4eba\uff1a\u5229\u5973\u8d1e\u3002\n\u5f56\u66f0\uff1a\u5bb6\u4eba\uff0c\u5973\u6b63\u4f4d\u4e4e\u5185\uff0c\u7537\u6b63\u4f4d\u4e4e\u5916\uff0c\u7537\u5973\u6b63\uff0c\u5929\u5730\u4e4b\u5927\u4e49\u4e5f\u3002\u5bb6\u4eba\u6709\u4e25\u541b\u7109\uff0c\u7236\u6bcd\u4e4b\u8c13\u4e5f\u3002\u7236\u7236\uff0c\u5b50\u5b50\uff0c\u5144\u5144\uff0c\u5f1f\u5f1f\uff0c\u592b\u592b\uff0c\u5987\u5987\uff0c\u800c\u5bb6\u9053\u6b63\uff1b\u6b63\u5bb6\u800c\u5929\u4e0b\u5b9a\u77e3\u3002\n\u8c61\u66f0\uff1a\u98ce\u81ea\u706b\u51fa\uff0c\u5bb6\u4eba\uff1b\u541b\u5b50\u4ee5\u8a00\u6709\u7269\uff0c\u800c\u884c\u6709\u6052\u3002\n",
        "\n\u3000\u3000\u521d\u4e5d\uff1a\u95f2\u6709\u5bb6\uff0c\u6094\u4ea1\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u95f2\u6709\u5bb6\uff0c\u5fd7\u672a\u53d8\u4e5f\u3002\n\u3000\u3000\u516d\u4e8c\uff1a\u65e0\u6538\u9042\uff0c\u5728\u4e2d\u9988\uff0c\u8d1e\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u516d\u4e8c\u4e4b\u5409\uff0c\u987a\u4ee5\u5dfd\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e09\uff1a\u5bb6\u4eba\u55c3\u55c3\uff0c\u6094\u5389\u5409\uff1b\u5987\u5b50\u563b\u563b\uff0c\u7ec8\u541d\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5bb6\u4eba\u55c3\u55c3\uff0c\u672a\u5931\u4e5f\uff1b\u5987\u5b50\u563b\u563b\uff0c\u5931\u5bb6\u8282\u4e5f\u3002\n\u3000\u3000\u516d\u56db\uff1a\u5bcc\u5bb6\uff0c\u5927\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5bcc\u5bb6\u5927\u5409\uff0c\u987a\u5728\u4f4d\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e94\uff1a\u738b\u5047\u6709\u5bb6\uff0c\u52ff\u6064\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u738b\u5047\u6709\u5bb6\uff0c\u4ea4\u76f8\u7231\u4e5f\u3002\n\u3000\u3000\u4e0a\u4e5d\uff1a\u6709\u5b5a\u5a01\u5982\uff0c\u7ec8\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5a01\u5982\u4e4b\u5409\uff0c\u53cd\u8eab\u4e4b\u8c13\u4e5f\u3002\n"
    ],
    [
        "\n\u300a\u6613\u7ecf\u300b\u7b2c\u4e09\u5341\u516b\u5366 \u777d \u706b\u6cfd\u777d \u79bb\u4e0a\u5151\u4e0b \n\u3000\u3000\u777d\uff1a\u5c0f\u4e8b\u5409\u3002\n\u5f56\u66f0\uff1a\u777d\uff0c\u706b\u52a8\u800c\u4e0a\uff0c\u6cfd\u52a8\u800c\u4e0b\uff1b \u4e8c\u5973\u540c\u5c45\uff0c\u5176\u5fd7\u4e0d\u540c\u884c\uff1b\u8bf4\u800c\u4e3d\u4e4e\u660e\uff0c\u67d4\u8fdb\u800c\u4e0a\u884c\uff0c\u5f97\u4e2d\u800c\u5e94\u4e4e\u521a\uff1b\u662f\u4ee5\u5c0f\u4e8b\u5409\u3002 \u5929\u5730\u777d\uff0c\u800c\u5176\u4e8b\u540c\u4e5f\uff1b\u7537\u5973\u777d\uff0c\u800c\u5176\u5fd7\u901a\u4e5f\uff1b\u4e07\u7269\u777d\uff0c\u800c\u5176\u4e8b\u7c7b\u4e5f\uff1b\u777d\u4e4b\u65f6\u7528\u5927\u77e3\u54c9\uff01\n\u8c61\u66f0\uff1a\u4e0a\u706b\u4e0b\u6cfd\uff0c\u777d\uff1b\u541b\u5b50\u4ee5\u540c\u800c\u5f02\u3002\n",
        "\n\u3000\u3000\u521d\u4e5d\uff1a\u6094\u4ea1\uff0c\u4e27\u9a6c\u52ff\u9010\uff0c\u81ea\u590d\uff1b\u89c1\u6076\u4eba\u65e0\u548e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u89c1\u6076\u4eba\uff0c\u4ee5\u8f9f\u548e\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e8c\uff1a\u9047\u4e3b\u4e8e\u5df7\uff0c\u65e0\u548e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u9047\u4e3b\u4e8e\u5df7\uff0c\u672a\u5931\u9053\u4e5f\u3002\n\u3000\u3000\u516d\u4e09\uff1a\u89c1\u8206\u66f3\uff0c\u5176\u725b\u63a3\uff0c\u5176\u4eba\u5929\u4e14\u5293\uff0c\u65e0\u521d\u6709\u7ec8\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u89c1\u8206\u66f3\uff0c\u4f4d\u4e0d\u5f53\u4e5f\u3002 \u65e0\u521d\u6709\u7ec8\uff0c\u9047\u521a\u4e5f\u3002\n\u3000\u3000\u4e5d\u56db\uff1a\u777d\u5b64\uff0c\u9047\u5143\u592b\uff0c\u4ea4\u5b5a\uff0c\u5389\u65e0\u548e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u4ea4\u5b5a\u65e0\u548e\uff0c\u5fd7\u884c\u4e5f\u3002\n\u3000\u3000\u516d\u4e94\uff1a\u6094\u4ea1\uff0c\u53a5\u5b97\u566c\u80a4\uff0c\u5f80\u4f55\u548e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u53a5\u5b97\u566c\u80a4\uff0c\u5f80\u6709\u5e86\u4e5f\u3002\n\u3000\u3000\u4e0a\u4e5d\uff1a\u777d\u5b64\uff0c \u89c1\u8c55\u8d1f\u6d82\uff0c\u8f7d\u9b3c\u4e00\u8f66\uff0c \u5148\u5f20\u4e4b\u5f27\uff0c\u540e\u8bf4\u4e4b\u5f27\uff0c\u532a\u5bc7\u5a5a\n\u5abe\uff0c\u5f80\u9047\u96e8\u5219\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u9047\u96e8\u4e4b\u5409\uff0c\u7fa4\u7591\u4ea1\u4e5f\u3002\n"
    ],
    [
        "\n\u300a\u6613\u7ecf\u300b\u7b2c\u4e09\u5341\u4e5d\u5366 \u8e47 \u6c34\u5c71\u8e47 \u574e\u4e0a\u826e\u4e0b \n\u3000\u3000\u8e47\uff1a\u5229\u897f\u5357\uff0c\u4e0d\u5229\u4e1c\u5317\uff1b\u5229\u89c1\u5927\u4eba\uff0c\u8d1e\u5409\u3002\n\u5f56\u66f0\uff1a\u8e47\uff0c\u96be\u4e5f\uff0c\u9669\u5728\u524d\u4e5f\u3002 \u89c1\u9669\u800c\u80fd\u6b62\uff0c\u77e5\u77e3\u54c9\uff01\u8e47\u5229\u897f\u5357\uff0c \u5f80\u5f97\u4e2d\n\u4e5f\uff1b\u4e0d\u5229\u4e1c\u5317\uff0c\u5176\u9053\u7a77\u4e5f\u3002 \u5229\u89c1\u5927\u4eba\uff0c\u5f80\u6709\u529f\u4e5f\u3002 \u5f53\u4f4d\u8d1e\u5409\uff0c\u4ee5\n\u6b63\u90a6\u4e5f\u3002 \u8e47\u4e4b\u65f6\u7528\u5927\u77e3\u54c9\uff01\n\u8c61\u66f0\uff1a\u5c71\u4e0a\u6709\u6c34\uff0c\u8e47\uff1b\u541b\u5b50\u4ee5\u53cd\u8eab\u4fee\u5fb7\u3002\n",
        "\n\u3000\u3000\u521d\u516d\uff1a\u5f80\u8e47\uff0c\u6765\u8a89\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5f80\u8e47\u6765\u8a89\uff0c\u5b9c\u5f85\u4e5f\u3002\n\u3000\u3000\u516d\u4e8c\uff1a\u738b\u81e3\u8e47\u8e47\uff0c\u532a\u8eac\u4e4b\u6545\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u738b\u81e3\u8e47\u8e47\uff0c\u7ec8\u65e0\u5c24\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e09\uff1a\u5f80\u8e47\u6765\u53cd\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5f80\u8e47\u6765\u53cd\uff0c\u5185\u559c\u4e4b\u4e5f\u3002\n\u3000\u3000\u516d\u56db\uff1a\u5f80\u8e47\u6765\u8fde\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5f80\u8e47\u6765\u8fde\uff0c\u5f53\u4f4d\u5b9e\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e94\uff1a\u5927\u8e47\u670b\u6765\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5927\u8e47\u670b\u6765\uff0c\u4ee5\u4e2d\u8282\u4e5f\u3002\n\u3000\u3000\u4e0a\u516d\uff1a\u5f80\u8e47\u6765\u7855\uff0c\u5409\uff1b\u5229\u89c1\u5927\u4eba\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5f80\u8e47\u6765\u7855\uff0c\u5fd7\u5728\u5185\u4e5f\u3002 \u5229\u89c1\u5927\u4eba\uff0c\u4ee5\u4ece\u8d35\u4e5f\u3002\n"
    ],
    [
        "\n\u300a\u6613\u7ecf\u300b\u7b2c\u56db\u5341\u5366 \u89e3 \u96f7\u6c34\u89e3 \u9707\u4e0a\u574e\u4e0b \n\u3000\u3000\u89e3\uff1a\u5229\u897f\u5357\uff0c\u65e0\u6240\u5f80\uff0c\u5176\u6765\u590d\u5409\u3002 \u6709\u6538\u5f80\uff0c\u5919\u5409\u3002\n\u5f56\u66f0\uff1a\u89e3\uff0c\u9669\u4ee5\u52a8\uff0c\u52a8\u800c\u514d\u4e4e\u9669\uff0c\u89e3\u3002 \u89e3\u5229\u897f\u5357\uff0c\u5f80\u5f97\u4f17\u4e5f\u3002\u5176\u6765\u590d\u5409\uff0c\u4e43\u5f97\u4e2d\u4e5f\u3002\u6709\u6538\u5f80\u5919\u5409\uff0c\u5f80\u6709\u529f\u4e5f\u3002 \u5929\u5730\u89e3\uff0c\u800c\u96f7\u96e8\u4f5c\uff0c\u96f7\u96e8\u4f5c\uff0c\u800c\u767e\u679c\u8349\u6728\u7686\u7532\u577c\uff0c\u89e3\u4e4b\u65f6\u4e49\u5927\u77e3\u54c9\uff01\n\u8c61\u66f0\uff1a\u96f7\u96e8\u4f5c\uff0c\u89e3\uff1b\u541b\u5b50\u4ee5\u8d66\u8fc7\u5ba5\u7f6a\u3002\n",
        "\n\u3000\u3000\u521d\u516d\uff1a\u65e0\u548e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u521a\u67d4\u4e4b\u9645\uff0c\u4e49\u65e0\u548e\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e8c\uff1a\u7530\u83b7\u4e09\u72d0\uff0c\u5f97\u9ec4\u77e2\uff0c\u8d1e\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u4e5d\u4e8c\u8d1e\u5409\uff0c\u5f97\u4e2d\u9053\u4e5f\u3002\n\u3000\u3000\u516d\u4e09\uff1a\u8d1f\u4e14\u4e58\uff0c\u81f4\u5bc7\u81f3\uff0c\u8d1e\u541d\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u8d1f\u4e14\u4e58\uff0c\u4ea6\u53ef\u4e11\u4e5f\uff0c\u81ea\u6211\u81f4\u620e\uff0c\u53c8\u8c01\u548e\u4e5f\u3002\n\u3000\u3000\u4e5d\u56db\uff1a\u89e3\u800c\u62c7\uff0c\u670b\u81f3\u65af\u5b5a\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u89e3\u800c\u62c7\uff0c\u672a\u5f53\u4f4d\u4e5f\u3002\n\u3000\u3000\u516d\u4e94\uff1a\u541b\u5b50\u7ef4\u6709\u89e3\uff0c\u5409\uff1b\u6709\u5b5a\u4e8e\u5c0f\u4eba\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u541b\u5b50\u6709\u89e3\uff0c\u5c0f\u4eba\u9000\u4e5f\u3002\n\u3000\u3000\u4e0a\u516d\uff1a\u516c\u7528\u5c04\u96bc\uff0c\u4e8e\u9ad8\u5889\u4e4b\u4e0a\uff0c\u83b7\u4e4b\uff0c\u65e0\u4e0d\u5229\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u516c\u7528\u5c04\u96bc\uff0c\u4ee5\u89e3\u6096\u4e5f\u3002\n"
    ],
    [
        "\n\u300a\u6613\u7ecf\u300b\u7b2c\u56db\u5341\u4e00\u5366 \u635f \u5c71\u6cfd\u635f \u826e\u4e0a\u5151\u4e0b \n\u3000\u3000\u635f\uff1a\u6709\u5b5a\uff0c\u5143\u5409\uff0c\u65e0\u548e\uff0c\u53ef\u8d1e\uff0c\u5229\u6709\u6538\u5f80\uff1f \u66f7\u4e4b\u7528\uff0c\u4e8c\u7c0b\u53ef\u7528\u4eab\u3002\n\u5f56\u66f0\uff1a\u635f\uff0c\u635f\u4e0b\u76ca\u4e0a\uff0c\u5176\u9053\u4e0a\u884c\u3002\u635f\u800c\u6709\u5b5a\uff0c\u5143\u5409\uff0c\u65e0\u548e\uff0c\u53ef\u8d1e\uff0c\u5229\u6709\u6538\u5f80\u3002 \u66f7\u4e4b\u7528\uff1f \u4e8c\u7c0b\u53ef\u7528\u4eab\uff1b\u4e8c\u7c0b\u5e94\u6709\u65f6\u3002\u635f\u521a\u76ca\u67d4\u6709\u65f6\uff0c\u635f\u76ca\u76c8\u865a\uff0c\u4e0e\u65f6\u5055\u884c\u3002\n\u8c61\u66f0\uff1a\u5c71\u4e0b\u6709\u6cfd\uff0c\u635f\uff1b\u541b\u5b50\u4ee5\u60e9\u5fff\u7a92\u6b32\u3002\n",
        "\n\u3000\u3000\u521d\u4e5d\uff1a\u5df2\u4e8b\u9044\u5f80\uff0c\u65e0\u548e\uff0c\u914c\u635f\u4e4b\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5df2\u4e8b\u9044\u5f80\uff0c\u5c1a\u5408\u5fd7\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e8c\uff1a\u5229\u8d1e\uff0c\u5f81\u51f6\uff0c\u5f17\u635f\u76ca\u4e4b\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u4e5d\u4e8c\u5229\u8d1e\uff0c\u4e2d\u4ee5\u4e3a\u5fd7\u4e5f\u3002\n\u3000\u3000\u516d\u4e09\uff1a\u4e09\u4eba\u884c\uff0c\u5219\u635f\u4e00\u4eba\uff1b\u4e00\u4eba\u884c\uff0c\u5219\u5f97\u5176\u53cb\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u4e00\u4eba\u884c\uff0c\u4e09\u5219\u7591\u4e5f\u3002\n\u3000\u3000\u516d\u56db\uff1a\u635f\u5176\u75be\uff0c\u4f7f\u9044\u6709\u559c\uff0c\u65e0\u548e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u635f\u5176\u75be\uff0c\u4ea6\u53ef\u559c\u4e5f\u3002\n\u3000\u3000\u516d\u4e94\uff1a\u6216\u76ca\u4e4b\uff0c\u5341\u670b\u4e4b\u9f9f\u5f17\u514b\u8fdd\uff0c\u5143\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u516d\u4e94\u5143\u5409\uff0c\u81ea\u4e0a\u4f51\u4e5f\u3002\n\u3000\u3000\u4e0a\u4e5d\uff1a\u5f17\u635f\u76ca\u4e4b\uff0c\u65e0\u548e\uff0c\u8d1e\u5409\uff0c\u5229\u6709\u6538\u5f80\uff0c\u5f97\u81e3\u65e0\u5bb6\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5f17\u635f\u76ca\u4e4b\uff0c\u5927\u5f97\u5fd7\u4e5f\u3002\n"
    ],
    [
        "\n\u300a\u6613\u7ecf\u300b\u7b2c\u56db\u5341\u4e8c\u5366 \u76ca \u98ce\u96f7\u76ca \u5dfd\u4e0a\u9707\u4e0b \n\u3000\u3000\u76ca\uff1a\u5229\u6709\u6538\u5f80\uff0c\u5229\u6d89\u5927\u5ddd\u3002\n\u5f56\u66f0\uff1a\u76ca\uff0c\u635f\u4e0a\u76ca\u4e0b\uff0c\u6c11\u8bf4\u65e0\u7586\uff0c\u81ea\u4e0a\u4e0b\u4e0b\uff0c\u5176\u9053\u5927\u5149\u3002\u5229\u6709\u6538\u5f80\uff0c\u4e2d\u6b63\u6709\u5e86\u3002 \u5229\u6d89\u5927\u5ddd\uff0c\u6728\u9053\u4e43\u884c\u3002 \u76ca\u52a8\u800c\u5dfd\uff0c\u65e5\u8fdb\u65e0\u7586\u3002 \u5929\u65bd\u5730\u751f\uff0c\u5176\u76ca\u65e0\u65b9\u3002 \u51e1\u76ca\u4e4b\u9053\uff0c\u4e0e\u65f6\u5055\u884c\u3002\n\u8c61\u66f0\uff1a\u98ce\u96f7\uff0c\u76ca\uff1b\u541b\u5b50\u4ee5\u89c1\u5584\u5219\u8fc1\uff0c\u6709\u8fc7\u5219\u6539\u3002\n",
        "\n\u3000\u3000\u521d\u4e5d\uff1a\u5229\u7528\u4e3a\u5927\u4f5c\uff0c\u5143\u5409\uff0c\u65e0\u548e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5143\u5409\u65e0\u548e\uff0c\u4e0b\u4e0d\u539a\u4e8b\u4e5f\u3002\n\u3000\u3000\u516d\u4e8c\uff1a\u6216\u76ca\u4e4b\uff0c\u5341\u670b\u4e4b\u9f9f\u5f17\u514b\u8fdd\uff0c\u6c38\u8d1e\u5409\u3002 \u738b\u7528\u4eab\u4e8e\u5e1d\uff0c\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u6216\u76ca\u4e4b\uff0c\u81ea\u5916\u6765\u4e5f\u3002\n\u3000\u3000\u516d\u4e09\uff1a\u76ca\u4e4b\u7528\u51f6\u4e8b\uff0c\u65e0\u548e\u3002 \u6709\u5b5a\u4e2d\u884c\uff0c\u544a\u516c\u7528\u572d\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u76ca\u7528\u51f6\u4e8b\uff0c\u56fa\u6709\u4e4b\u4e5f\u3002\n\u3000\u3000\u516d\u56db\uff1a\u4e2d\u884c\uff0c\u544a\u516c\u4ece\u3002 \u5229\u7528\u4e3a\u4f9d\u8fc1\u56fd\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u544a\u516c\u4ece\uff0c\u4ee5\u76ca\u5fd7\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e94\uff1a\u6709\u5b5a\u60e0\u5fc3\uff0c\u52ff\u95ee\u5143\u5409\u3002 \u6709\u5b5a\u60e0\u6211\u5fb7\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u6709\u5b5a\u60e0\u5fc3\uff0c\u52ff\u95ee\u4e4b\u77e3\u3002 \u60e0\u6211\u5fb7\uff0c\u5927\u5f97\u5fd7\u4e5f\u3002\n\u3000\u3000\u4e0a\u4e5d\uff1a\u83ab\u76ca\u4e4b\uff0c\u6216\u51fb\u4e4b\uff0c\u7acb\u5fc3\u52ff\u6052\uff0c\u51f6\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u83ab\u76ca\u4e4b\uff0c\u504f\u8f9e\u4e5f\u3002 \u6216\u51fb\u4e4b\uff0c\u81ea\u5916\u6765\u4e5f\u3002\n"
    ],
    [
        "\n\u300a\u6613\u7ecf\u300b\u7b2c\u56db\u5341\u4e09\u5366 \u592c \u6cfd\u5929\u592c \u5151\u4e0a\u4e7e\u4e0b \n\u3000\u3000\u592c\uff1a\u626c\u4e8e\u738b\u5ead\uff0c\u5b5a\u53f7\uff0c\u6709\u5389\uff0c\u544a\u81ea\u9091\uff0c\u4e0d\u5229\u5373\u620e\uff0c\u5229\u6709\u6538\u5f80\u3002\n\u5f56\u66f0\uff1a\u592c\uff0c\u51b3\u4e5f\uff0c\u521a\u51b3\u67d4\u4e5f\u3002\u5065\u800c\u8bf4\uff0c\u51b3\u800c\u548c\uff0c\u626c\u4e8e\u738b\u5ead\uff0c\u67d4\u4e58\u4e94\u521a\u4e5f\u3002\u5b5a\u53f7\u6709\u5389\uff0c\u5176\u5371\u4e43\u5149\u4e5f\u3002 \u544a\u81ea\u9091\uff0c\u4e0d\u5229\u5373\u620e\uff0c\u6240\u5c1a\u4e43\u7a77\u4e5f\u3002 \u5229\u6709\u6538\u5f80\uff0c\u521a\u957f\u4e43\u7ec8\u4e5f\u3002sm.aa963.com\n\u8c61\u66f0\uff1a\u6cfd\u4e0a\u4e8e\u5929\uff0c\u592c\uff1b\u541b\u5b50\u4ee5\u65bd\u7984\u53ca\u4e0b\uff0c\u5c45\u5fb7\u5219\u5fcc\u3002\n",
        "\n\u3000\u3000\u521d\u4e5d\uff1a\u58ee\u4e8e\u524d\u8dbe\uff0c\u5f80\u4e0d\u80dc\u4e3a\u541d\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u4e0d\u80dc\u800c\u5f80\uff0c\u548e\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e8c\uff1a\u60d5\u53f7\uff0c\u83ab\u591c\u6709\u620e\uff0c\u52ff\u6064\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u83ab\u591c\u6709\u620e\uff0c\u5f97\u4e2d\u9053\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e09\uff1a\u58ee\u4e8e\u9804\uff0c\u6709\u51f6\u3002 \u541b\u5b50\u592c\u592c\uff0c\u72ec\u884c\u9047\u96e8\uff0c\u82e5\u6fe1\u6709\u6120\uff0c\u65e0\u548e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u541b\u5b50\u592c\u592c\uff0c\u7ec8\u65e0\u548e\u4e5f\u3002\n\u3000\u3000\u4e5d\u56db\uff1a\u81c0\u65e0\u80a4\uff0c\u5176\u884c\u6b21\u4e14\u3002 \u7275\u7f8a\u6094\u4ea1\uff0c\u95fb\u8a00\u4e0d\u4fe1\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5176\u884c\u6b21\u4e14\uff0c\u4f4d\u4e0d\u5f53\u4e5f\u3002 \u95fb\u8a00\u4e0d\u4fe1\uff0c\u806a\u4e0d\u660e\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e94\uff1a\u82cb\u9646\u592c\u592c\uff0c\u4e2d\u884c\u65e0\u548e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u4e2d\u884c\u65e0\u548e\uff0c\u4e2d\u672a\u5149\u4e5f\u3002\n\u3000\u3000\u4e0a\u516d\uff1a\u65e0\u53f7\uff0c\u7ec8\u6709\u51f6\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u65e0\u53f7\u4e4b\u51f6\uff0c\u7ec8\u4e0d\u53ef\u957f\u4e5f\u3002\n"
    ],
    [
        "\n\u300a\u6613\u7ecf\u300b\u7b2c\u56db\u5341\u56db\u5366 \u59e4 \u5929\u98ce\u59e4 \u4e7e\u4e0a\u5dfd\u4e0b \n\u3000\u3000\u59e4\uff1a\u5973\u58ee\uff0c\u52ff\u7528\u53d6\u5973\u3002\n\u5f56\u66f0\uff1a\u59e4\uff0c\u9047\u4e5f\uff0c\u67d4\u9047\u521a\u4e5f\u3002\u52ff\u7528\u53d6\u5973\uff0c\u4e0d\u53ef\u4e0e\u957f\u4e5f\u3002 \u5929\u5730\u76f8\u9047\uff0c\u54c1\u7269\u54b8\u7ae0\u4e5f\u3002 \u521a\u9047\u4e2d\u6b63\uff0c\u5929\u4e0b\u5927\u884c\u4e5f\u3002 \u59e4\u4e4b\u65f6\u4e49\u5927\u77e3\u54c9\uff01\n\u8c61\u66f0\uff1a\u5929\u4e0b\u6709\u98ce\uff0c\u59e4\uff1b\u540e\u4ee5\u65bd\u547d\u8bf0\u56db\u65b9\u3002\n",
        "\n\u3000\u3000\u521d\u516d\uff1a\u7cfb\u4e8e\u91d1\u67c5\uff0c\u8d1e\u5409\uff0c\u6709\u6538\u5f80\uff0c\u89c1\u51f6\uff0c\u7fb8\u8c55\u8e1f\u8e85\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u7cfb\u4e8e\u91d1\u67c5\uff0c\u67d4\u9053\u7275\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e8c\uff1a\u5305\u6709\u9c7c\uff0c\u65e0\u548e\uff0c\u4e0d\u5229\u5bbe\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5305\u6709\u9c7c\uff0c\u4e49\u4e0d\u53ca\u5bbe\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e09\uff1a\u81c0\u65e0\u80a4\uff0c\u5176\u884c\u6b21\u4e14\uff0c\u5389\uff0c\u65e0\u5927\u548e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5176\u884c\u6b21\u4e14\uff0c\u884c\u672a\u7275\u4e5f\u3002\n\u3000\u3000\u4e5d\u56db\uff1a\u5305\u65e0\u9c7c\uff0c\u8d77\u51f6\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u65e0\u9c7c\u4e4b\u51f6\uff0c\u8fdc\u6c11\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e94\uff1a\u4ee5\u675e\u5305\u74dc\uff0c\u542b\u7ae0\uff0c\u6709\u9668\u81ea\u5929\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u4e5d\u4e94\u542b\u7ae0\uff0c\u4e2d\u6b63\u4e5f\u3002 \u6709\u9668\u81ea\u5929\uff0c\u5fd7\u4e0d\u820d\u547d\u4e5f\u3002\n\u3000\u3000\u4e0a\u4e5d\uff1a\u59e4\u5176\u89d2\uff0c\u541d\uff0c\u65e0\u548e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u59e4\u5176\u89d2\uff0c\u4e0a\u7a77\u541d\u4e5f\u3002\n"
    ],
    [
        "\n\u300a\u6613\u7ecf\u300b\u7b2c\u56db\u5341\u4e94\u5366 \u8403 \u6cfd\u5730\u8403 \u5151\u4e0a\u5764\u4e0b\n\u8403\uff1a\u4ea8\u3002 \u738b\u5047\u6709\u5e99\uff0c\u5229\u89c1\u5927\u4eba\uff0c\u4ea8\uff0c\u5229\u8d1e\u3002 \u7528\u5927\u7272\u5409\uff0c\u5229\u6709\u6538\u5f80\u3002\n\u5f56\u66f0\uff1a\u8403\uff0c\u805a\u4e5f\uff1b\u987a\u4ee5\u8bf4\uff0c\u521a\u4e2d\u800c\u5e94\uff0c\u6545\u805a\u4e5f\u3002\u738b\u5047\u6709\u5e99\uff0c\u81f4\u5b5d\u4eab\u4e5f\u3002\u5229\u89c1\u5927\u4eba\u4ea8\uff0c\u805a\u4ee5\u6b63\u4e5f\u3002 \u7528\u5927\u7272\u5409\uff0c\u5229\u6709\u6538\u5f80\uff0c\u987a\u5929\u547d\u4e5f\u3002 \u89c2\u5176\u6240\u805a\uff0c\u800c\u5929\u5730\u4e07\u7269\u4e4b\u60c5\u53ef\u89c1\u77e3\u3002\n\u8c61\u66f0\uff1a\u6cfd\u4e0a\u65bc\u5730\uff0c\u8403\uff1b\u541b\u5b50\u4ee5\u9664\u620e\u5668\uff0c\u6212\u4e0d\u865e\u3002\n",
        "\n\u3000\u3000\u521d\u516d\uff1a\u6709\u5b5a\u4e0d\u7ec8\uff0c\u4e43\u4e71\u4e43\u8403\uff0c\u82e5\u53f7\u4e00\u63e1\u4e3a\u7b11\uff0c\u52ff\u6064\uff0c\u5f80\u65e0\u548e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u4e43\u4e71\u4e43\u8403\uff0c\u5176\u5fd7\u4e71\u4e5f\u3002\n\u3000\u3000\u516d\u4e8c\uff1a\u5f15\u5409\uff0c\u65e0\u548e\uff0c\u5b5a\u4e43\u5229\u7528\u79b4\u3002\n    \u8c61\u66f0\uff1a\u5f15\u5409\u65e0\u548e\uff0c\u4e2d\u672a\u53d8\u4e5f\u3002 \n\u3000\u3000\u516d\u4e09\uff1a\u8403\u5982\uff0c\u55df\u5982\uff0c\u65e0\u6538\u5229\uff0c\u5f80\u65e0\u548e\uff0c\u5c0f\u541d\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5f80\u65e0\u548e\uff0c\u4e0a\u5dfd\u4e5f\u3002\n\u3000\u3000\u4e5d\u56db\uff1a\u5927\u5409\uff0c\u65e0\u548e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5927\u5409\u65e0\u548e\uff0c\u4f4d\u4e0d\u5f53\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e94\uff1a\u8403\u6709\u4f4d\uff0c\u65e0\u548e\u3002 \u532a\u5b5a\uff0c\u5143\u6c38\u8d1e\uff0c\u6094\u4ea1\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u8403\u6709\u4f4d\uff0c\u5fd7\u672a\u5149\u4e5f\u3002\n\u3000\u3000\u4e0a\u516d\uff1a\u9f4e\u54a8\u6d95\u6d1f\uff0c\u65e0\u548e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u9f4e\u54a8\u6d95\u6d1f\uff0c\u672a\u5b89\u4e0a\u4e5f\u3002\n"
    ],
    [
        "\n\u300a\u6613\u7ecf\u300b\u7b2c\u56db\u5341\u516d\u5366 \u5347 \u5730\u98ce\u5347 \u5764\u4e0a\u5dfd\u4e0b \n\u3000\u3000\u5347\uff1a\u5143\u4ea8\uff0c\u7528\u89c1\u5927\u4eba\uff0c\u52ff\u6064\uff0c\u5357\u5f81\u5409\u3002\n\u5f56\u66f0\uff1a\u67d4\u4ee5\u65f6\u5347\uff0c\u5dfd\u800c\u987a\uff0c\u521a\u4e2d\u800c\u5e94\uff0c\u662f\u4ee5\u5927\u4ea8\u3002\u7528\u89c1\u5927\u4eba\uff0c\u52ff\u6064\uff1b\u6709\u5e86\u4e5f\u3002 \u5357\u5f81\u5409\uff0c\u5fd7\u884c\u4e5f\u3002\n\u8c61\u66f0\uff1a\u5730\u4e2d\u751f\u6728\uff0c\u5347\uff1b\u541b\u5b50\u4ee5\u987a\u5fb7\uff0c\u79ef\u5c0f\u4ee5\u9ad8\u5927\u3002\n",
        "\n\u3000\u3000\u521d\u516d\uff1a\u5141\u5347\uff0c\u5927\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5141\u5347\u5927\u5409\uff0c\u4e0a\u5408\u5fd7\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e8c\uff1a\u5b5a\u4e43\u5229\u7528\u79b4\uff0c\u65e0\u548e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u4e5d\u4e8c\u4e4b\u5b5a\uff0c\u6709\u559c\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e09\uff1a\u5347\u865a\u9091\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5347\u865a\u9091\uff0c\u65e0\u6240\u7591\u4e5f\u3002\n\u3000\u3000\u516d\u56db\uff1a\u738b\u7528\u4ea8\u4e8e\u5c90\u5c71\uff0c\u5409\u65e0\u548e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u738b\u7528\u4ea8\u4e8e\u5c90\u5c71\uff0c\u987a\u4e8b\u4e5f\u3002\n\u3000\u3000\u516d\u4e94\uff1a\u8d1e\u5409\uff0c\u5347\u9636\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u8d1e\u5409\u5347\u9636\uff0c\u5927\u5f97\u5fd7\u4e5f\u3002\n\u3000\u3000\u4e0a\u516d\uff1a\u51a5\u5347\uff0c\u5229\u4e8e\u4e0d\u606f\u4e4b\u8d1e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u51a5\u5347\u5728\u4e0a\uff0c\u6d88\u4e0d\u5bcc\u4e5f\u3002\n"
    ],
    [
        "\n\u300a\u6613\u7ecf\u300b\u7b2c\u56db\u5341\u4e03\u5366 \u56f0 \u6cfd\u6c34\u56f0 \u5151\u4e0a\u574e\u4e0b \n\u3000\u3000\u56f0\uff1a\u4ea8\uff0c\u8d1e\uff0c\u5927\u4eba\u5409\uff0c\u65e0\u548e\uff0c\u6709\u8a00\u4e0d\u4fe1\u3002\n\u5f56\u66f0\uff1a\u56f0\uff0c\u521a\u63a9\u4e5f\u3002 \u9669\u4ee5\u8bf4\uff0c\u56f0\u800c\u4e0d\u5931\u5176\u6240\uff0c\u4ea8\uff1b\u5176\u552f\u541b\u5b50\u4e4e\uff1f \u8d1e\u5927\u4eba\u5409\uff0c\u4ee5\u521a\u4e2d\u4e5f\u3002 \u6709\u8a00\u4e0d\u4fe1\uff0c\u5c1a\u53e3\u4e43\u7a77\u4e5f\u3002\n\u8c61\u66f0\uff1a\u6cfd\u65e0\u6c34\uff0c\u56f0\uff1b\u541b\u5b50\u4ee5\u81f4\u547d\u9042\u5fd7\u3002\n",
        "\n\u3000\u3000\u521d\u516d\uff1a\u81c0\u56f0\u4e8e\u682a\u6728\uff0c\u5165\u4e8e\u5e7d\u8c37\uff0c\u4e09\u5c81\u4e0d\u89c1\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5165\u4e8e\u5e7d\u8c37\uff0c\u5e7d\u4e0d\u660e\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e8c\uff1a\u56f0\u4e8e\u9152\u98df\uff0c\u6731\u7ec2\u65b9\u6765\uff0c\u5229\u7528\u4ea8\u7940\uff0c\u5f81\u51f6\uff0c\u65e0\u548e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u56f0\u4e8e\u9152\u98df\uff0c\u4e2d\u6709\u5e86\u4e5f\u3002\n\u3000\u3000\u516d\u4e09\uff1a\u56f0\u4e8e\u77f3\uff0c\u636e\u4e8e\u84ba\u85dc\uff0c\u5165\u4e8e\u5176\u5bab\uff0c\u4e0d\u89c1\u5176\u59bb\uff0c\u51f6\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u636e\u4e8e\u84ba\u85dc\uff0c\u4e58\u521a\u4e5f\u3002 \u5165\u4e8e\u5176\u5bab\uff0c\u4e0d\u89c1\u5176\u59bb\uff0c\u4e0d\u7965\u4e5f\u3002\n\u3000\u3000\u4e5d\u56db\uff1a\u6765\u5f90\u5f90\uff0c\u56f0\u4e8e\u91d1\u8f66\uff0c\u541d\uff0c\u6709\u7ec8\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u6765\u5f90\u5f90\uff0c\u5fd7\u5728\u4e0b\u4e5f\u3002 \u867d\u4e0d\u5f53\u4f4d\uff0c\u6709\u4e0e\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e94\uff1a\u5293\u5216\uff0c\u56f0\u4e8e\u8d64\u7ec2\uff0c\u4e43\u5f90\u6709\u8bf4\uff0c\u5229\u7528\u796d\u7940\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5293\u5216\uff0c\u5fd7\u672a\u5f97\u4e5f\u3002\u4e43\u5f90\u6709\u8bf4\uff0c\u4ee5\u4e2d\u76f4\u4e5f\u3002\u5229\u7528\u796d\u7940\uff0c\u53d7\u798f\u4e5f\u3002\n\u3000\u3000\u4e0a\u516d\uff1a\u56f0\u4e8e\u845b\u85df\uff0c\u4e8e\u81f2\u9770\uff0c\u66f0\u52a8\u6094\u3002 \u6709\u6094\uff0c\u5f81\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u56f0\u4e8e\u845b\u85df\uff0c\u672a\u5f53\u4e5f\u3002 \u52a8\u6094\uff0c\u6709\u6094\u5409\uff0c\u884c\u4e5f\u3002\n"
    ],
    [
        "\n\u300a\u6613\u7ecf\u300b\u7b2c\u56db\u5341\u516b\u5366 \u4e95 \u6c34\u98ce\u4e95 \u574e\u4e0a\u5dfd\u4e0b \n\u3000\u3000\u4e95\uff1a\u6539\u9091\u4e0d\u6539\u4e95\uff0c\u65e0\u4e27\u65e0\u5f97\uff0c\u5f80\u6765\u4e95\u4e95\u3002\u6c54\u81f3\uff0c\u4ea6\u672a\u7e58\u4e95\uff0c\u7fb8\u5176\u74f6\uff0c\u51f6\u3002\n\u5f56\u66f0\uff1a\u5dfd\u4e4e\u6c34\u800c\u4e0a\u6c34\uff0c\u4e95\uff1b\u4e95\u517b\u800c\u4e0d\u7a77\u4e5f\u3002\u6539\u9091\u4e0d\u6539\u4e95\uff0c\u4e43\u4ee5\u521a\u4e2d\u4e5f\u3002\u6c54\u81f3\u4ea6\u672a\u7e58\u4e95\uff0c\u672a\u6709\u529f\u4e5f\u3002 \u7fb8\u5176\u74f6\uff0c\u662f\u4ee5\u51f6\u4e5f\u3002\n\u8c61\u66f0\uff1a\u6728\u4e0a\u6709\u6c34\uff0c\u4e95\uff1b\u541b\u5b50\u4ee5\u52b3\u6c11\u529d\u76f8\u3002\n",
        "\n\u3000\u3000\u521d\u516d\uff1a\u4e95\u6ce5\u4e0d\u98df\uff0c\u65e7\u4e95\u65e0\u79bd\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u4e95\u6ce5\u4e0d\u98df\uff0c\u4e0b\u4e5f\u3002 \u65e7\u4e95\u65e0\u79bd\uff0c\u65f6\u820d\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e8c\uff1a\u4e95\u8c37\u5c04\u9c8b\uff0c\u74ee\u655d\u6f0f\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u4e95\u8c37\u5c04\u9c8b\uff0c\u65e0\u4e0e\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e09\uff1a\u4e95\u6e2b\u4e0d\u98df\uff0c\u4e3a\u6211\u6c11\u607b\uff0c\u53ef\u7528\u6c72\uff0c\u738b\u660e\uff0c\u5e76\u53d7\u5176\u798f\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u4e95\u6e2b\u4e0d\u98df\uff0c\u884c\u607b\u4e5f\u3002 \u6c42\u738b\u660e\uff0c\u53d7\u798f\u4e5f\u3002\n\u3000\u3000\u516d\u56db\uff1a\u4e95\u7503\uff0c\u65e0\u548e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u4e95\u7503\u65e0\u548e\uff0c\u4fee\u4e95\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e94\uff1a\u4e95\u51bd\uff0c\u5bd2\u6cc9\u98df\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5bd2\u6cc9\u4e4b\u98df\uff0c\u4e2d\u6b63\u4e5f\u3002\n\u3000\u3000\u4e0a\u516d\uff1a\u4e95\u6536\u52ff\u5e55\uff0c\u6709\u5b5a\u65e0\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5143\u5409\u5728\u4e0a\uff0c\u5927\u6210\u4e5f\u3002\n"
    ],
    [
        "\n\u300a\u6613\u7ecf\u300b\u7b2c\u56db\u5341\u4e5d\u5366 \u9769 \u6cfd\u706b\u9769 \u5151\u4e0a\u79bb\u4e0b\u3000\u3000\n\u9769\uff1a\u5df1\u65e5\u4e43\u5b5a\uff0c\u5143\u4ea8\u5229\u8d1e\uff0c\u6094\u4ea1\u3002\n\u5f56\u66f0\uff1a\u9769\uff0c\u6c34\u706b\u76f8\u606f\uff0c\u4e8c\u5973\u540c\u5c45\uff0c\u5176\u5fd7\u4e0d\u76f8\u5f97\uff0c\u66f0\u9769\u3002\u5df1\u65e5\u4e43\u5b5a\uff1b\u9769\u800c\u4fe1\u4e5f\u3002 \u6587\u660e\u4ee5\u8bf4\uff0c\u5927\u4ea8\u4ee5\u6b63\uff0c\u9769\u800c\u5f53\uff0c\u5176\u6094\u4e43\u4ea1\u3002\u5929\u5730\u9769\u800c\u56db\u65f6\u6210\uff0c\u6c64\u6b66\u9769\u547d\uff0c\u987a\u4e4e\u5929\u800c\u5e94\u4e4e\u4eba\uff0c\u9769\u4e4b\u65f6\u4e49\u5927\u77e3\u54c9\uff01\n\u8c61\u66f0\uff1a\u6cfd\u4e2d\u6709\u706b\uff0c\u9769\uff1b\u541b\u5b50\u4ee5\u6cbb\u5386\u660e\u65f6\u3002\n",
        "\n\u3000\u3000\u521d\u4e5d\uff1a\u5de9\u7528\u9ec4\u725b\u4e4b\u9769\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5de9\u7528\u9ec4\u725b\uff0c\u4e0d\u53ef\u4ee5\u6709\u4e3a\u4e5f\u3002\n\u3000\u3000\u516d\u4e8c\uff1a\u5df1\u65e5\u4e43\u9769\u4e4b\uff0c\u5f81\u5409\uff0c\u65e0\u548e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5df1\u65e5\u9769\u4e4b\uff0c\u884c\u6709\u5609\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e09\uff1a\u5f81\u51f6\uff0c\u8d1e\u5389\uff0c\u9769\u8a00\u4e09\u5c31\uff0c\u6709\u5b5a\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u9769\u8a00\u4e09\u5c31\uff0c\u53c8\u4f55\u4e4b\u77e3\u3002\n\u3000\u3000\u4e5d\u56db\uff1a\u6094\u4ea1\uff0c\u6709\u5b5a\u6539\u547d\uff0c\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u6539\u547d\u4e4b\u5409\uff0c\u4fe1\u5fd7\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e94\uff1a\u5927\u4eba\u864e\u53d8\uff0c\u672a\u5360\u6709\u5b5a\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5927\u4eba\u864e\u53d8\uff0c\u5176\u6587\u70b3\u4e5f\u3002\n\u3000\u3000\u4e0a\u516d\uff1a\u541b\u5b50\u8c79\u53d8\uff0c\u5c0f\u4eba\u9769\u9762\uff0c\u5f81\u51f6\uff0c\u5c45\u8d1e\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u541b\u5b50\u8c79\u53d8\uff0c\u5176\u6587\u851a\u4e5f\u3002 \u5c0f\u4eba\u9769\u9762\uff0c\u987a\u4ee5\u4ece\u541b\u4e5f\u3002\n"
    ],
    [
        "\n\u300a\u6613\u7ecf\u300b\u7b2c\u4e94\u5341\u5366 \u9f0e \u706b\u98ce\u9f0e \u79bb\u4e0a\u5dfd\u4e0b \n\u3000\u3000\u9f0e\uff1a\u5143\u5409\uff0c\u4ea8\u3002\n\u5f56\u66f0\uff1a\u9f0e\uff0c\u8c61\u4e5f\u3002 \u4ee5\u6728\u5dfd\u706b\uff0c\u4ea8\u996a\u4e5f\u3002 \u5723\u4eba\u4ea8\u4ee5\u4eab\u4e0a\u5e1d\uff0c\u800c\u5927\u4ea8\u4ee5\u517b\u5723\u8d24\u3002 \u5dfd\u800c\u8033\u76ee\u806a\u660e\uff0c\u67d4\u8fdb\u800c\u4e0a\u884c\uff0c\u5f97\u4e2d\u800c\u5e94\u4e4e\u521a\uff0c\u662f\u4ee5\u5143\u4ea8\u3002\n\u8c61\u66f0\uff1a\u6728\u4e0a\u6709\u706b\uff0c\u9f0e\uff1b\u541b\u5b50\u4ee5\u6b63\u4f4d\u51dd\u547d\u3002\n",
        "\n\u3000\u3000\u521d\u516d\uff1a\u9f0e\u98a0\u8dbe\uff0c\u5229\u51fa\u5426\uff0c\u5f97\u59be\u4ee5\u5176\u5b50\uff0c\u65e0\u548e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u9f0e\u98a0\u8dbe\uff0c\u672a\u6096\u4e5f\u3002 \u5229\u51fa\u5426\uff0c\u4ee5\u4ece\u8d35\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e8c\uff1a\u9f0e\u6709\u5b9e\uff0c\u6211\u4ec7\u6709\u75be\uff0c\u4e0d\u6211\u80fd\u5373\uff0c\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u9f0e\u6709\u5b9e\uff0c\u614e\u6240\u4e4b\u4e5f\u3002 \u6211\u4ec7\u6709\u75be\uff0c\u7ec8\u65e0\u5c24\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e09\uff1a\u9f0e\u8033\u9769\uff0c\u5176\u884c\u585e\uff0c\u96c9\u818f\u4e0d\u98df\uff0c\u65b9\u96e8\u4e8f\u6094\uff0c\u7ec8\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u9f0e\u8033\u9769\uff0c\u5931\u5176\u4e49\u4e5f\u3002\n\u3000\u3000\u4e5d\u56db\uff1a\u9f0e\u6298\u8db3\uff0c\u8986\u516c\u9917\uff0c\u5176\u5f62\u6e25\uff0c\u51f6\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u8986\u516c\u9917\uff0c\u4fe1\u5982\u4f55\u4e5f\u3002\n\u3000\u3000\u516d\u4e94\uff1a\u9f0e\u9ec4\u8033\u91d1\u94c9\uff0c\u5229\u8d1e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u9f0e\u9ec4\u8033\uff0c\u4e2d\u4ee5\u4e3a\u5b9e\u4e5f\u3002\n\u3000\u3000\u4e0a\u4e5d\uff1a\u9f0e\u7389\u94c9\uff0c\u5927\u5409\uff0c\u65e0\u4e0d\u5229\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u7389\u94c9\u5728\u4e0a\uff0c\u521a\u67d4\u8282\u4e5f\u3002\n"
    ],
    [
        "\n\u300a\u6613\u7ecf\u300b\u7b2c\u4e94\u5341\u4e00\u5366 \u9707 \u9707\u4e3a\u96f7 \u9707\u4e0a\u9707\u4e0b \n\u3000\u3000\u9707\uff1a\u4ea8\u3002 \u9707\u6765\u8669\u8669\uff0c\u7b11\u8a00\u54d1\u54d1\u3002 \u9707\u60ca\u767e\u91cc\uff0c\u4e0d\u4e27\u5315\u9b2f\u3002\n\u5f56\u66f0\uff1a\u9707\uff0c\u4ea8\u3002 \u9707\u6765\u8669\u8669\uff0c\u6050\u81f4\u798f\u4e5f\u3002\u7b11\u8a00\u54d1\u54d1\uff0c\u540e\u6709\u5219\u4e5f\u3002 \u9707\u60ca\u767e\u91cc\uff0c\u60ca\u8fdc\u800c\u60e7\u8fe9\u4e5f\u3002 \u51fa\u53ef\u4ee5\u5b88\u5b97\u5e99\u793e\u7a37\uff0c\u4ee5\u4e3a\u796d\u4e3b\u4e5f\u3002\n\u8c61\u66f0\uff1a\u6d0a\u96f7\uff0c\u9707\uff1b\u541b\u5b50\u4ee5\u6050\u60e7\u4fee\u8eab\u3002\n",
        "\n\u3000\u3000\u521d\u4e5d\uff1a\u9707\u6765\u8669\u8669\uff0c\u540e\u7b11\u8a00\u54d1\u54d1\uff0c\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u9707\u6765\u8669\u8669\uff0c\u6050\u81f4\u798f\u4e5f\u3002 \u7b11\u8a00\u54d1\u54d1\uff0c\u540e\u6709\u5219\u4e5f\u3002\n\u3000\u3000\u516d\u4e8c\uff1a\u9707\u6765\u5389\uff0c\u4ebf\u4e27\u8d1d\uff0c\u8dfb\u4e8e\u4e5d\u9675\uff0c\u52ff\u9010\uff0c\u4e03\u65e5\u5f97\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u9707\u6765\u5389\uff0c\u4e58\u521a\u4e5f\u3002\n\u3000\u3000\u516d\u4e09\uff1a\u9707\u82cf\u82cf\uff0c\u9707\u884c\u65e0\u771a\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u9707\u82cf\u82cf\uff0c\u4f4d\u4e0d\u5f53\u4e5f\u3002\n\u3000\u3000\u4e5d\u56db\uff1a\u9707\u9042\u6ce5\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u9707\u9042\u6ce5\uff0c\u672a\u5149\u4e5f\u3002\n\u3000\u3000\u516d\u4e94\uff1a\u9707\u5f80\u6765\u5389\uff0c\u4ebf\u65e0\u4e27\uff0c\u6709\u4e8b\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u9707\u5f80\u6765\u5389\uff0c\u5371\u884c\u4e5f\u3002 \u5176\u4e8b\u5728\u4e2d\uff0c\u5927\u65e0\u4e27\u4e5f\u3002\n\u3000\u3000\u4e0a\u516d\uff1a\u9707\u7d22\u7d22\uff0c\u89c6\u77cd\u77cd\uff0c\u5f81\u51f6\u3002 \u9707\u4e0d\u4e8e\u5176\u8eac\uff0c\u4e8e\u5176\u90bb\uff0c\u65e0\u548e\u3002 \u5a5a\u5abe\u6709\u8a00\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u9707\u7d22\u7d22\uff0c\u672a\u5f97\u4e2d\u4e5f\u3002 \u867d\u51f6\u65e0\u548e\uff0c\u754f\u90bb\u6212\u4e5f\u3002\n \u300a\u6613\u7ecf\u300b\u7b2c\u4e94\u5341\u4e8c\u5366 \u826e \u826e\u4e3a\u5c71 \u826e\u4e0a\u826e\u4e0b [/COLOR]\n\u3000\u3000\u826e\uff1a\u826e\u5176\u80cc\uff0c\u4e0d\u83b7\u5176\u8eab\uff0c\u884c\u5176\u5ead\uff0c\u4e0d\u89c1\u5176\u4eba\uff0c\u65e0\u548e\u3002\n\u5f56\u66f0\uff1a\u826e\uff0c\u6b62\u4e5f\u3002 \u65f6\u6b62\u5219\u6b62\uff0c\u65f6\u884c\u5219\u884c\uff0c\u52a8\u9759\u4e0d\u5931\u5176\u65f6\uff0c\u5176\u9053\u5149\u660e\u3002 \u826e\u5176\u6b62\uff0c\u6b62\u5176\u6240\u4e5f\u3002 \u4e0a\u4e0b\u654c\u5e94\uff0c\u4e0d\u76f8\u4e0e\u4e5f\u3002 \u662f\u4ee5\u4e0d\u83b7\u5176\u8eab\uff0c\u884c\u5176\u5ead\u4e0d\u89c1\u5176\u4eba\uff0c\u65e0\u548e\u4e5f\u3002\n\u8c61\u66f0\uff1a\u517c\u5c71\uff0c\u826e\uff1b\u541b\u5b50\u4ee5\u601d\u4e0d\u51fa\u5176\u4f4d\u3002\n",
        "\n\u3000\u3000\u521d\u516d\uff1a\u826e\u5176\u8dbe\uff0c\u65e0\u548e\uff0c\u5229\u6c38\u8d1e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u826e\u5176\u8dbe\uff0c\u672a\u5931\u6b63\u4e5f\u3002\n\u3000\u3000\u516d\u4e8c\uff1a\u826e\u5176\u8153\uff0c\u4e0d\u62ef\u5176\u968f\uff0c\u5176\u5fc3\u4e0d\u5feb\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u4e0d\u62ef\u5176\u968f\uff0c\u672a\u9000\u542c\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e09\uff1a\u826e\u5176\u9650\uff0c\u5217\u5176\u5924\uff0c\u5389\u85b0\u5fc3\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u826e\u5176\u9650\uff0c\u5371\u85b0\u5fc3\u4e5f\u3002\n\u3000\u3000\u516d\u56db\uff1a\u826e\u5176\u8eab\uff0c\u65e0\u548e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u826e\u5176\u8eab\uff0c\u6b62\u8bf8\u8eac\u4e5f\u3002\n\u3000\u3000\u516d\u4e94\uff1a\u826e\u5176\u8f85\uff0c\u8a00\u6709\u5e8f\uff0c\u6094\u4ea1\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u826e\u5176\u8f85\uff0c\u4ee5\u4e2d\u6b63\u4e5f\u3002\n\u3000\u3000\u4e0a\u4e5d\uff1a\u6566\u826e\uff0c\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u6566\u826e\u4e4b\u5409\uff0c\u4ee5\u539a\u7ec8\u4e5f\u3002\n"
    ],
    [
        "\n\u300a\u6613\u7ecf\u300b\u7b2c\u4e94\u5341\u4e09\u5366 \u6e10 \u98ce\u5c71\u6e10 \u5dfd\u4e0a\u826e\u4e0b \n\u3000\u3000\u6e10\uff1a\u5973\u5f52\u5409\uff0c\u5229\u8d1e\u3002\n\u5f56\u66f0\uff1a\u6e10\u4e4b\u8fdb\u4e5f\uff0c\u5973\u5f52\u5409\u4e5f\u3002 \u8fdb\u5f97\u4f4d\uff0c\u5f80\u6709\u529f\u4e5f\u3002\u8fdb\u4ee5\u6b63\uff0c\u53ef\u4ee5\u6b63\u90a6\u4e5f\u3002\u5176\u4f4d\u521a\uff0c\u5f97\u4e2d\u4e5f\u3002 \u6b62\u800c\u5dfd\uff0c\u52a8\u4e0d\u7a77\u4e5f\u3002\n\u8c61\u66f0\uff1a\u5c71\u4e0a\u6709\u6728\uff0c\u6e10\uff1b\u541b\u5b50\u4ee5\u5c45\u8d24\u5fb7\uff0c\u5584\u4fd7\u3002\n",
        "\n\u3000\u3000\u521d\u516d\uff1a\u9e3f\u6e10\u4e8e\u5e72\uff0c\u5c0f\u5b50\u5389\uff0c\u6709\u8a00\uff0c\u65e0\u548e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5c0f\u5b50\u4e4b\u5389\uff0c\u4e49\u65e0\u548e\u4e5f\u3002\n\u3000\u3000\u516d\u4e8c\uff1a\u9e3f\u6e10\u4e8e\u78d0\uff0c\u996e\u98df\u884e\u884e\uff0c\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u996e\u98df\u884e\u884e\uff0c\u4e0d\u7d20\u9971\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e09\uff1a\u9e3f\u6e10\u4e8e\u9646\uff0c\u592b\u5f81\u4e0d\u590d\uff0c\u5987\u5b55\u4e0d\u80b2\uff0c\u51f6\uff1b\u5229\u5fa1\u5bc7\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u592b\u5f81\u4e0d\u590d\uff0c\u79bb\u7fa4\u4e11\u4e5f\u3002 \u5987\u5b55\u4e0d\u80b2\uff0c\u5931\u5176\u9053\u4e5f\u3002\u5229\u7528\u5fa1\u5bc7\uff0c\u987a\u76f8\u4fdd\u4e5f\u3002\n\u3000\u3000\u516d\u56db\uff1a\u9e3f\u6e10\u4e8e\u6728\uff0c\u6216\u5f97\u5176\u6877\uff0c\u65e0\u548e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u6216\u5f97\u5176\u6877\uff0c\u987a\u4ee5\u5dfd\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e94\uff1a\u9e3f\u6e10\u4e8e\u9675\uff0c\u5987\u4e09\u5c81\u4e0d\u5b55\uff0c\u7ec8\u83ab\u4e4b\u80dc\uff0c\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u7ec8\u83ab\u4e4b\u80dc\uff0c\u5409\uff1b\u5f97\u6240\u613f\u4e5f\u3002\n\u3000\u3000\u4e0a\u4e5d\uff1a\u9e3f\u6e10\u4e8e\u9035\uff0c\u5176\u7fbd\u53ef\u7528\u4e3a\u4eea\uff0c\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5176\u7fbd\u53ef\u7528\u4e3a\u4eea\uff0c\u5409\uff1b\u4e0d\u53ef\u4e71\u4e5f\u3002\n"
    ],
    [
        "\n\u300a\u6613\u7ecf\u300b\u7b2c\u4e94\u5341\u56db\u5366 \u5f52\u59b9 \u96f7\u6cfd\u5f52\u59b9 \u9707\u4e0a\u5151\u4e0b \u3000\u3000\n\u5f52\u59b9\uff1a\u5f81\u51f6\uff0c\u65e0\u6538\u5229\u3002\n\u5f56\u66f0\uff1a\u5f52\u59b9\uff0c\u5929\u5730\u4e4b\u5927\u4e49\u4e5f\u3002\u5929\u5730\u4e0d\u4ea4\uff0c\u800c\u4e07\u7269\u4e0d\u5174\uff0c\u5f52\u59b9\u4eba\u4e4b\u7ec8\u59cb\u4e5f\u3002\u8bf4\u4ee5\u52a8\uff0c\u6240\u5f52\u59b9\u4e5f\u3002 \u5f81\u51f6\uff0c\u4f4d\u4e0d\u5f53\u4e5f\u3002 \u65e0\u6538\u5229\uff0c\u67d4\u4e58\u521a\u4e5f\u3002\n\u8c61\u66f0\uff1a\u6cfd\u4e0a\u6709\u96f7\uff0c\u5f52\u59b9\uff1b\u541b\u5b50\u4ee5\u6c38\u7ec8\u77e5\u655d\u3002\n",
        "\n\u3000\u3000\u521d\u4e5d\uff1a\u5f52\u59b9\u4ee5\u5a23\uff0c\u8ddb\u80fd\u5c65\uff0c\u5f81\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5f52\u59b9\u4ee5\u5a23\uff0c\u4ee5\u6052\u4e5f\u3002 \u8ddb\u80fd\u5c65\u5409\uff0c\u76f8\u627f\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e8c\uff1a\u7707\u80fd\u89c6\uff0c\u5229\u5e7d\u4eba\u4e4b\u8d1e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5229\u5e7d\u4eba\u4e4b\u8d1e\uff0c\u672a\u53d8\u5e38\u4e5f\u3002\n\u3000\u3000\u516d\u4e09\uff1a\u5f52\u59b9\u4ee5\u987b\uff0c\u53cd\u5f52\u4ee5\u5a23\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5f52\u59b9\u4ee5\u987b\uff0c\u672a\u5f53\u4e5f\u3002\n\u3000\u3000\u4e5d\u56db\uff1a\u5f52\u59b9\u6106\u671f\uff0c\u8fdf\u5f52\u6709\u65f6\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u6106\u671f\u4e4b\u5fd7\uff0c\u6709\u5f85\u800c\u884c\u4e5f\u3002\n\u3000\u3000\u516d\u4e94\uff1a\u5e1d\u4e59\u5f52\u59b9\uff0c\u5176\u541b\u4e4b\u8882\uff0c\u4e0d\u5982\u5176\u5a23\u4e4b\u8882\u826f\uff0c\u6708\u51e0\u671b\uff0c\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5e1d\u4e59\u5f52\u59b9\uff0c\u4e0d\u5982\u5176\u5a23\u4e4b\u8882\u826f\u4e5f\u3002 \u5176\u4f4d\u5728\u4e2d\uff0c\u4ee5\u8d35\u884c\u4e5f\u3002\n\u3000\u3000\u4e0a\u516d\uff1a\u5973\u627f\u7b50\u65e0\u5b9e\uff0c\u58eb\u5232\u7f8a\u65e0\u8840\uff0c\u65e0\u6538\u5229\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u4e0a\u516d\u65e0\u5b9e\uff0c\u627f\u865a\u7b50\u4e5f\u3002\n"
    ],
    [
        "\n\u300a\u6613\u7ecf\u300b\u7b2c\u4e94\u5341\u4e94\u5366 \u4e30 \u96f7\u706b\u4e30 \u9707\u4e0a\u79bb\u4e0b \n\u3000\u3000\u4e30\uff1a\u4ea8\uff0c\u738b\u5047\u4e4b\uff0c\u52ff\u5fe7\uff0c\u5b9c\u65e5\u4e2d\u3002\n\u5f56\u66f0\uff1a\u4e30\uff0c\u5927\u4e5f\u3002 \u660e\u4ee5\u52a8\uff0c\u6545\u4e30\u3002\u738b\u5047\u4e4b\uff0c\u5c1a\u5927\u4e5f\u3002 \u52ff\u5fe7\u5b9c\u65e5\u4e2d\uff0c\u5b9c\u7167\u5929\u4e0b\u4e5f\u3002\u65e5\u4e2d\u5219\u6603\uff0c\u6708\u76c8\u5219\u98df\uff0c\u5929\u5730\u76c8\u865a\uff0c\u4e0e\u65f6\u6d88\u606f\uff0c\u800c\u51b5\u4eba\u65bc\u4eba\u4e4e\uff1f\u51b5\u65bc\u9b3c\u795e\u4e4e\uff1f\n\u8c61\u66f0\uff1a\u96f7\u7535\u7686\u81f3\uff0c\u4e30\uff1b\u541b\u5b50\u4ee5\u6298\u72f1\u81f4\u5211\u3002\n",
        "\n\u3000\u3000\u521d\u4e5d\uff1a\u9047\u5176\u914d\u4e3b\uff0c\u867d\u65ec\u65e0\u548e\uff0c\u5f80\u6709\u5c1a\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u867d\u65ec\u65e0\u548e\uff0c\u8fc7\u65ec\u707e\u4e5f\u3002\n\u3000\u3000\u516d\u4e8c\uff1a\u4e30\u5176\u8500\uff0c\u65e5\u4e2d\u89c1\u6597\uff0c\u5f80\u5f97\u7591\u75be\uff0c\u6709\u5b5a\u53d1\u82e5\uff0c\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u6709\u5b5a\u53d1\u82e5\uff0c\u4fe1\u4ee5\u53d1\u5fd7\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e09\uff1a\u4e30\u5176\u6c9b\uff0c\u65e5\u4e2d\u89c1\u6627\uff0c\u6298\u5176\u53f3\u80b1\uff0c\u65e0\u548e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u4e30\u5176\u6c9b\uff0c\u4e0d\u53ef\u5927\u4e8b\u4e5f\u3002 \u6298\u5176\u53f3\u80b1\uff0c\u7ec8\u4e0d\u53ef\u7528\u4e5f\u3002\n\u3000\u3000\u4e5d\u56db\uff1a\u4e30\u5176\u8500\uff0c\u65e5\u4e2d\u89c1\u6597\uff0c\u9047\u5176\u5937\u4e3b\uff0c\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u4e30\u5176\u8500\uff0c\u4f4d\u4e0d\u5f53\u4e5f\u3002 \u65e5\u4e2d\u89c1\u6597\uff0c\u5e7d\u4e0d\u660e\u4e5f\u3002 \u9047\u5176\u5937\u4e3b\uff0c\u5409\uff1b\u884c\u4e5f\u3002\n\u3000\u3000\u516d\u4e94\uff1a\u6765\u7ae0\uff0c\u6709\u5e86\u8a89\uff0c\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u516d\u4e94\u4e4b\u5409\uff0c\u6709\u5e86\u4e5f\u3002\n\u3000\u3000\u4e0a\u516d\uff1a\u4e30\u5176\u5c4b\uff0c\u8500\u5176\u5bb6\uff0c\u7aa5\u5176\u6237\uff0c\u95c3\u5176\u65e0\u4eba\uff0c\u4e09\u5c81\u4e0d\u89c1\uff0c\u51f6\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u4e30\u5176\u5c4b\uff0c\u5929\u9645\u7fd4\u4e5f\u3002 \u7aa5\u5176\u6237\uff0c\u95c3\u5176\u65e0\u4eba\uff0c\u81ea\u85cf\u4e5f\u3002\n"
    ],
    [
        "\n\u300a\u6613\u7ecf\u300b\u7b2c\u4e94\u5341\u516d\u5366 \u65c5 \u706b\u5c71\u65c5 \u79bb\u4e0a\u826e\u4e0b \n\u3000\u3000\u65c5\uff1a\u5c0f\u4ea8\uff0c\u65c5\u8d1e\u5409\u3002\n\u5f56\u66f0\uff1a\u65c5\uff0c\u5c0f\u4ea8\uff0c\u67d4\u5f97\u4e2d\u4e4e\u5916\uff0c\u800c\u987a\u4e4e\u521a\uff0c\u6b62\u800c\u4e3d\u4e4e\u660e\uff0c\u662f\u4ee5\u5c0f\u4ea8\uff0c\u65c5\u8d1e\u5409\u4e5f\u3002 \u65c5\u4e4b\u65f6\u4e49\u5927\u77e3\u54c9\uff01\n\u8c61\u66f0\uff1a\u5c71\u4e0a\u6709\u706b\uff0c\u65c5\uff1b\u541b\u5b50\u4ee5\u660e\u614e\u7528\u5211\uff0c\u800c\u4e0d\u7559\u72f1\u3002\n",
        "\n\u3000\u3000\u521d\u516d\uff1a\u65c5\u7410\u7410\uff0c\u65af\u5176\u6240\u53d6\u707e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u65c5\u7410\u7410\uff0c\u5fd7\u7a77\u707e\u4e5f\u3002\n\u3000\u3000\u516d\u4e8c\uff1a\u65c5\u5373\u6b21\uff0c\u6000\u5176\u8d44\uff0c\u5f97\u7ae5\u4ec6\u8d1e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5f97\u7ae5\u4ec6\u8d1e\uff0c\u7ec8\u65e0\u5c24\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e09\uff1a\u65c5\u711a\u5176\u6b21\uff0c\u4e27\u5176\u7ae5\u4ec6\uff0c\u8d1e\u5389\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u65c5\u711a\u5176\u6b21\uff0c\u4ea6\u4ee5\u4f24\u77e3\u3002 \u4ee5\u65c5\u4e0e\u4e0b\uff0c\u5176\u4e49\u4e27\u4e5f\u3002\n\u3000\u3000\u4e5d\u56db\uff1a\u65c5\u4e8e\u5904\uff0c\u5f97\u5176\u8d44\u65a7\uff0c\u6211\u5fc3\u4e0d\u5feb\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u65c5\u4e8e\u5904\uff0c\u672a\u5f97\u4f4d\u4e5f\u3002 \u5f97\u5176\u8d44\u65a7\uff0c\u5fc3\u672a\u5feb\u4e5f\u3002\n\u3000\u3000\u516d\u4e94\uff1a\u5c04\u96c9\u4e00\u77e2\u4ea1\uff0c\u7ec8\u4ee5\u8a89\u547d\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u7ec8\u4ee5\u8a89\u547d\uff0c\u4e0a\u902e\u4e5f\u3002\n\u3000\u3000\u4e0a\u4e5d\uff1a\u9e1f\u711a\u5176\u5de2\uff0c\u65c5\u4eba\u5148\u7b11\u540e\u53f7\u5555\u3002 \u4e27\u725b\u4e8e\u6613\uff0c\u51f6\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u4ee5\u65c5\u5728\u4e0a\uff0c\u5176\u4e49\u711a\u4e5f\u3002 \u4e27\u725b\u4e8e\u6613\uff0c\u7ec8\u83ab\u4e4b\u95fb\u4e5f\u3002\n"
    ],
    [
        "\n\u300a\u6613\u7ecf\u300b\u7b2c\u4e94\u5341\u4e03\u5366 \u5dfd \u5dfd\u4e3a\u98ce \u5dfd\u4e0a\u5dfd\u4e0b \n\u3000\u3000\u5dfd\uff1a\u5c0f\u4ea8\uff0c\u5229\u6538\u5f80\uff0c\u5229\u89c1\u5927\u4eba\u3002\n\u5f56\u66f0\uff1a\u91cd\u5dfd\u4ee5\u7533\u547d\uff0c\u521a\u5dfd\u4e4e\u4e2d\u6b63\u800c\u5fd7\u884c\u3002\u67d4\u7686\u987a\u4e4e\u521a\uff0c\u662f\u4ee5\u5c0f\u4ea8\uff0c\u5229\u6709\u6538\u5f80\uff0c\u5229\u89c1\u5927\u4eba\u3002\n\u8c61\u66f0\uff1a\u968f\u98ce\uff0c\u5dfd\uff1b\u541b\u5b50\u4ee5\u7533\u547d\u884c\u4e8b\u3002sm.aa963.com\n",
        "\n\u3000\u3000\u521d\u516d\uff1a\u8fdb\u9000\uff0c\u5229\u6b66\u4eba\u4e4b\u8d1e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u8fdb\u9000\uff0c\u5fd7\u7591\u4e5f\u3002 \u5229\u6b66\u4eba\u4e4b\u8d1e\uff0c\u5fd7\u6cbb\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e8c\uff1a\u5dfd\u5728\u25a1\u4e0b\uff0c\u7528\u53f2\u5deb\u7eb7\u82e5\uff0c\u5409\u65e0\u548e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u7eb7\u82e5\u4e4b\u5409\uff0c\u5f97\u4e2d\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e09\uff1a\u9891\u5dfd\uff0c\u541d\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u9891\u5dfd\u4e4b\u541d\uff0c\u5fd7\u7a77\u4e5f\u3002\n\u3000\u3000\u516d\u56db\uff1a\u6094\u4ea1\uff0c\u7530\u83b7\u4e09\u54c1\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u7530\u83b7\u4e09\u54c1\uff0c\u6709\u529f\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e94\uff1a\u8d1e\u5409\u6094\u4ea1\uff0c\u65e0\u4e0d\u5229\u3002 \u65e0\u521d\u6709\u7ec8\uff0c\u5148\u5e9a\u4e09\u65e5\uff0c\u540e\u5e9a\u4e09\u65e5\uff0c\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u4e5d\u4e94\u4e4b\u5409\uff0c\u4f4d\u6b63\u4e2d\u4e5f\u3002\n\u3000\u3000\u4e0a\u4e5d\uff1a\u5dfd\u5728\u7240\u4e0b\uff0c\u4e27\u5176\u8d44\u65a7\uff0c\u8d1e\u51f6\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5dfd\u5728\u7240\u4e0b\uff0c\u4e0a\u7a77\u4e5f\u3002 \u4e27\u5176\u8d44\u65a7\uff0c\u6b63\u4e4e\u51f6\u4e5f\u3002\n"
    ],
    [
        "\n\u300a\u6613\u7ecf\u300b\u7b2c\u4e94\u5341\u516b\u5366 \u5151 \u5151\u4e3a\u6cfd \u5151\u4e0a\u5151\u4e0b \n\u3000\u3000\u5151\uff1a\u4ea8\uff0c\u5229\u8d1e\u3002\n\u5f56\u66f0\uff1a\u5151\uff0c\u8bf4\u4e5f\u3002 \u521a\u4e2d\u800c\u67d4\u5916\uff0c\u8bf4\u4ee5\u5229\u8d1e\uff0c\u662f\u4ee5\u987a\u4e4e\u5929\uff0c\u800c\u5e94\u4e4e\u4eba\u3002 \u8bf4\u4ee5\u5148\u6c11\uff0c\u6c11\u5fd8\u5176\u52b3\uff1b\u8bf4\u4ee5\u72af\u96be\uff0c\u6c11\u5fd8\u5176\u6b7b\uff1b\u8bf4\u4e4b\u5927\uff0c\u6c11\u529d\u77e3\u54c9\uff01\n\u8c61\u66f0\uff1a\u4e3d\u6cfd\uff0c\u5151\uff1b\u541b\u5b50\u4ee5\u670b\u53cb\u8bb2\u4e60\u3002\n",
        "\n\u3000\u3000\u521d\u4e5d\uff1a\u548c\u5151\uff0c\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u548c\u5151\u4e4b\u5409\uff0c\u884c\u672a\u7591\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e8c\uff1a\u5b5a\u5151\uff0c\u5409\uff0c\u6094\u4ea1\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5b5a\u5151\u4e4b\u5409\uff0c\u4fe1\u5fd7\u4e5f\u3002\n\u3000\u3000\u516d\u4e09\uff1a\u6765\u5151\uff0c\u51f6\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u6765\u5151\u4e4b\u51f6\uff0c\u4f4d\u4e0d\u5f53\u4e5f\u3002\n\u3000\u3000\u4e5d\u56db\uff1a\u5546\u5151\uff0c\u672a\u5b81\uff0c\u4ecb\u75be\u6709\u559c\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u4e5d\u56db\u4e4b\u559c\uff0c\u6709\u5e86\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e94\uff1a\u5b5a\u4e8e\u5265\uff0c\u6709\u5389\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5b5a\u4e8e\u5265\uff0c\u4f4d\u6b63\u5f53\u4e5f\u3002\n\u3000\u3000\u4e0a\u516d\uff1a\u5f15\u5151\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u4e0a\u516d\u5f15\u5151\uff0c\u672a\u5149\u4e5f\u3002\n"
    ],
    [
        "\n\u300a\u6613\u7ecf\u300b\u7b2c\u4e94\u5341\u4e5d\u5366 \u6da3 \u98ce\u6c34\u6da3 \u5dfd\u4e0a\u574e\u4e0b \n\u3000\u3000\u6da3\uff1a\u4ea8\u3002 \u738b\u5047\u6709\u5e99\uff0c\u5229\u6d89\u5927\u5ddd\uff0c\u5229\u8d1e\u3002\n\u5f56\u66f0\uff1a\u6da3\uff0c\u4ea8\u3002 \u521a\u6765\u800c\u4e0d\u7a77\uff0c\u67d4\u5f97\u4f4d\u4e4e\u5916\u800c\u4e0a\u540c\u3002 \u738b\u5047\u6709\u5e99\uff0c\u738b\u4e43\u5728\u4e2d\u4e5f\u3002 \u5229\u6d89\u5927\u5ddd\uff0c\u4e58\u6728\u6709\u529f\u4e5f\u3002\n\u8c61\u66f0\uff1a\u98ce\u884c\u6c34\u4e0a\uff0c\u6da3\uff1b\u5148\u738b\u4ee5\u4eab\u4e8e\u5e1d\u7acb\u5e99\u3002\n",
        "\n\u3000\u3000\u521d\u516d\uff1a\u7528\u62ef\u9a6c\u58ee\uff0c\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u521d\u516d\u4e4b\u5409\uff0c\u987a\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e8c\uff1a\u6da3\u5954\u5176\u673a\uff0c\u6094\u4ea1\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u6da3\u5954\u5176\u673a\uff0c\u5f97\u613f\u4e5f\u3002\n\u3000\u3000\u516d\u4e09\uff1a\u6da3\u5176\u8eac\uff0c\u65e0\u6094\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u6da3\u5176\u8eac\uff0c\u5fd7\u5728\u5916\u4e5f\u3002\n\u3000\u3000\u516d\u56db\uff1a\u6da3\u5176\u7fa4\uff0c\u5143\u5409\u3002 \u6da3\u6709\u4e18\uff0c\u532a\u5937\u6240\u601d\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u6da3\u5176\u7fa4\uff0c\u5143\u5409\uff1b\u5149\u5927\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e94\uff1a\u6da3\u6c57\u5176\u5927\u53f7\uff0c\u6da3\u738b\u5c45\uff0c\u65e0\u548e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u738b\u5c45\u65e0\u548e\uff0c\u6b63\u4f4d\u4e5f\u3002\n\u3000\u3000\u4e0a\u4e5d\uff1a\u6da3\u5176\u8840\uff0c\u53bb\u9016\u51fa\uff0c\u65e0\u548e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u6da3\u5176\u8840\uff0c\u8fdc\u5bb3\u4e5f\u3002\n"
    ],
    [
        "\n\u300a\u6613\u7ecf\u300b\u7b2c\u516d\u5341\u5366 \u8282 \u6c34\u6cfd\u8282 \u574e\u4e0a\u5151\u4e0b \n\u3000\u3000\u8282\uff1a\u4ea8\u3002 \u82e6\u8282\u4e0d\u53ef\u8d1e\u3002\n\u5f56\u66f0\uff1a\u8282\uff0c\u4ea8\uff0c\u521a\u67d4\u5206\uff0c\u800c\u521a\u5f97\u4e2d\u3002\u82e6\u8282\u4e0d\u53ef\u8d1e\uff0c\u5176\u9053\u7a77\u4e5f\u3002\u8bf4\u4ee5\u884c\u9669\uff0c\u5f53\u4f4d\u4ee5\u8282\uff0c\u4e2d\u6b63\u4ee5\u901a\u3002 \u5929\u5730\u8282\u800c\u56db\u65f6\u6210\uff0c\u8282\u4ee5\u5236\u5ea6\uff0c\u4e0d\u4f24\u8d22\uff0c\u4e0d\u5bb3\u6c11\u3002\n\u8c61\u66f0\uff1a\u6cfd\u4e0a\u6709\u6c34\uff0c\u8282\uff1b\u541b\u5b50\u4ee5\u5236\u6570\u5ea6\uff0c\u8bae\u5fb7\u884c\u3002\n",
        "\n\u3000\u3000\u521d\u4e5d\uff1a\u4e0d\u51fa\u6237\u5ead\uff0c\u65e0\u548e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u4e0d\u51fa\u6237\u5ead\uff0c\u77e5\u901a\u585e\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e8c\uff1a\u4e0d\u51fa\u95e8\u5ead\uff0c\u51f6\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u4e0d\u51fa\u95e8\u5ead\uff0c\u5931\u65f6\u6781\u4e5f\u3002\n\u3000\u3000\u516d\u4e09\uff1a\u4e0d\u8282\u82e5\uff0c\u5219\u55df\u82e5\uff0c\u65e0\u548e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u4e0d\u8282\u4e4b\u55df\uff0c\u53c8\u8c01\u548e\u4e5f\u3002\n\u3000\u3000\u516d\u56db\uff1a\u5b89\u8282\uff0c\u4ea8\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5b89\u8282\u4e4b\u4ea8\uff0c\u627f\u4e0a\u9053\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e94\uff1a\u7518\u8282\uff0c\u5409\uff1b\u5f80\u6709\u5c1a\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u7518\u8282\u4e4b\u5409\uff0c\u5c45\u4f4d\u4e2d\u4e5f\u3002\n\u3000\u3000\u4e0a\u516d\uff1a\u82e6\u8282\uff0c\u8d1e\u51f6\uff0c\u6094\u4ea1\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u82e6\u8282\u8d1e\u51f6\uff0c\u5176\u9053\u7a77\u4e5f\u3002\n"
    ],
    [
        "\n\u300a\u6613\u7ecf\u300b\u7b2c\u516d\u5341\u4e00\u5366 \u4e2d\u5b5a \u98ce\u6cfd\u4e2d\u5b5a \u5dfd\u4e0a\u5151\u4e0b \n\u3000\u3000\u4e2d\u5b5a\uff1a\u8c5a\u9c7c\u5409\uff0c\u5229\u6d89\u5927\u5ddd\uff0c\u5229\u8d1e\u3002\n\u5f56\u66f0\uff1a\u4e2d\u5b5a\uff0c\u67d4\u5728\u5185\u800c\u521a\u5f97\u4e2d\u3002 \u8bf4\u800c\u5dfd\uff0c\u5b5a\uff0c\u4e43\u5316\u90a6\u4e5f\u3002 \u8c5a\u9c7c\u5409\uff0c\u4fe1\u53ca\u8c5a\u9c7c\u4e5f\u3002 \u5229\u6d89\u5927\u5ddd\uff0c\u4e58\u6728\u821f\u865a\u4e5f\u3002 \u4e2d\u5b5a\u4ee5\u5229\u8d1e\uff0c\u4e43\u5e94\u4e4e\u5929\u4e5f\u3002\n\u8c61\u66f0\uff1a\u6cfd\u4e0a\u6709\u98ce\uff0c\u4e2d\u5b5a\uff1b\u541b\u5b50\u4ee5\u8bae\u72f1\u7f13\u6b7b\u3002\n",
        "\n\u3000\u3000\u521d\u4e5d\uff1a\u865e\u5409\uff0c\u6709\u4ed6\u4e0d\u71d5\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u521d\u4e5d\u865e\u5409\uff0c\u5fd7\u672a\u53d8\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e8c\uff1a\u9e23\u9e64\u5728\u9634\uff0c\u5176\u5b50\u548c\u4e4b\uff0c\u6211\u6709\u597d\u7235\uff0c\u543e\u4e0e\u5c14\u9761\u4e4b\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5176\u5b50\u548c\u4e4b\uff0c\u4e2d\u5fc3\u613f\u4e5f\u3002\n\u3000\u3000\u516d\u4e09\uff1a\u5f97\u654c\uff0c\u6216\u9f13\u6216\u7f62\uff0c\u6216\u6ce3\u6216\u6b4c\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u53ef\u9f13\u6216\u7f62\uff0c\u4f4d\u4e0d\u5f53\u4e5f\u3002\n\u3000\u3000\u516d\u56db\uff1a\u6708\u51e0\u671b\uff0c\u9a6c\u5339\u4ea1\uff0c\u65e0\u548e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u9a6c\u5339\u4ea1\uff0c\u7edd\u7c7b\u4e0a\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e94\uff1a\u6709\u5b5a\u631b\u5982\uff0c\u65e0\u548e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u6709\u5b5a\u631b\u5982\uff0c\u4f4d\u6b63\u5f53\u4e5f\u3002\n\u3000\u3000\u4e0a\u4e5d\uff1a\u7ff0\u97f3\u767b\u4e8e\u5929\uff0c\u8d1e\u51f6\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u7ff0\u97f3\u767b\u4e8e\u5929\uff0c\u4f55\u53ef\u957f\u4e5f\u3002\n"
    ],
    [
        "\n\u300a\u6613\u7ecf\u300b\u7b2c\u516d\u5341\u4e8c\u5366 \u5c0f\u8fc7 \u96f7\u5c71\u5c0f\u8fc7 \u9707\u4e0a\u826e\u4e0b \n\u3000\u3000\u5c0f\u8fc7\uff1a\u4ea8\uff0c\u5229\u8d1e\uff0c\u53ef\u5c0f\u4e8b\uff0c\u4e0d\u53ef\u5927\u4e8b\u3002\u98de\u9e1f\u9057\u4e4b\u97f3\uff0c\u4e0d\u5b9c\u4e0a\u5b9c\u4e0b\uff0c\u5927\u5409\u3002\n\u5f56\u66f0\uff1a\u5c0f\u8fc7\uff0c\u5c0f\u8005\u8fc7\u800c\u4ea8\u4e5f\u3002 \u8fc7\u4ee5\u5229\u8d1e\uff0c\u4e0e\u65f6\u884c\u4e5f\u3002 \u67d4\u5f97\u4e2d\uff0c\u662f\u4ee5\u5c0f\u4e8b\u5409\u4e5f\u3002 \u521a\u5931\u4f4d\u800c\u4e0d\u4e2d\uff0c\u662f\u4ee5\u4e0d\u53ef\u5927\u4e8b\u4e5f\u3002 \u6709\u98de\u9e1f\u4e4b\u8c61\u7109\uff0c\u6709\u98de\u9e1f\u9057\u4e4b\u97f3\uff0c\u4e0d\u5b9c\u4e0a\u5b9c\u4e0b\uff0c\u5927\u5409\uff1b\u4e0a\u9006\u800c\u4e0b\u987a\u4e5f\u3002\n\u8c61\u66f0\uff1a\u5c71\u4e0a\u6709\u96f7\uff0c\u5c0f\u8fc7\uff1b\u541b\u5b50\u4ee5\u884c\u8fc7\u4e4e\u606d\uff0c\u4e27\u8fc7\u4e4e\u54c0\uff0c\u7528\u8fc7\u4e4e\u4fed\u3002\n",
        "\n\u3000\u3000\u521d\u516d\uff1a\u98de\u9e1f\u4ee5\u51f6\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u98de\u9e1f\u4ee5\u51f6\uff0c\u4e0d\u53ef\u5982\u4f55\u4e5f\u3002\n\u3000\u3000\u516d\u4e8c\uff1a\u8fc7\u5176\u7956\uff0c\u9047\u5176\u59a3\uff1b\u4e0d\u53ca\u5176\u541b\uff0c\u9047\u5176\u81e3\uff1b\u65e0\u548e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u4e0d\u53ca\u5176\u541b\uff0c\u81e3\u4e0d\u53ef\u8fc7\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e09\uff1a\u5f17\u8fc7\u9632\u4e4b\uff0c\u4ece\u6216\u6215\u4e4b\uff0c\u51f6\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u4ece\u6216\u6215\u4e4b\uff0c\u51f6\u5982\u4f55\u4e5f\u3002\n\u3000\u3000\u4e5d\u56db\uff1a\u65e0\u548e\uff0c\u5f17\u8fc7\u9047\u4e4b\u3002 \u5f80\u5389\u5fc5\u6212\uff0c\u52ff\u7528\u6c38\u8d1e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5f17\u8fc7\u9047\u4e4b\uff0c\u4f4d\u4e0d\u5f53\u4e5f\u3002 \u5f80\u5389\u5fc5\u6212\uff0c\u7ec8\u4e0d\u53ef\u957f\u4e5f\u3002\n\u3000\u3000\u516d\u4e94\uff1a\u5bc6\u4e91\u4e0d\u96e8\uff0c\u81ea\u6211\u897f\u90ca\uff0c\u516c\u5f0b\u53d6\u5f7c\u5728\u7a74\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5bc6\u4e91\u4e0d\u96e8\uff0c\u5df2\u4e0a\u4e5f\u3002\n\u3000\u3000\u4e0a\u516d\uff1a\u5f17\u9047\u8fc7\u4e4b\uff0c\u98de\u9e1f\u79bb\u4e4b\uff0c\u51f6\uff0c\u662f\u8c13\u707e\u771a\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u5f17\u9047\u8fc7\u4e4b\uff0c\u5df2\u4ea2\u4e5f\u3002\n"
    ],
    [
        "\n\u300a\u6613\u7ecf\u300b\u7b2c\u516d\u5341\u4e09\u5366 \u65e2\u6d4e \u6c34\u706b\u65e2\u6d4e \u574e\u4e0a\u79bb\u4e0b \n\u3000\u3000\u65e2\u6d4e\uff1a\u4ea8\uff0c\u5c0f\u5229\u8d1e\uff0c\u521d\u5409\u7ec8\u4e71\u3002\n\u5f56\u66f0\uff1a\u65e2\u6d4e\uff0c\u4ea8\uff0c\u5c0f\u8005\u4ea8\u4e5f\u3002\u5229\u8d1e\uff0c\u521a\u67d4\u6b63\u800c\u4f4d\u5f53\u4e5f\u3002 \u521d\u5409\uff0c\u67d4\u5f97\u4e2d\u4e5f\u3002\u7ec8\u6b62\u5219\u4e71\uff0c\u5176\u9053\u7a77\u4e5f\u3002\n\u8c61\u66f0\uff1a\u6c34\u5728\u706b\u4e0a\uff0c\u65e2\u6d4e\uff1b\u541b\u5b50\u4ee5\u601d\u60a3\u800c\u9884\u9632\u4e4b\u3002\n",
        "\n\u3000\u3000\u521d\u4e5d\uff1a\u66f3\u5176\u8f6e\uff0c\u6fe1\u5176\u5c3e\uff0c\u65e0\u548e\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u66f3\u5176\u8f6e\uff0c \u4e49\u65e0\u548e\u4e5f\u3002\n\u3000\u3000\u516d\u4e8c\uff1a\u5987\u4e27\u5176\u9af4\uff0c\u52ff\u9010\uff0c\u4e03\u65e5\u5f97\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u4e03\u65e5\u5f97\uff0c\u4ee5\u4e2d\u9053\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e09\uff1a\u9ad8\u5b97\u4f10\u9b3c\u65b9\uff0c\u4e09\u5e74\u514b\u4e4b\uff0c\u5c0f\u4eba\u52ff\u7528\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u4e09\u5e74\u514b\u4e4b\uff0c\u60eb\u4e5f\u3002\n\u3000\u3000\u516d\u56db\uff1a\u7e7b\u6709\u8863\u8864\u5982\uff0c\u7ec8\u65e5\u6212\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u7ec8\u65e5\u6212\uff0c\u6709\u6240\u7591\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e94\uff1a\u4e1c\u90bb\u6740\u725b\uff0c\u4e0d\u5982\u897f\u90bb\u4e4b\u79b4\u796d\uff0c\u5b9e\u53d7\u5176\u798f\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u4e1c\u90bb\u6740\u725b\uff0c\u4e0d\u5982\u897f\u90bb\u4e4b\u65f6\u4e5f\uff1b\u5b9e\u53d7\u5176\u798f\uff0c\u5409\u5927\u6765\u4e5f\u3002\n\u3000\u3000\u4e0a\u516d\uff1a\u6fe1\u5176\u9996\uff0c\u5389\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u6fe1\u5176\u9996\u5389\uff0c\u4f55\u53ef\u4e45\u4e5f\u3002\n"
    ],
    [
        "\n\u300a\u6613\u7ecf\u300b\u7b2c\u516d\u5341\u56db\u5366 \u672a\u6d4e \u706b\u6c34\u672a\u6d4e \u79bb\u4e0a\u574e\u4e0b \n\u3000\u3000\u672a\u6d4e\uff1a\u4ea8\uff0c\u5c0f\u72d0\u6c54\u6d4e\uff0c\u6fe1\u5176\u5c3e\uff0c\u65e0\u6538\u5229\u3002\n\u5f56\u66f0\uff1a\u672a\u6d4e\uff0c\u4ea8\uff1b\u67d4\u5f97\u4e2d\u4e5f\u3002 \u5c0f\u72d0\u6c54\u6d4e\uff0c\u672a\u51fa\u4e2d\u4e5f\u3002 \u6fe1\u5176\u5c3e\uff0c\u65e0\u6538\u5229\uff1b\u4e0d\u7eed\u7ec8\u4e5f\u3002 \u867d\u4e0d\u5f53\u4f4d\uff0c\u521a\u67d4\u5e94\u4e5f\u3002\n\u8c61\u66f0\uff1a\u706b\u5728\u6c34\u4e0a\uff0c\u672a\u6d4e\uff1b\u541b\u5b50\u4ee5\u614e\u8fa8\u7269\u5c45\u65b9\u3002\n",
        "\n\u3000\u3000\u521d\u516d\uff1a\u6fe1\u5176\u5c3e\uff0c\u541d\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u6fe1\u5176\u5c3e\uff0c\u4ea6\u4e0d\u77e5\u6781\u4e5f\u3002\n\u3000\u3000\u4e5d\u4e8c\uff1a\u66f3\u5176\u8f6e\uff0c\u8d1e\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u4e5d\u4e8c\u8d1e\u5409\uff0c\u4e2d\u4ee5\u884c\u6b63\u4e5f\u3002\n\u3000\u3000\u516d\u4e09\uff1a\u672a\u6d4e\uff0c\u5f81\u51f6\uff0c\u5229\u6d89\u5927\u5ddd\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u672a\u6d4e\u5f81\u51f6\uff0c\u4f4d\u4e0d\u5f53\u4e5f\u3002\n\u3000\u3000\u4e5d\u56db\uff1a\u8d1e\u5409\uff0c\u6094\u4ea1\uff0c\u9707\u7528\u4f10\u9b3c\u65b9\uff0c\u4e09\u5e74\u6709\u8d4f\u4e8e\u5927\u56fd\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u8d1e\u5409\u6094\u4ea1\uff0c\u5fd7\u884c\u4e5f\u3002\n\u3000\u3000\u516d\u4e94\uff1a\u8d1e\u5409\uff0c\u65e0\u6094\uff0c\u541b\u5b50\u4e4b\u5149\uff0c\u6709\u5b5a\uff0c\u5409\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u541b\u5b50\u4e4b\u5149\uff0c\u5176\u6656\u5409\u4e5f\u3002\n\u3000\u3000\u4e0a\u4e5d\uff1a\u6709\u5b5a\u4e8e\u996e\u9152\uff0c\u65e0\u548e\uff0c\u6fe1\u5176\u9996\uff0c\u6709\u5b5a\u5931\u662f\u3002\n\u3000\u3000\u8c61\u66f0\uff1a\u996e\u9152\u6fe1\u9996\uff0c\u4ea6\u4e0d\u77e5\u8282\u4e5f\u3002\n"
    ]
]



================================================
FILE: tests/data/guaci.txt
================================================
[Binary file]


================================================
FILE: tests/data/simple.tpl
================================================
男测：{{title}}

公历：{{solar.year}}年 {{solar.month}}月 {{solar.day}}日 {{solar.hour}}时 {{solar.minute}}分
干支：{{lunar.gz.year}}年 {{lunar.gz.month}}月 {{lunar.gz.day}}日 {{lunar.gz.hour}}时 （旬空：{{lunar.xkong}})

得「{{name}}」之「{{bian.name}}」卦

{{god6.5}}{{hide.qin6.5}}{{qin6.5}}{{qinx.5}} {{mark.5}} {{shiy.5}} {{dyao.5}} {{bian.qin6.5}} {{bian.mark.5}}
{{god6.4}}{{hide.qin6.4}}{{qin6.4}}{{qinx.4}} {{mark.4}} {{shiy.4}} {{dyao.4}} {{bian.qin6.4}} {{bian.mark.4}}
{{god6.3}}{{hide.qin6.3}}{{qin6.3}}{{qinx.3}} {{mark.3}} {{shiy.3}} {{dyao.3}} {{bian.qin6.3}} {{bian.mark.3}}
{{god6.2}}{{hide.qin6.2}}{{qin6.2}}{{qinx.2}} {{mark.2}} {{shiy.2}} {{dyao.2}} {{bian.qin6.2}} {{bian.mark.2}}
{{god6.1}}{{hide.qin6.1}}{{qin6.1}}{{qinx.1}} {{mark.1}} {{shiy.1}} {{dyao.1}} {{bian.qin6.1}} {{bian.mark.1}}
{{god6.0}}{{hide.qin6.0}}{{qin6.0}}{{qinx.0}} {{mark.0}} {{shiy.0}} {{dyao.0}} {{bian.qin6.0}} {{bian.mark.0}}


