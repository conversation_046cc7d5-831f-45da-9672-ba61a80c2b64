# 手动摇卦日期选择功能

## 功能概述

在手动摇卦排盘中新增了用户手动添加日期的功能。当用户启用此功能时，所有日期相关的计算（如天干地支、空亡等）都会使用用户提供的日期，而不是当前系统时间。

## 功能特点

### 1. 灵活的日期选择
- **日期选择器**：基于 react-day-picker 的日历组件，支持直观的日期选择
- **时辰选择**：提供24小时制的时辰选择（0-23时）
- **快速设置**：一键设置为当前时间
- **开关控制**：可以随时切换使用自定义日期或当前时间

### 2. 准确的干支计算
- 使用 lunar-javascript 库进行精确的干支计算
- 支持年干支、月干支、日干支、时干支的完整计算
- 自动处理节气、闰月等复杂情况
- 计算结果包括空亡信息

### 3. 用户友好的界面
- 清晰的开关标识，用户可以明确知道是否使用自定义日期
- 当未启用自定义日期时，显示"将使用当前时间计算干支"的提示
- 日期选择器采用中文格式显示（如：2024年01月01日 10时）

## 使用方法

### 1. 启用自定义日期
1. 在手动摇卦界面，找到"手动设置日期时间"开关
2. 点击开关启用自定义日期功能
3. 界面会显示日期时间选择器

### 2. 选择日期和时间
1. 点击日期选择器按钮
2. 在弹出的日历中选择所需日期
3. 在时辰下拉菜单中选择具体时辰（0-23时）
4. 可以点击"使用当前时间"快速设置为现在
5. 点击"确定"完成设置

### 3. 进行摇卦
1. 设置好日期后，按正常流程进行摇卦
2. 系统会使用您设置的日期计算所有相关信息
3. 生成的卦象会显示您设置的日期和对应的干支

## 技术实现

### 1. 组件结构
```
DateTimePicker (日期时间选择器)
├── Calendar (日期选择)
├── Select (时辰选择)
└── Button (快速设置)

ManualCoinInput (手动摇卦组件)
├── Switch (自定义日期开关)
├── DateTimePicker (日期选择器)
└── 原有摇卦功能
```

### 2. 数据流
```
用户选择日期 → ManualCoinInput → DivinationInterface → generateHexagram → 干支计算
```

### 3. 关键函数修改
- `generateHexagram()`: 新增 `customDate` 可选参数
- `ManualCoinInput.onComplete()`: 新增日期参数传递
- `handleManualComplete()`: 处理自定义日期

## 示例效果

### 使用当前时间（2025年7月25日19时）
```
干支：丙午年 乙亥月 甲戌日 甲戌时
```

### 使用自定义时间（2024年1月1日10时）
```
干支：乙巳年 丁巳月 癸卯日 丁巳时
```

## 注意事项

1. **时区处理**：系统使用本地时区进行计算
2. **历法转换**：自动处理公历到农历的转换
3. **节气影响**：月干支计算会考虑节气交接时间
4. **数据一致性**：确保摇卦记录中保存的是用户选择的日期

## 未来扩展

1. **农历日期输入**：支持直接输入农历日期
2. **历史日期验证**：对过于久远的日期进行合理性检查
3. **时区选择**：支持不同时区的日期计算
4. **批量摇卦**：支持为多个不同日期批量生成卦象
