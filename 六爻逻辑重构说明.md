# 六爻逻辑重构说明

## 概述

基于GitHub项目 `bopo-najia` 的纳甲六爻排盘系统，我重新实现了完整的六爻逻辑，包括六神、六亲、地支、五行、本卦、变卦等所有核心功能。

## 重构内容

### 1. 常量定义重构 (`src/constants/najia.ts`)

- **八卦序列**: 按传统顺序定义八卦
- **天干地支**: 完整的天干地支定义
- **五行系统**: 木火土金水五行定义
- **六亲六神**: 传统六亲六神定义
- **纳甲配置**: 完整的八卦纳甲天干地支配置
- **64卦映射**: 完整的64卦二进制到卦名映射
- **旬空配置**: 六甲旬空对照表

### 2. 核心计算逻辑重构 (`src/utils/hexagramCalculator.ts`)

#### 2.1 卦象计算
- `calculateHexagram()`: 根据爻线计算卦名和编号
- 支持完整的64卦识别

#### 2.2 六神计算
- `calculateSixSpirits()`: 基于日干起六神
- 实现传统口诀：甲乙起青龙，丙丁起朱雀，戊起勾陈，己起螣蛇，庚辛起白虎，壬癸起玄武

#### 2.3 纳甲系统
- `getNajia()`: 获取卦的纳甲天干地支配置
- 基于八卦的传统纳甲法

#### 2.4 六亲计算
- `getQin6()`: 基于五行生克关系计算六亲
- `calculateSixRelations()`: 完整的六亲关系计算
- 实现五行生克：我生者为子孙，生我者为父母，克我者为官鬼，我克者为妻财，同我者为兄弟

#### 2.5 世应位置算法
- `setShiYao()`: 实现传统寻世诀算法
- "天同二世天变五，地同四世地变初，本宫六世三世异，人同游魂人变归"

#### 2.6 卦宫判断
- `palace()`: 实现认宫诀算法
- "一二三六外卦宫，四五游魂内变更，归魂内卦是本宫"

#### 2.7 卦象类型判断
- `getType()`: 判断游魂、归魂、六冲、六合等卦象类型
- `soul()`: 游魂归魂判断
- `attack()`: 六冲卦判断
- `unite()`: 六合卦判断

#### 2.8 干支五行系统
- `GZ5X()`: 干支五行计算
- `xkong()`: 旬空计算
- `getStemBranch()`: 干支计算

#### 2.9 变卦逻辑
- `calculateChangedHexagram()`: 计算变卦
- 动爻变化：老阳变少阴，老阴变少阳
- 变卦六亲按变卦所在卦宫计算

#### 2.10 伏神计算
- `calculateHiddenSpirits()`: 计算伏神
- 当六亲不全时，从本宫卦中寻找伏神

### 3. 组件更新

#### 3.1 HexagramUtils组件
- 更新接口定义，增加卦象类型和变卦信息
- 集成所有新的计算逻辑

#### 3.2 HexagramDisplay组件
- 显示卦象类型信息
- 优化变卦显示逻辑，只显示动爻的变化

#### 3.3 DivinationInterface组件
- 简化变卦处理逻辑
- 使用新的数据结构

## 核心算法实现

### 寻世诀算法
```typescript
// 天同二世天变五，地同四世地变初，本宫六世三世异，人同游魂人变归
export const setShiYao = (symbol: string): [number, number, number] => {
  const wai = symbol.slice(3); // 外卦
  const nei = symbol.slice(0, 3); // 内卦
  
  // 实现完整的寻世诀逻辑
  // ...
}
```

### 六亲生克算法
```typescript
// 基于五行生克关系计算六亲
export const getQin6 = (w1: string | number, w2: string | number): string => {
  const w1Index = typeof w1 === 'string' ? XING5.indexOf(w1) : w1;
  const w2Index = typeof w2 === 'string' ? XING5.indexOf(w2) : w2;
  
  const ws = ((w1Index - w2Index) + 5) % 5;
  return QING6[ws];
};
```

### 六神起法算法
```typescript
// 甲乙起青龙，丙丁起朱雀等
export const calculateSixSpirits = (dayStem: string): string[] => {
  const gm = GANS.indexOf(dayStem);
  let num = Math.ceil((gm + 1) / 2) - 7;
  
  // 特殊处理戊己
  if (gm === 4) num = -4; // 戊
  if (gm === 5) num = -3; // 己
  if (gm > 5) num += 1;
  
  const startIndex = ((num % 6) + 6) % 6;
  return [...SHEN6.slice(startIndex), ...SHEN6.slice(0, startIndex)];
};
```

## 测试验证

创建了完整的测试系统：
- `src/test/hexagramTest.ts`: 核心逻辑测试
- `src/components/TestPage.tsx`: 测试结果展示页面
- 访问 `/test` 路由可查看测试结果

## 主要改进

1. **准确性**: 基于传统纳甲法，确保所有计算准确无误
2. **完整性**: 实现了完整的64卦系统，包括所有特殊卦象
3. **算法优化**: 使用高效的算法实现复杂的六爻逻辑
4. **代码结构**: 模块化设计，便于维护和扩展
5. **类型安全**: 完整的TypeScript类型定义

## 使用方法

1. 启动开发服务器: `npm run dev`
2. 访问主页面进行六爻排盘
3. 访问 `/test` 页面查看算法测试结果
4. 所有计算都基于传统纳甲法，确保准确性

## 技术特点

- 完全基于传统六爻纳甲法
- 支持完整的64卦系统
- 准确的世应位置计算
- 正确的六亲生克关系
- 完整的变卦逻辑
- 准确的伏神计算
- 传统的旬空算法

这次重构确保了六爻逻辑的完整性和准确性，为用户提供了专业级的六爻排盘功能。
