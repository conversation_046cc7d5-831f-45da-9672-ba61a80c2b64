// 伏神计算测试 - 专门测试雷地豫卦
import {
  calculateSixRelations,
  calculateHiddenSpirits,
  seekForOrigin,
  getNajia,
  findReps
} from '../utils/hexagramCalculator';
import { EARTH_ENUM_MAP, ZHIS, XING5, ZHI5 } from '../constants/najia';
import { YaoType } from '../components/YaoLine';

console.log('=== 雷地豫卦伏神测试 ===');

// 雷地豫卦：上震下坤 = 001000
// 从下到上：阴阴阴阳阴阴
const leiDiYuLines: YaoType[] = ['yin', 'yin', 'yin', 'yang', 'yin', 'yin'];

console.log('1. 雷地豫卦爻线:', leiDiYuLines);

// 转换为二进制字符串
const binaryString = leiDiYuLines.map(line => {
  const isYang = line === 'yang' || line === 'yang_changing';
  return isYang ? '1' : '0';
}).join('');
console.log('2. 二进制表示:', binaryString);

// 获取卦宫
const origin = seekForOrigin(leiDiYuLines);
console.log('3. 卦宫:', origin);

// 获取世应位置
import { calculateWorldAndResponse } from '../utils/hexagramCalculator';
const worldResponse = calculateWorldAndResponse(leiDiYuLines);
console.log('3.1 世应位置:', worldResponse);

// 获取纳甲配置
const najiaGanZhi = getNajia(binaryString);
console.log('4. 纳甲配置:', najiaGanZhi);

// 计算六亲关系
const sixRelations = calculateSixRelations(16, leiDiYuLines); // 16是雷地豫卦的卦序
console.log('5. 六亲关系:', sixRelations);

// 分析六亲是否齐全
const relationTypes = ['父母', '官鬼', '兄弟', '子孙', '妻财'];
const existingTypes = sixRelations.map(rel => rel.substring(0, 2));
const missingRelations = relationTypes.filter(type => !existingTypes.includes(type));
console.log('6. 现有六亲:', existingTypes);
console.log('7. 缺失六亲:', missingRelations);

// 计算伏神
const hiddenSpirits = calculateHiddenSpirits(leiDiYuLines, sixRelations);
console.log('8. 当前计算的伏神:', hiddenSpirits);

console.log('\n=== 手动验证本宫卦 ===');

// 震宫八纯卦：震为雷 = 001001 (这是错误的调用方式)
const zhengongBinary = '001001';
const zhengongNajia = getNajia(zhengongBinary);
console.log('9. 错误的震宫八纯卦纳甲:', zhengongNajia);

// 正确的震宫纳甲配置（直接使用NAJIA[4]，震宫索引是4）
import { NAJIA } from '../constants/najia';
const correctZhengongNajia = [
  NAJIA[4][0][0] + NAJIA[4][0][1], // 内卦第1爻
  NAJIA[4][0][0] + NAJIA[4][0][2], // 内卦第2爻
  NAJIA[4][0][0] + NAJIA[4][0][3], // 内卦第3爻
  NAJIA[4][1][0] + NAJIA[4][1][1], // 外卦第1爻
  NAJIA[4][1][0] + NAJIA[4][1][2], // 外卦第2爻
  NAJIA[4][1][0] + NAJIA[4][1][3]  // 外卦第3爻
];
console.log('9.1 正确的震宫纳甲配置:', correctZhengongNajia);

// 震宫属木，property = 3
const property = 3;
console.log('10. 震宫五行属性:', property);

// 计算震宫八纯卦的六亲（使用正确的纳甲配置）
console.log('11. 震宫八纯卦六亲（正确版本）:');
for (let i = 0; i < 6; i++) {
  const ganZhi = correctZhengongNajia[i];
  const diZhi = ganZhi[1];
  const earthEnum = EARTH_ENUM_MAP[diZhi as any];
  const liuqin = findReps(property, earthEnum);
  const diZhiIndex = ZHIS.indexOf(diZhi as any);
  const diZhiWuxing = XING5[ZHI5[diZhiIndex]];

  console.log(`  第${i+1}爻: ${ganZhi} ${liuqin} ${diZhiWuxing}`);
}

console.log('\n=== 手动验证六亲关系 ===');
console.log('雷地豫卦纳甲配置:', najiaGanZhi);
console.log('震宫属木，property = 3');
for (let i = 0; i < 6; i++) {
  const ganZhi = najiaGanZhi[i];
  const diZhi = ganZhi[1];
  const earthEnum = EARTH_ENUM_MAP[diZhi as any];
  const liuqin = findReps(3, earthEnum);
  console.log(`第${i+1}爻: ${ganZhi} -> 地支${diZhi} -> 枚举${earthEnum} -> 六亲${liuqin}`);
}

console.log('\n=== 期望结果 ===');
console.log('根据传统六爻理论，雷地豫卦应该只有一个伏神：');
console.log('父母子水 - 伏在初爻下');
