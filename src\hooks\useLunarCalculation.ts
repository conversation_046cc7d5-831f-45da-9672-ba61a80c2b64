import { useState, useEffect } from 'react';
import { xkong } from '@/utils/hexagramCalculator';

interface StemBranchInfo {
  year: string;
  month: string;
  day: string;
  hour: string;
}

interface VoidnessInfo {
  year: string[];
  month: string[];
  day: string[];
  hour: string[];
}

interface LunarCalculationResult {
  stems: StemBranchInfo;
  voidness: VoidnessInfo;
  isLoading: boolean;
  isAccurate: boolean; // 是否使用了准确的lunar-javascript计算
}

export const useLunarCalculation = (date: Date): LunarCalculationResult => {
  const [result, setResult] = useState<LunarCalculationResult>(() => {
    // 初始状态：使用降级计算
    const fallbackStems = {
      year: '未知年',
      month: '未知月',
      day: '未知日',
      hour: '未知时'
    };
    const dayGanZhi = fallbackStems.day.replace('日', '');
    const fallbackVoidness = xkong(dayGan<PERSON>hi).split('');

    return {
      stems: fallbackStems,
      voidness: {
        year: fallbackVoidness,
        month: fallbackVoidness,
        day: fallbackVoidness,
        hour: fallbackVoidness
      },
      isLoading: true,
      isAccurate: false
    };
  });

  useEffect(() => {
    let isCancelled = false;

    const loadLunarCalculation = async () => {
      try {
        // 动态导入lunar-javascript
        const { Solar } = await import('lunar-javascript');
        
        if (isCancelled) return;

        const solar = Solar.fromYmdHms(
          date.getFullYear(),
          date.getMonth() + 1,
          date.getDate(),
          date.getHours(),
          date.getMinutes(),
          date.getSeconds()
        );
        
        const lunar = solar.getLunar();
        const eightChar = lunar.getEightChar();
        
        const accurateStems = {
          year: eightChar.getYear() + '年',
          month: eightChar.getMonth() + '月',
          day: eightChar.getDay() + '日',
          hour: eightChar.getTime() + '时'
        };

        const accurateVoidness = {
          year: eightChar.getYearXunKong().split(''),
          month: eightChar.getMonthXunKong().split(''),
          day: eightChar.getDayXunKong().split(''),
          hour: eightChar.getTimeXunKong().split('')
        };

        if (!isCancelled) {
          setResult({
            stems: accurateStems,
            voidness: accurateVoidness,
            isLoading: false,
            isAccurate: true
          });
        }
      } catch (error) {
        console.error('Failed to load lunar-javascript, using fallback calculation:', error);
        
        if (!isCancelled) {
          // 保持降级计算结果，但标记为加载完成
          setResult(prev => ({
            ...prev,
            isLoading: false,
            isAccurate: false
          }));
        }
      }
    };

    loadLunarCalculation();

    return () => {
      isCancelled = true;
    };
  }, [date]);

  return result;
};
