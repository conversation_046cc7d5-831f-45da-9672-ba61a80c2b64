import React, { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { supabase, debugAuthState } from '@/lib/supabase'
import { useAuthStore } from '@/stores/authStore'

export const AuthDebugQuick: React.FC = () => {
  const [logs, setLogs] = useState<string[]>([])
  const { user, signIn } = useAuthStore()

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString()
    setLogs(prev => [...prev, `[${timestamp}] ${message}`])
    console.log(message)
  }

  const clearLogs = () => setLogs([])

  const testAuth = async () => {
    addLog('开始认证测试...')
    
    try {
      // 1. 检查前端状态
      addLog(`前端用户状态: ${user ? user.email : '未登录'}`)
      
      // 2. 运行调试函数
      await debugAuthState()
      
      // 3. 如果没有用户，尝试登录
      if (!user) {
        addLog('尝试登录...')
        await signIn('<EMAIL>', '123456')
        addLog('登录完成')
      }
      
      // 4. 测试简单的数据库查询
      addLog('测试数据库连接...')
      const { data, error } = await supabase
        .from('user_profiles')
        .select('count')
        .limit(1)
      
      if (error) {
        addLog(`数据库查询错误: ${error.message}`)
      } else {
        addLog('数据库连接正常')
      }
      
    } catch (error: any) {
      addLog(`测试失败: ${error.message}`)
    }
  }

  const testDirectInsert = async () => {
    addLog('测试直接数据库插入...')
    
    try {
      const testRecord = {
        user_id: user?.id || 'test-user-id',
        hexagram_name: '测试卦',
        hexagram_number: 1,
        original_hexagram: '111111',
        coin_results: [3, 2, 3, 2, 3, 2],
        divination_time: new Date().toISOString(),
        question: '测试问题',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
      
      addLog('准备插入测试记录...')
      
      const insertPromise = supabase
        .from('divination_records')
        .insert(testRecord)
        .select()
        .single()
      
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('插入操作超时')), 10000)
      })
      
      const { data, error } = await Promise.race([insertPromise, timeoutPromise]) as any
      
      if (error) {
        addLog(`插入失败: ${error.message}`)
      } else {
        addLog('插入成功!')
        addLog(`插入的记录ID: ${data.id}`)
      }
      
    } catch (error: any) {
      addLog(`插入测试失败: ${error.message}`)
    }
  }

  const clearAuth = async () => {
    addLog('清理认证状态...')
    
    try {
      // 清理localStorage
      Object.keys(localStorage).forEach(key => {
        if (key.includes('supabase') || key.includes('sb-')) {
          localStorage.removeItem(key)
          addLog(`清理: ${key}`)
        }
      })
      
      // 登出
      await supabase.auth.signOut()
      addLog('认证状态已清理')
      
    } catch (error: any) {
      addLog(`清理失败: ${error.message}`)
    }
  }

  return (
    <div className="container mx-auto p-4 max-w-4xl">
      <Card>
        <CardHeader>
          <CardTitle>认证问题快速调试</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2 flex-wrap">
            <Button onClick={testAuth}>测试认证状态</Button>
            <Button onClick={testDirectInsert} disabled={!user}>测试数据库插入</Button>
            <Button onClick={clearAuth} variant="destructive">清理认证状态</Button>
            <Button onClick={clearLogs} variant="outline">清理日志</Button>
          </div>
          
          <div className="bg-gray-100 p-4 rounded-lg max-h-96 overflow-y-auto">
            <h3 className="font-semibold mb-2">调试日志:</h3>
            {logs.length === 0 ? (
              <p className="text-gray-500">暂无日志</p>
            ) : (
              <div className="space-y-1">
                {logs.map((log, index) => (
                  <div key={index} className="text-sm font-mono">
                    {log}
                  </div>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
