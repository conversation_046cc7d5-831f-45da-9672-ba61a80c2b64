import { useState, useEffect, useCallback, useRef } from 'react'
import { supabase, DivinationRecord } from '@/lib/supabase'
import { useAuthStore } from '@/stores/authStore'

export const useDivinationRecords = () => {
  const { user, loading: authLoading } = useAuthStore()
  const [records, setRecords] = useState<DivinationRecord[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const fetchingRef = useRef(false)
  const lastUserIdRef = useRef<string | null>(null)

  // 获取用户的所有摇卦记录
  const fetchRecords = useCallback(async () => {
    console.log('fetchRecords called, user:', user?.id, 'authLoading:', authLoading, 'fetching:', fetchingRef.current)

    // 防止重复调用
    if (fetchingRef.current) {
      console.log('Already fetching, skipping...')
      return
    }

    // 如果正在加载认证状态，等待一下
    if (authLoading) {
      console.log('Auth still loading, skipping fetch')
      return
    }

    if (!user) {
      console.log('No user, setting empty records')
      setRecords([])
      setLoading(false)
      setError(null)
      return
    }

    fetchingRef.current = true

    try {
      setLoading(true)
      console.log('Starting query for user:', user.id)

      // 移除会话检查，直接依赖用户状态
      console.log('用户已登录，准备查询数据库...')

      // 添加超时处理，防止查询卡住
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('查询超时，请检查网络连接')), 10000) // 10秒超时
      })

      const queryPromise = supabase
        .from('divination_records')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })

      console.log('Executing query...')
      const { data, error } = await Promise.race([queryPromise, timeoutPromise]) as any

      console.log('Query result:', { data: data?.length || 0, error })

      if (error) {
        console.error('Query error details:', {
          code: error.code,
          message: error.message,
          details: error.details,
          hint: error.hint
        })

        // 特殊处理各种错误
        if (error.code === '42501') {
          throw new Error('权限验证失败，请重新登录')
        } else if (error.message.includes('JWT')) {
          throw new Error('认证令牌无效，请重新登录')
        }

        throw error
      }

      setRecords(data || [])
      setError(null)
      console.log('Records set successfully:', data?.length || 0)
    } catch (err) {
      console.error('fetchRecords error:', err)
      const errorMessage = err instanceof Error ? err.message : '获取记录失败'
      setError(errorMessage)
      setRecords([])
    } finally {
      setLoading(false)
      fetchingRef.current = false
      console.log('fetchRecords completed')
    }
  }, [user?.id, authLoading])

  // 保存新的摇卦记录
  const saveRecord = async (record: Omit<DivinationRecord, 'id' | 'user_id' | 'created_at' | 'updated_at'>) => {
    if (!user) {
      throw new Error('用户未登录')
    }

    // 直接进行数据库操作，不检查session避免卡住
    console.log('准备进行数据库操作，用户ID:', user.id)

    try {
      // 数据验证和清理
      const cleanedRecord = {
        hexagram_name: record.hexagram_name,
        hexagram_number: record.hexagram_number,
        original_hexagram: record.original_hexagram,
        changed_hexagram: record.changed_hexagram || null,
        coin_results: Array.isArray(record.coin_results) ? record.coin_results : [],
        divination_time: record.divination_time,
        question: record.question || null,
        interpretation: record.interpretation || null,
        analysis_status: record.analysis_status || 'pending',
        analysis_type: record.analysis_type || 'basic',
        hexagram_data: record.hexagram_data || null,
        user_id: user.id,
      }

      console.log('准备保存摇卦记录:', cleanedRecord.hexagram_name)
      console.log('当前用户ID:', user.id)
      console.log('记录数据概览:', {
        hexagram_name: cleanedRecord.hexagram_name,
        hexagram_number: cleanedRecord.hexagram_number,
        coin_results_length: cleanedRecord.coin_results?.length,
        has_question: !!cleanedRecord.question,
        analysis_status: cleanedRecord.analysis_status
      })

      // 验证必填字段
      console.log('验证必填字段...')
      if (!cleanedRecord.hexagram_name) {
        throw new Error('卦象名称不能为空')
      }
      if (!cleanedRecord.hexagram_number) {
        throw new Error('卦象编号不能为空')
      }
      if (!cleanedRecord.original_hexagram) {
        throw new Error('原卦不能为空')
      }
      if (!cleanedRecord.coin_results || cleanedRecord.coin_results.length === 0) {
        throw new Error('摇卦结果不能为空')
      }
      if (!cleanedRecord.divination_time) {
        throw new Error('摇卦时间不能为空')
      }
      console.log('字段验证通过')

      // 直接使用Supabase客户端进行插入，添加超时保护
      console.log('开始执行数据库插入操作...')

      const insertPromise = supabase
        .from('divination_records')
        .insert(cleanedRecord)
        .select()
        .single()

      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('数据库操作超时，请检查网络连接')), 15000) // 15秒超时
      })

      const { data, error } = await Promise.race([insertPromise, timeoutPromise]) as any
      console.log('数据库插入操作完成')

      if (error) {
        console.error('保存记录失败:', error)
        console.error('错误详情:', {
          code: error.code,
          message: error.message,
          details: error.details,
          hint: error.hint
        })

        // 特殊处理各种错误
        if (error.code === '42501') {
          throw new Error('权限验证失败，请重新登录后再试')
        } else if (error.code === 'PGRST301') {
          throw new Error('数据格式错误，请检查输入数据')
        } else if (error.message.includes('JWT')) {
          throw new Error('认证令牌无效，请重新登录')
        } else if (error.code === '23505') {
          throw new Error('记录已存在，请勿重复保存')
        }

        throw new Error(`保存失败: ${error.message}`)
      }

      if (!data) {
        throw new Error('插入成功但没有返回数据')
      }

      console.log('记录保存成功，ID:', data.id)

      // 更新本地记录列表
      setRecords(prev => [data, ...prev])
      return data

    } catch (err) {
      console.error('Error in saveRecord:', err)
      const errorMessage = err instanceof Error ? err.message : '保存记录失败'
      setError(errorMessage)
      throw new Error(errorMessage)
    }
  }

  // 删除摇卦记录
  const deleteRecord = async (id: string) => {
    if (!user) {
      throw new Error('用户未登录')
    }

    try {
      const { error } = await supabase
        .from('divination_records')
        .delete()
        .eq('id', id)
        .eq('user_id', user.id)

      if (error) throw error

      // 更新本地记录列表
      setRecords(prev => prev.filter(record => record.id !== id))
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '删除记录失败'
      setError(errorMessage)
      throw new Error(errorMessage)
    }
  }

  // 更新摇卦记录
  const updateRecord = async (id: string, updates: Partial<DivinationRecord>) => {
    if (!user) {
      throw new Error('用户未登录')
    }

    try {
      const { data, error } = await supabase
        .from('divination_records')
        .update({
          ...updates,
          updated_at: new Date().toISOString(),
        })
        .eq('id', id)
        .eq('user_id', user.id)
        .select()
        .single()

      if (error) throw error

      // 更新本地记录列表
      setRecords(prev => prev.map(record => 
        record.id === id ? { ...record, ...data } : record
      ))
      return data
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '更新记录失败'
      setError(errorMessage)
      throw new Error(errorMessage)
    }
  }

  useEffect(() => {
    // 只有在认证状态稳定且用户存在时才获取记录，避免重复查询
    if (!authLoading && user?.id && !fetchingRef.current && lastUserIdRef.current !== user.id) {
      lastUserIdRef.current = user.id
      fetchRecords()
    } else if (!user?.id && lastUserIdRef.current) {
      // 用户登出时清理状态
      lastUserIdRef.current = null
      setRecords([])
      setLoading(false)
      setError(null)
    }
  }, [user?.id, authLoading]) // 移除fetchRecords依赖，避免循环

  return {
    records,
    loading,
    error,
    saveRecord,
    deleteRecord,
    updateRecord,
    refetch: fetchRecords,
  }
}
