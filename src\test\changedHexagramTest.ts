// 变卦六亲计算测试
import {
  calculateChangedHexagram,
  calculateSixRelations,
  seekForOrigin
} from '../utils/hexagramCalculator';
import { YaoType } from '../components/YaoLine';

console.log('=== 变卦六亲计算测试 ===');

// 雷地豫卦有动爻：二爻和五爻动
// 从下到上：阴阴(动)阴阳阴(动)阴 -> 变为：阴阳阴阳阳阴 = 泽水困
const leiDiYuChangingLines: YaoType[] = ['yin', 'yin_changing', 'yin', 'yang', 'yin_changing', 'yin'];

console.log('1. 本卦爻线（雷地豫）:', leiDiYuChangingLines);

// 计算本卦六亲
const origin = seekForOrigin(leiDiYuChangingLines);
console.log('2. 本卦卦宫:', origin);

const originalSixRelations = calculateSixRelations(16, leiDiYuChangingLines);
console.log('3. 本卦六亲:', originalSixRelations);

// 计算变卦
const changedHexagram = calculateChangedHexagram(leiDiYuChangingLines);
if (changedHexagram) {
  console.log('4. 变卦名称:', changedHexagram.name);
  console.log('5. 变卦爻线:', changedHexagram.lines);
  console.log('6. 变卦六亲:', changedHexagram.sixRelations);
  
  // 验证变卦的卦宫（应该与本卦相同）
  const changedOrigin = seekForOrigin(changedHexagram.lines);
  console.log('7. 变卦自身卦宫:', changedOrigin);
  console.log('8. 变卦六亲应该按本卦卦宫计算，本卦卦宫是:', origin);
} else {
  console.log('4. 没有动爻，无变卦');
}

console.log('\n=== 验证六亲计算是否正确 ===');
if (changedHexagram) {
  console.log('变卦六亲详细分析：');
  changedHexagram.sixRelations.forEach((relation, index) => {
    console.log(`第${index + 1}爻: ${relation}`);
  });

  console.log('\n震宫属木，变卦六亲应该按木的生克关系计算：');
  console.log('- 木克土 -> 妻财');
  console.log('- 土克木 -> 官鬼');
  console.log('- 木生火 -> 子孙');
  console.log('- 水生木 -> 父母');
  console.log('- 木同木 -> 兄弟');
}

console.log('\n=== 期望结果 ===');
console.log('根据传统六爻理论：');
console.log('- 本卦：雷地豫（震宫）');
console.log('- 变卦六亲应该按震宫（木）计算');
console.log('- 修正后的变卦六亲计算应该是正确的');
