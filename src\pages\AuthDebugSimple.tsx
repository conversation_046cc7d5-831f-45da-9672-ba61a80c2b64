import React, { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { useAuthStore } from '@/stores/authStore'
import { supabase } from '@/lib/supabase'
import { toast } from 'sonner'

export const AuthDebugSimple: React.FC = () => {
  const { user, signIn, signOut } = useAuthStore()
  const [logs, setLogs] = useState<string[]>([])
  const [testing, setTesting] = useState(false)

  const addLog = (message: string, type: 'info' | 'success' | 'error' = 'info') => {
    const timestamp = new Date().toLocaleTimeString()
    const prefix = type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️'
    const logMessage = `${timestamp} ${prefix} ${message}`
    setLogs(prev => [...prev, logMessage])
    console.log(logMessage)
  }

  const testAuthAndDB = async () => {
    setTesting(true)
    setLogs([])
    
    try {
      addLog('开始认证和数据库测试...')
      
      // 1. 检查前端认证状态
      addLog(`前端用户状态: ${user ? user.email : '未登录'}`)
      
      if (!user) {
        addLog('用户未登录，尝试登录...', 'info')
        try {
          await signIn('<EMAIL>', '123456')
          addLog('登录成功', 'success')
        } catch (error: any) {
          addLog(`登录失败: ${error.message}`, 'error')
          return
        }
      }
      
      // 2. 检查Supabase会话
      addLog('检查Supabase会话...')
      const { data: { session }, error: sessionError } = await supabase.auth.getSession()
      
      if (sessionError) {
        addLog(`会话错误: ${sessionError.message}`, 'error')
        return
      }
      
      if (!session) {
        addLog('没有Supabase会话', 'error')
        return
      }
      
      addLog(`Supabase会话用户: ${session.user.email}`, 'success')
      addLog(`会话过期时间: ${new Date(session.expires_at! * 1000).toLocaleString()}`)
      
      // 3. 测试数据库查询
      addLog('测试数据库查询...')
      const { data: queryData, error: queryError } = await supabase
        .from('divination_records')
        .select('id, hexagram_name, created_at')
        .limit(3)
      
      if (queryError) {
        addLog(`查询失败: ${queryError.message}`, 'error')
        addLog(`错误代码: ${queryError.code}`, 'error')
      } else {
        addLog(`查询成功，返回 ${queryData.length} 条记录`, 'success')
      }
      
      // 4. 测试数据库插入
      addLog('测试数据库插入...')
      const testRecord = {
        user_id: session.user.id,
        hexagram_name: '测试卦象',
        hexagram_number: 1,
        original_hexagram: '111111',
        coin_results: [3, 3, 3, 3, 3, 3],
        divination_time: new Date().toISOString(),
        question: '这是一个测试问题',
        analysis_status: 'completed'
      }
      
      const { data: insertData, error: insertError } = await supabase
        .from('divination_records')
        .insert(testRecord)
        .select()
        .single()
      
      if (insertError) {
        addLog(`插入失败: ${insertError.message}`, 'error')
        addLog(`错误代码: ${insertError.code}`, 'error')
        addLog(`错误详情: ${JSON.stringify(insertError.details)}`, 'error')
      } else {
        addLog(`插入成功，记录ID: ${insertData.id}`, 'success')
      }
      
      addLog('测试完成', 'success')
      
    } catch (error: any) {
      addLog(`测试异常: ${error.message}`, 'error')
    } finally {
      setTesting(false)
    }
  }

  const testSignOut = async () => {
    try {
      await signOut()
      addLog('退出登录成功', 'success')
      toast.success('退出成功')
    } catch (error: any) {
      addLog(`退出失败: ${error.message}`, 'error')
    }
  }

  const clearLogs = () => {
    setLogs([])
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-6">
      <div className="max-w-4xl mx-auto space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>认证和数据库调试</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="p-4 bg-muted rounded-lg">
                <h3 className="font-semibold mb-2">当前状态</h3>
                <p>用户: {user ? user.email : '未登录'}</p>
                <p>用户ID: {user?.id?.slice(0, 8) || '无'}...</p>
              </div>
              
              <div className="space-y-2">
                <Button onClick={testAuthAndDB} disabled={testing} className="w-full">
                  {testing ? '测试中...' : '运行完整测试'}
                </Button>
                
                {user && (
                  <Button onClick={testSignOut} variant="destructive" className="w-full">
                    退出登录
                  </Button>
                )}
              </div>
              
              <div className="space-y-2">
                <Button onClick={clearLogs} variant="outline" className="w-full">
                  清空日志
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>测试日志</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-black text-green-400 p-4 rounded-lg font-mono text-sm max-h-96 overflow-y-auto">
              {logs.length === 0 ? (
                <p>点击"运行完整测试"开始调试...</p>
              ) : (
                logs.map((log, index) => (
                  <div key={index} className={
                    log.includes('❌') ? 'text-red-400' : 
                    log.includes('✅') ? 'text-green-400' : 
                    'text-gray-300'
                  }>
                    {log}
                  </div>
                ))
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
