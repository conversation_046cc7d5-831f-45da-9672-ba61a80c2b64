Directory structure:
└── qichenx-liuyaodivining/
    ├── README.md
    ├── Design.md
    ├── enums.py
    ├── GUA.py
    ├── initialize.py
    ├── LICENSE
    ├── Logic_MainWindow.py
    ├── MainWindow.ui
    ├── Ui_MainWindow.py
    ├── utils.py
    ├── XIANG.py
    └── YAO.py

================================================
FILE: README.md
================================================
# LiuYaoDivining / 六爻排盘

基于PyQt6实现的可视化六爻排盘项目

## 本项目用于提供六爻排盘功能，具体包括：

1. 根据当前时间，随机起卦，记录所占事项及卦主性别
2. 对所的卦象进行处理，包括变卦、寻世应、纳甲、寻六亲、缺六亲、寻六神
3. 输出处理后的卦象



## TODO：

1. 保存卦象到本地
2. 手工指定卦象
3. 分析旺相休囚等因素
4. 从本地读取卦象



## Reference

公历时间转化为八字 https://github.com/6tail/lunar-python/tree/master


================================================
FILE: Design.md
================================================
[Binary file]


================================================
FILE: enums.py
================================================
from enum import Enum, unique

@unique
class Sky(Enum):
    JIA = '甲'
    YI = '乙'
    BING = '丙'
    DING = '丁'
    WU = '戊'
    JI = '己'
    GENG = '庚'
    XIN = '辛'
    REN = '壬'
    GUI = '癸'

@unique
class Earth(Enum):
    ZI = '子'
    CHOU = '丑'
    YIN = '寅'
    MAO = '卯'
    CHEN = '辰'
    SI = '巳'
    WU = '午'
    WEI = '未'
    SHEN = '申'
    YOU = '酉'
    XU = '戌'
    HAI = '亥'

@unique
class Reps(Enum):
    GUAN = '官鬼'
    QI = '妻财'
    XIONG = '兄弟'
    FU = '父母'
    ZI = '子孙'

@unique
class Soul(Enum):
    LONG = '青龙'
    QUE = '朱雀'
    CHEN = '勾陈'
    SHE = '腾蛇'
    HU = '白虎'
    WU = '玄武'

@unique
# 金0木3水1火4土2
class PropertyPair(Enum):
    GUAN = [[0, Earth.WU], [0, Earth.SI], [1, Earth.CHEN], [1, Earth.XU], [1, Earth.CHOU], [1, Earth.WEI], 
            [2, Earth.YIN], [2, Earth.MAO], [3, Earth.SHEN], [3, Earth.YOU], [4, Earth.HAI], [4, Earth.ZI]]
    QI = [[0, Earth.YIN], [0, Earth.MAO], [1, Earth.SI], [1, Earth.WU], [2, Earth.HAI], [2, Earth.ZI], 
            [3, Earth.CHEN], [3, Earth.XU], [3, Earth.CHOU], [3, Earth.WEI], [4, Earth.SHEN], [4, Earth.YOU]]
    XIONG = [[0, Earth.SHEN], [0, Earth.YOU], [1, Earth.ZI], [1, Earth.HAI], [2, Earth.CHEN], [2, Earth.XU], 
            [2, Earth.WU], [2, Earth.WEI], [3, Earth.YIN], [3, Earth.MAO], [4, Earth.SI], [4, Earth.WU]]
    FU = [[0, Earth.CHEN], [0, Earth.XU], [0, Earth.CHOU], [0, Earth.WEI], [1, Earth.SHEN], [1, Earth.YOU], 
          [2, Earth.SI], [2, Earth.WU], [3, Earth.HAI], [3, Earth.ZI], [4, Earth.YIN], [4, Earth.MAO]]
    ZI = [[0, Earth.HAI], [0, Earth.ZI], [1, Earth.YIN], [1, Earth.MAO], [2, Earth.SHEN], [2, Earth.YOU], 
          [3, Earth.SI], [3, Earth.WU], [4, Earth.CHEN], [4, Earth.XU], [4, Earth.CHOU], [4, Earth.WEI], ]


================================================
FILE: GUA.py
================================================
class GUA:
    yaos = None


================================================
FILE: initialize.py
================================================
from XIANG import XIANG
from GUA import GUA
from YAO import YAO
from enums import Sky, Earth
import hashlib
import random
from lunar_python import Solar
import datetime

def initialization(question, sex):

    xiang = XIANG()
    base = GUA()
    yaos_base = [YAO() for _ in range(6)]
    xiang.question = question
    xiang.sex = sex
    xiang.flag = 0
    
    random_res = getRandomList(xiang.question, xiang.sex)
    for i in range(6):
        transfer(yaos_base[i], random_res[i])

    if 0 in random_res or 3 in random_res:
        xiang.flag = 1
    xiang.base = base

    setDate(xiang)

    base.yaos = yaos_base

    return xiang


def getRandomList(question, sex):
    my_string = question + sex + str(datetime.datetime.now())
    my_bytes = str.encode(my_string)
    my_hash = hashlib.sha256(my_bytes).hexdigest()
    seed = int(my_hash, 16)
    random.seed(seed)
    random_list = [random.randint(0, 10000)%4 for _ in range(6)]
    return random_list


def transfer(y:YAO, integer):
    # 0老阴1少阴2少阳3老阳
    match integer:
        case 0:
            y.essence = 0
            y.feature = 0
        case 1:
            y.essence = 0
            y.feature = 1
        case 2:
            y.essence = 1
            y.feature = 1
        case 3:
            y.essence = 1
            y.feature = 0
        case _:
            return


def setDate(xiang:XIANG):
    current = datetime.datetime.now()
    lunar = Solar.fromYmdHms(current.year, current.month, current.day, 
                             current.hour, current.minute, current.second).getLunar()
    date = lunar.getEightChar()
    year = date.getYear()
    month = date.getMonth()
    day = date.getDay()
    hour = date.getTime()
    lacks = date.getDayXunKong()

    xiang.year = [matchSkyEnum(year[0]), matchEarthEnum(year[1])]
    xiang.month = [matchSkyEnum(month[0]), matchEarthEnum(month[1])]
    xiang.day = [matchSkyEnum(day[0]), matchEarthEnum(day[1])]
    xiang.hour = [matchSkyEnum(hour[0]), matchEarthEnum(hour[1])]
    xiang.lacks = [matchEarthEnum(lacks[0]), matchEarthEnum(lacks[1])]


def matchSkyEnum(s:str):
    return Sky(s)


def matchEarthEnum(e:str):
    return Earth(e)


================================================
FILE: LICENSE
================================================
MIT License

Copyright (c) 2023 QiChen

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.



================================================
FILE: Logic_MainWindow.py
================================================
from XIANG import XIANG
import utils
import sys
from Ui_MainWindow import Ui_Dialog
from PyQt6 import QtCore, QtGui, QtWidgets
from initialize import initialization

class Logic_MainWindow(QtWidgets.QMainWindow, Ui_Dialog):
    xiang = None

    startSignal = QtCore.pyqtSignal()
    clearSignal = QtCore.pyqtSignal()

    def __init__(self, parent=None):
        super(Logic_MainWindow, self).__init__(parent)
        self.setupUi(self)
        self.initUI()
    
    def initUI(self):
        self.startSignal.connect(self.liuYaoDivining)
        self.start_pushButton.clicked.connect(self.emitStartSignal)
        
        self.clearSignal.connect(self.clearPage)
        self.clear_pushButton.clicked.connect(self.emitClearSignal)

        self.exit_pushButton.clicked.connect(self.close)

    def emitStartSignal(self):
        self.startSignal.emit()
    
    def emitClearSignal(self):
        self.clearSignal.emit()

    def liuYaoDivining(self):
        question = self.question_lineEdit.text()
        sex = self.sex_comboBox.currentText()
        if question=='' or sex=='':
            message = QtWidgets.QMessageBox
            message.about(self,"警告","请确保完整填写所占事项与卦主性别")
            return
        
        self.xiang = initialization(question, sex)
        # 本卦推变卦
        utils.deriveChange(self.xiang)
        # 寻世应
        utils.seekForEgo(self.xiang)
        # 纳甲
        utils.matchSkyandEarch(self.xiang)
        # 寻卦宫，定六亲
        utils.seekForReps(self.xiang)
        # 缺六亲
        utils.seekForDefects(self.xiang)
        # 寻六神
        utils.seekForSouls(self.xiang)
        # 输出
        self.showUI()
        return

    def showUI(self):
        self.showDate()
        self.showSoul()
        self.showBaseReps()
        self.showBaseYAOs()
        self.showEgo()
        self.showChangeReps()
        self.showChangeYAOs()
        return

    def showDate(self):
        year = self.xiang.year
        month = self.xiang.month
        day = self.xiang.day
        hour = self.xiang.hour
        lacks = self.xiang.lacks
        date = ''
        date += year[0].value + year[1].value + '年，'
        date += month[0].value + month[1].value + '月，'
        date += day[0].value + day[1].value + '日，'
        date += hour[0].value + hour[1].value + '时   '
        date += '(空亡：' + lacks[0].value + lacks[1].value + ')'
        
        self.date_label.setText(date)
        return

    def showSoul(self):
        yaos = self.xiang.base.yaos
        widgets = [self.soul_1, self.soul_2, self.soul_3, self.soul_4, self.soul_5, self.soul_6]
        for i in range(6):
            soul = yaos[i].soul
            widgets[i].setText(soul.value)
        return

    def showBaseReps(self):
        yaos = self.xiang.base.yaos
        widgets = [self.base_rep_1, self.base_rep_2, self.base_rep_3, self.base_rep_4, self.base_rep_5, self.base_rep_6]
        for i in range(6):
            rep = yaos[i].representation
            najia = yaos[i].najia
            text = rep.value + najia[0].value + najia[1].value
            widgets[i].setText(text)
        return

    def showBaseYAOs(self):
        yaos = self.xiang.base.yaos
        widgets = [self.base_yao_1, self.base_yao_2, self.base_yao_3, self.base_yao_4, self.base_yao_5, self.base_yao_6]
        for i in range(6):
            if yaos[i].essence == 0:
                text = '▅▅▅▅▅▅▅▅  ▅▅▅▅▅▅▅▅'
            elif yaos[i].essence == 1:
                text = '▅▅▅▅▅▅▅▅▅▅▅▅▅▅▅▅▅'
            widgets[i].setText(text)
        return

    def showEgo(self):
        yaos = self.xiang.base.yaos
        widgets = [self.ego_1, self.ego_2, self.ego_3, self.ego_4, self.ego_5, self.ego_6]
        for i in range(6):
            text = ['   ', ' ', ' ', '   ']
            if yaos[i].feature == 0:
                text[0] = '变'
            if yaos[i].ego == 1:
                text[3] = '世'
            if yaos[i].other == 1:
                text[3] = '应'
            t = ''
            for j in text:
                t += str(j)
            widgets[i].setText(t)
            widgets[i].setAlignment(QtCore.Qt.AlignmentFlag.AlignLeft | QtCore.Qt.AlignmentFlag.AlignVCenter)
        return

    def showChangeReps(self):
        widgets = [self.change_rep_1, self.change_rep_2, self.change_rep_3, self.change_rep_4, self.change_rep_5, self.change_rep_6]
        if self.xiang.flag == 0:
            for w in widgets:
                w.setText('')
            return
        yaos = self.xiang.change.yaos
        for i in range(6):
            rep = yaos[i].representation
            najia = yaos[i].najia
            text = rep.value + najia[0].value + najia[1].value
            widgets[i].setText(text)
        return

    def showChangeYAOs(self):
        widgets = [self.change_yao_1, self.change_yao_2, self.change_yao_3, self.change_yao_4, self.change_yao_5, self.change_yao_6]
        if self.xiang.flag == 0:
            for w in widgets:
                w.setText('')
            return
        yaos = self.xiang.change.yaos
        for i in range(6):
            if yaos[i].essence == 0:
                text = '▅▅▅▅▅▅▅▅  ▅▅▅▅▅▅▅▅'
            elif yaos[i].essence == 1:
                text = '▅▅▅▅▅▅▅▅▅▅▅▅▅▅▅▅▅'
            widgets[i].setText(text)
        return

    def clearPage(self):
        self.question_lineEdit.setText('')
        self.sex_comboBox.setCurrentIndex(-1)
        self.date_label.setText('')
        widgets = [self.soul_1, self.soul_2, self.soul_3, self.soul_4, self.soul_5, self.soul_6, 
                   self.base_rep_1, self.base_rep_2, self.base_rep_3, self.base_rep_4, self.base_rep_5, self.base_rep_6, 
                   self.base_yao_1, self.base_yao_2, self.base_yao_3, self.base_yao_4, self.base_yao_5, self.base_yao_6, 
                   self.ego_1, self.ego_2, self.ego_3, self.ego_4, self.ego_5, self.ego_6, 
                   self.change_rep_1, self.change_rep_2, self.change_rep_3, self.change_rep_4, self.change_rep_5, self.change_rep_6, 
                   self.change_yao_1, self.change_yao_2, self.change_yao_3, self.change_yao_4, self.change_yao_5, self.change_yao_6
                   ]
        for w in widgets:
            w.setText('')
        return


if __name__ == "__main__":
    app = QtWidgets.QApplication(sys.argv)
    win = Logic_MainWindow()
    win.show()
    sys.exit(app.exec())


================================================
FILE: MainWindow.ui
================================================
[Binary file]


================================================
FILE: Ui_MainWindow.py
================================================
# Form implementation generated from reading ui file 'c:\Users\<USER>\Desktop\LiuYaoDivining\MainWindow.ui'
#
# Created by: PyQt6 UI code generator 6.4.2
#
# WARNING: Any manual changes made to this file will be lost when pyuic6 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt6 import QtCore, QtGui, QtWidgets


class Ui_Dialog(object):
    def setupUi(self, Dialog):
        Dialog.setObjectName("Dialog")
        Dialog.resize(920, 760)
        self.question_lineEdit = QtWidgets.QLineEdit(parent=Dialog)
        self.question_lineEdit.setGeometry(QtCore.QRect(130, 30, 480, 40))
        font = QtGui.QFont()
        font.setPointSize(13)
        self.question_lineEdit.setFont(font)
        self.question_lineEdit.setText("")
        self.question_lineEdit.setObjectName("question_lineEdit")
        self.question_label = QtWidgets.QLabel(parent=Dialog)
        self.question_label.setGeometry(QtCore.QRect(40, 30, 90, 40))
        font = QtGui.QFont()
        font.setPointSize(13)
        self.question_label.setFont(font)
        self.question_label.setObjectName("question_label")
        self.sex_label = QtWidgets.QLabel(parent=Dialog)
        self.sex_label.setGeometry(QtCore.QRect(40, 80, 90, 40))
        font = QtGui.QFont()
        font.setPointSize(13)
        self.sex_label.setFont(font)
        self.sex_label.setObjectName("sex_label")
        self.sex_comboBox = QtWidgets.QComboBox(parent=Dialog)
        self.sex_comboBox.setGeometry(QtCore.QRect(130, 80, 80, 40))
        font = QtGui.QFont()
        font.setPointSize(13)
        self.sex_comboBox.setFont(font)
        self.sex_comboBox.setObjectName("sex_comboBox")
        self.sex_comboBox.addItem("")
        self.sex_comboBox.addItem("")
        self.clear_pushButton = QtWidgets.QPushButton(parent=Dialog)
        self.clear_pushButton.setGeometry(QtCore.QRect(690, 720, 80, 30))
        font = QtGui.QFont()
        font.setPointSize(13)
        self.clear_pushButton.setFont(font)
        self.clear_pushButton.setObjectName("clear_pushButton")
        self.start_pushButton = QtWidgets.QPushButton(parent=Dialog)
        self.start_pushButton.setGeometry(QtCore.QRect(590, 720, 80, 30))
        font = QtGui.QFont()
        font.setPointSize(13)
        self.start_pushButton.setFont(font)
        self.start_pushButton.setObjectName("start_pushButton")
        self.save_pushButton = QtWidgets.QPushButton(parent=Dialog)
        self.save_pushButton.setGeometry(QtCore.QRect(20, 720, 80, 30))
        font = QtGui.QFont()
        font.setPointSize(13)
        self.save_pushButton.setFont(font)
        self.save_pushButton.setObjectName("save_pushButton")
        self.load_pushButton = QtWidgets.QPushButton(parent=Dialog)
        self.load_pushButton.setGeometry(QtCore.QRect(120, 720, 80, 30))
        font = QtGui.QFont()
        font.setPointSize(13)
        self.load_pushButton.setFont(font)
        self.load_pushButton.setObjectName("load_pushButton")
        self.exit_pushButton = QtWidgets.QPushButton(parent=Dialog)
        self.exit_pushButton.setGeometry(QtCore.QRect(790, 720, 80, 30))
        font = QtGui.QFont()
        font.setPointSize(13)
        self.exit_pushButton.setFont(font)
        self.exit_pushButton.setObjectName("exit_pushButton")
        self.date_label = QtWidgets.QLabel(parent=Dialog)
        self.date_label.setGeometry(QtCore.QRect(270, 140, 380, 45))
        font = QtGui.QFont()
        font.setPointSize(13)
        self.date_label.setFont(font)
        self.date_label.setText("")
        self.date_label.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        self.date_label.setObjectName("date_label")
        self.verticalLayoutWidget_2 = QtWidgets.QWidget(parent=Dialog)
        self.verticalLayoutWidget_2.setGeometry(QtCore.QRect(80, 240, 131, 271))
        font = QtGui.QFont()
        font.setPointSize(13)
        self.verticalLayoutWidget_2.setFont(font)
        self.verticalLayoutWidget_2.setObjectName("verticalLayoutWidget_2")
        self.base_rep_verticalLayout = QtWidgets.QVBoxLayout(self.verticalLayoutWidget_2)
        self.base_rep_verticalLayout.setContentsMargins(0, 0, 0, 0)
        self.base_rep_verticalLayout.setSpacing(0)
        self.base_rep_verticalLayout.setObjectName("base_rep_verticalLayout")
        self.base_rep_6 = QtWidgets.QLabel(parent=self.verticalLayoutWidget_2)
        font = QtGui.QFont()
        font.setPointSize(13)
        self.base_rep_6.setFont(font)
        self.base_rep_6.setText("")
        self.base_rep_6.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        self.base_rep_6.setObjectName("base_rep_6")
        self.base_rep_verticalLayout.addWidget(self.base_rep_6)
        self.base_rep_5 = QtWidgets.QLabel(parent=self.verticalLayoutWidget_2)
        font = QtGui.QFont()
        font.setPointSize(13)
        self.base_rep_5.setFont(font)
        self.base_rep_5.setText("")
        self.base_rep_5.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        self.base_rep_5.setObjectName("base_rep_5")
        self.base_rep_verticalLayout.addWidget(self.base_rep_5)
        self.base_rep_4 = QtWidgets.QLabel(parent=self.verticalLayoutWidget_2)
        font = QtGui.QFont()
        font.setPointSize(13)
        self.base_rep_4.setFont(font)
        self.base_rep_4.setText("")
        self.base_rep_4.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        self.base_rep_4.setObjectName("base_rep_4")
        self.base_rep_verticalLayout.addWidget(self.base_rep_4)
        self.base_rep_3 = QtWidgets.QLabel(parent=self.verticalLayoutWidget_2)
        font = QtGui.QFont()
        font.setPointSize(13)
        self.base_rep_3.setFont(font)
        self.base_rep_3.setText("")
        self.base_rep_3.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        self.base_rep_3.setObjectName("base_rep_3")
        self.base_rep_verticalLayout.addWidget(self.base_rep_3)
        self.base_rep_2 = QtWidgets.QLabel(parent=self.verticalLayoutWidget_2)
        font = QtGui.QFont()
        font.setPointSize(13)
        self.base_rep_2.setFont(font)
        self.base_rep_2.setText("")
        self.base_rep_2.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        self.base_rep_2.setObjectName("base_rep_2")
        self.base_rep_verticalLayout.addWidget(self.base_rep_2)
        self.base_rep_1 = QtWidgets.QLabel(parent=self.verticalLayoutWidget_2)
        font = QtGui.QFont()
        font.setPointSize(13)
        self.base_rep_1.setFont(font)
        self.base_rep_1.setText("")
        self.base_rep_1.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        self.base_rep_1.setObjectName("base_rep_1")
        self.base_rep_verticalLayout.addWidget(self.base_rep_1)
        self.verticalLayoutWidget = QtWidgets.QWidget(parent=Dialog)
        self.verticalLayoutWidget.setGeometry(QtCore.QRect(210, 240, 251, 271))
        font = QtGui.QFont()
        font.setPointSize(13)
        self.verticalLayoutWidget.setFont(font)
        self.verticalLayoutWidget.setObjectName("verticalLayoutWidget")
        self.base_yao_verticalLayout = QtWidgets.QVBoxLayout(self.verticalLayoutWidget)
        self.base_yao_verticalLayout.setContentsMargins(0, 0, 0, 0)
        self.base_yao_verticalLayout.setSpacing(0)
        self.base_yao_verticalLayout.setObjectName("base_yao_verticalLayout")
        self.base_yao_6 = QtWidgets.QLabel(parent=self.verticalLayoutWidget)
        font = QtGui.QFont()
        font.setPointSize(13)
        self.base_yao_6.setFont(font)
        self.base_yao_6.setText("")
        self.base_yao_6.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        self.base_yao_6.setObjectName("base_yao_6")
        self.base_yao_verticalLayout.addWidget(self.base_yao_6)
        self.base_yao_5 = QtWidgets.QLabel(parent=self.verticalLayoutWidget)
        font = QtGui.QFont()
        font.setPointSize(13)
        self.base_yao_5.setFont(font)
        self.base_yao_5.setText("")
        self.base_yao_5.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        self.base_yao_5.setObjectName("base_yao_5")
        self.base_yao_verticalLayout.addWidget(self.base_yao_5)
        self.base_yao_4 = QtWidgets.QLabel(parent=self.verticalLayoutWidget)
        font = QtGui.QFont()
        font.setPointSize(13)
        self.base_yao_4.setFont(font)
        self.base_yao_4.setText("")
        self.base_yao_4.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        self.base_yao_4.setObjectName("base_yao_4")
        self.base_yao_verticalLayout.addWidget(self.base_yao_4)
        self.base_yao_3 = QtWidgets.QLabel(parent=self.verticalLayoutWidget)
        font = QtGui.QFont()
        font.setPointSize(13)
        self.base_yao_3.setFont(font)
        self.base_yao_3.setText("")
        self.base_yao_3.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        self.base_yao_3.setObjectName("base_yao_3")
        self.base_yao_verticalLayout.addWidget(self.base_yao_3)
        self.base_yao_2 = QtWidgets.QLabel(parent=self.verticalLayoutWidget)
        font = QtGui.QFont()
        font.setPointSize(13)
        self.base_yao_2.setFont(font)
        self.base_yao_2.setText("")
        self.base_yao_2.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        self.base_yao_2.setObjectName("base_yao_2")
        self.base_yao_verticalLayout.addWidget(self.base_yao_2)
        self.base_yao_1 = QtWidgets.QLabel(parent=self.verticalLayoutWidget)
        font = QtGui.QFont()
        font.setPointSize(13)
        self.base_yao_1.setFont(font)
        self.base_yao_1.setText("")
        self.base_yao_1.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        self.base_yao_1.setObjectName("base_yao_1")
        self.base_yao_verticalLayout.addWidget(self.base_yao_1)
        self.verticalLayoutWidget_5 = QtWidgets.QWidget(parent=Dialog)
        self.verticalLayoutWidget_5.setGeometry(QtCore.QRect(20, 240, 61, 271))
        font = QtGui.QFont()
        font.setPointSize(13)
        self.verticalLayoutWidget_5.setFont(font)
        self.verticalLayoutWidget_5.setObjectName("verticalLayoutWidget_5")
        self.soul_verticalLayout = QtWidgets.QVBoxLayout(self.verticalLayoutWidget_5)
        self.soul_verticalLayout.setContentsMargins(0, 0, 0, 0)
        self.soul_verticalLayout.setSpacing(0)
        self.soul_verticalLayout.setObjectName("soul_verticalLayout")
        self.soul_6 = QtWidgets.QLabel(parent=self.verticalLayoutWidget_5)
        font = QtGui.QFont()
        font.setPointSize(13)
        self.soul_6.setFont(font)
        self.soul_6.setText("")
        self.soul_6.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        self.soul_6.setObjectName("soul_6")
        self.soul_verticalLayout.addWidget(self.soul_6)
        self.soul_5 = QtWidgets.QLabel(parent=self.verticalLayoutWidget_5)
        font = QtGui.QFont()
        font.setPointSize(13)
        self.soul_5.setFont(font)
        self.soul_5.setText("")
        self.soul_5.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        self.soul_5.setObjectName("soul_5")
        self.soul_verticalLayout.addWidget(self.soul_5)
        self.soul_4 = QtWidgets.QLabel(parent=self.verticalLayoutWidget_5)
        font = QtGui.QFont()
        font.setPointSize(13)
        self.soul_4.setFont(font)
        self.soul_4.setText("")
        self.soul_4.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        self.soul_4.setObjectName("soul_4")
        self.soul_verticalLayout.addWidget(self.soul_4)
        self.soul_3 = QtWidgets.QLabel(parent=self.verticalLayoutWidget_5)
        font = QtGui.QFont()
        font.setPointSize(13)
        self.soul_3.setFont(font)
        self.soul_3.setText("")
        self.soul_3.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        self.soul_3.setObjectName("soul_3")
        self.soul_verticalLayout.addWidget(self.soul_3)
        self.soul_2 = QtWidgets.QLabel(parent=self.verticalLayoutWidget_5)
        font = QtGui.QFont()
        font.setPointSize(13)
        self.soul_2.setFont(font)
        self.soul_2.setText("")
        self.soul_2.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        self.soul_2.setObjectName("soul_2")
        self.soul_verticalLayout.addWidget(self.soul_2)
        self.soul_1 = QtWidgets.QLabel(parent=self.verticalLayoutWidget_5)
        font = QtGui.QFont()
        font.setPointSize(13)
        self.soul_1.setFont(font)
        self.soul_1.setText("")
        self.soul_1.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        self.soul_1.setObjectName("soul_1")
        self.soul_verticalLayout.addWidget(self.soul_1)
        self.verticalLayoutWidget_4 = QtWidgets.QWidget(parent=Dialog)
        self.verticalLayoutWidget_4.setGeometry(QtCore.QRect(520, 240, 131, 271))
        font = QtGui.QFont()
        font.setPointSize(13)
        self.verticalLayoutWidget_4.setFont(font)
        self.verticalLayoutWidget_4.setObjectName("verticalLayoutWidget_4")
        self.change_rep_verticalLayout = QtWidgets.QVBoxLayout(self.verticalLayoutWidget_4)
        self.change_rep_verticalLayout.setContentsMargins(0, 0, 0, 0)
        self.change_rep_verticalLayout.setSpacing(0)
        self.change_rep_verticalLayout.setObjectName("change_rep_verticalLayout")
        self.change_rep_6 = QtWidgets.QLabel(parent=self.verticalLayoutWidget_4)
        font = QtGui.QFont()
        font.setPointSize(13)
        self.change_rep_6.setFont(font)
        self.change_rep_6.setText("")
        self.change_rep_6.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        self.change_rep_6.setObjectName("change_rep_6")
        self.change_rep_verticalLayout.addWidget(self.change_rep_6)
        self.change_rep_5 = QtWidgets.QLabel(parent=self.verticalLayoutWidget_4)
        font = QtGui.QFont()
        font.setPointSize(13)
        self.change_rep_5.setFont(font)
        self.change_rep_5.setText("")
        self.change_rep_5.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        self.change_rep_5.setObjectName("change_rep_5")
        self.change_rep_verticalLayout.addWidget(self.change_rep_5)
        self.change_rep_4 = QtWidgets.QLabel(parent=self.verticalLayoutWidget_4)
        font = QtGui.QFont()
        font.setPointSize(13)
        self.change_rep_4.setFont(font)
        self.change_rep_4.setText("")
        self.change_rep_4.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        self.change_rep_4.setObjectName("change_rep_4")
        self.change_rep_verticalLayout.addWidget(self.change_rep_4)
        self.change_rep_3 = QtWidgets.QLabel(parent=self.verticalLayoutWidget_4)
        font = QtGui.QFont()
        font.setPointSize(13)
        self.change_rep_3.setFont(font)
        self.change_rep_3.setText("")
        self.change_rep_3.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        self.change_rep_3.setObjectName("change_rep_3")
        self.change_rep_verticalLayout.addWidget(self.change_rep_3)
        self.change_rep_2 = QtWidgets.QLabel(parent=self.verticalLayoutWidget_4)
        font = QtGui.QFont()
        font.setPointSize(13)
        self.change_rep_2.setFont(font)
        self.change_rep_2.setText("")
        self.change_rep_2.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        self.change_rep_2.setObjectName("change_rep_2")
        self.change_rep_verticalLayout.addWidget(self.change_rep_2)
        self.change_rep_1 = QtWidgets.QLabel(parent=self.verticalLayoutWidget_4)
        font = QtGui.QFont()
        font.setPointSize(13)
        self.change_rep_1.setFont(font)
        self.change_rep_1.setText("")
        self.change_rep_1.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        self.change_rep_1.setObjectName("change_rep_1")
        self.change_rep_verticalLayout.addWidget(self.change_rep_1)
        self.verticalLayoutWidget_3 = QtWidgets.QWidget(parent=Dialog)
        self.verticalLayoutWidget_3.setGeometry(QtCore.QRect(650, 240, 251, 271))
        font = QtGui.QFont()
        font.setPointSize(13)
        self.verticalLayoutWidget_3.setFont(font)
        self.verticalLayoutWidget_3.setObjectName("verticalLayoutWidget_3")
        self.change_yao_verticalLayout = QtWidgets.QVBoxLayout(self.verticalLayoutWidget_3)
        self.change_yao_verticalLayout.setContentsMargins(0, 0, 0, 0)
        self.change_yao_verticalLayout.setSpacing(0)
        self.change_yao_verticalLayout.setObjectName("change_yao_verticalLayout")
        self.change_yao_6 = QtWidgets.QLabel(parent=self.verticalLayoutWidget_3)
        font = QtGui.QFont()
        font.setPointSize(13)
        self.change_yao_6.setFont(font)
        self.change_yao_6.setText("")
        self.change_yao_6.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        self.change_yao_6.setObjectName("change_yao_6")
        self.change_yao_verticalLayout.addWidget(self.change_yao_6)
        self.change_yao_5 = QtWidgets.QLabel(parent=self.verticalLayoutWidget_3)
        font = QtGui.QFont()
        font.setPointSize(13)
        self.change_yao_5.setFont(font)
        self.change_yao_5.setText("")
        self.change_yao_5.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        self.change_yao_5.setObjectName("change_yao_5")
        self.change_yao_verticalLayout.addWidget(self.change_yao_5)
        self.change_yao_4 = QtWidgets.QLabel(parent=self.verticalLayoutWidget_3)
        font = QtGui.QFont()
        font.setPointSize(13)
        self.change_yao_4.setFont(font)
        self.change_yao_4.setText("")
        self.change_yao_4.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        self.change_yao_4.setObjectName("change_yao_4")
        self.change_yao_verticalLayout.addWidget(self.change_yao_4)
        self.change_yao_3 = QtWidgets.QLabel(parent=self.verticalLayoutWidget_3)
        font = QtGui.QFont()
        font.setPointSize(13)
        self.change_yao_3.setFont(font)
        self.change_yao_3.setText("")
        self.change_yao_3.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        self.change_yao_3.setObjectName("change_yao_3")
        self.change_yao_verticalLayout.addWidget(self.change_yao_3)
        self.change_yao_2 = QtWidgets.QLabel(parent=self.verticalLayoutWidget_3)
        font = QtGui.QFont()
        font.setPointSize(13)
        self.change_yao_2.setFont(font)
        self.change_yao_2.setText("")
        self.change_yao_2.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        self.change_yao_2.setObjectName("change_yao_2")
        self.change_yao_verticalLayout.addWidget(self.change_yao_2)
        self.change_yao_1 = QtWidgets.QLabel(parent=self.verticalLayoutWidget_3)
        font = QtGui.QFont()
        font.setPointSize(13)
        self.change_yao_1.setFont(font)
        self.change_yao_1.setText("")
        self.change_yao_1.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        self.change_yao_1.setObjectName("change_yao_1")
        self.change_yao_verticalLayout.addWidget(self.change_yao_1)
        self.verticalLayoutWidget_6 = QtWidgets.QWidget(parent=Dialog)
        self.verticalLayoutWidget_6.setGeometry(QtCore.QRect(460, 240, 61, 271))
        font = QtGui.QFont()
        font.setPointSize(13)
        self.verticalLayoutWidget_6.setFont(font)
        self.verticalLayoutWidget_6.setObjectName("verticalLayoutWidget_6")
        self.ego_verticalLayout = QtWidgets.QVBoxLayout(self.verticalLayoutWidget_6)
        self.ego_verticalLayout.setContentsMargins(0, 0, 0, 0)
        self.ego_verticalLayout.setSpacing(0)
        self.ego_verticalLayout.setObjectName("ego_verticalLayout")
        self.ego_6 = QtWidgets.QLabel(parent=self.verticalLayoutWidget_6)
        font = QtGui.QFont()
        font.setPointSize(13)
        self.ego_6.setFont(font)
        self.ego_6.setText("")
        self.ego_6.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        self.ego_6.setObjectName("ego_6")
        self.ego_verticalLayout.addWidget(self.ego_6)
        self.ego_5 = QtWidgets.QLabel(parent=self.verticalLayoutWidget_6)
        font = QtGui.QFont()
        font.setPointSize(13)
        self.ego_5.setFont(font)
        self.ego_5.setText("")
        self.ego_5.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        self.ego_5.setObjectName("ego_5")
        self.ego_verticalLayout.addWidget(self.ego_5)
        self.ego_4 = QtWidgets.QLabel(parent=self.verticalLayoutWidget_6)
        font = QtGui.QFont()
        font.setPointSize(13)
        self.ego_4.setFont(font)
        self.ego_4.setText("")
        self.ego_4.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        self.ego_4.setObjectName("ego_4")
        self.ego_verticalLayout.addWidget(self.ego_4)
        self.ego_3 = QtWidgets.QLabel(parent=self.verticalLayoutWidget_6)
        font = QtGui.QFont()
        font.setPointSize(13)
        self.ego_3.setFont(font)
        self.ego_3.setText("")
        self.ego_3.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        self.ego_3.setObjectName("ego_3")
        self.ego_verticalLayout.addWidget(self.ego_3)
        self.ego_2 = QtWidgets.QLabel(parent=self.verticalLayoutWidget_6)
        font = QtGui.QFont()
        font.setPointSize(13)
        self.ego_2.setFont(font)
        self.ego_2.setText("")
        self.ego_2.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        self.ego_2.setObjectName("ego_2")
        self.ego_verticalLayout.addWidget(self.ego_2)
        self.ego_1 = QtWidgets.QLabel(parent=self.verticalLayoutWidget_6)
        font = QtGui.QFont()
        font.setPointSize(13)
        self.ego_1.setFont(font)
        self.ego_1.setText("")
        self.ego_1.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        self.ego_1.setObjectName("ego_1")
        self.ego_verticalLayout.addWidget(self.ego_1)

        self.retranslateUi(Dialog)
        self.sex_comboBox.setCurrentIndex(-1)
        QtCore.QMetaObject.connectSlotsByName(Dialog)

    def retranslateUi(self, Dialog):
        _translate = QtCore.QCoreApplication.translate
        Dialog.setWindowTitle(_translate("Dialog", "Dialog"))
        self.question_label.setText(_translate("Dialog", "所占何事："))
        self.sex_label.setText(_translate("Dialog", "卦主性别："))
        self.sex_comboBox.setItemText(0, _translate("Dialog", "男"))
        self.sex_comboBox.setItemText(1, _translate("Dialog", "女"))
        self.clear_pushButton.setText(_translate("Dialog", "清空"))
        self.start_pushButton.setText(_translate("Dialog", "起卦"))
        self.save_pushButton.setText(_translate("Dialog", "保存本卦"))
        self.load_pushButton.setText(_translate("Dialog", "读取"))
        self.exit_pushButton.setText(_translate("Dialog", "退出"))



================================================
FILE: utils.py
================================================
from GUA import GUA
from XIANG import XIANG
from YAO import YAO
from enums import Earth, PropertyPair, Reps, Sky, Soul

# 本卦推变卦
def deriveChange(xiang:XIANG):
    if xiang.flag == 1:
        change = GUA()
        xiang.change = change
        change.yaos = [YAO() for _ in range(6)]
        for i in range(len(xiang.base.yaos)):
            if xiang.base.yaos[i].feature == 0:
                change.yaos[i].feature = 0
                change.yaos[i].essence = (xiang.base.yaos[i].essence+1)%2
            else:
                change.yaos[i].feature = 1
                change.yaos[i].essence = xiang.base.yaos[i].essence


# 寻世应
def seekForEgo(xiang:XIANG):
    base = xiang.base
    yaos = base.yaos
    
    s = False
    h = False
    e = False
    if yaos[0].essence == yaos[3].essence:
        e = True
    if yaos[1].essence == yaos[4].essence:
        h = True
    if yaos[2].essence == yaos[5].essence:
        s = True
    
    if s and h and e:
        yaos[5].ego = 1
        yaos[2].other = 1
    if not (s or h or e):
        yaos[2].ego = 1
        yaos[5].other = 1
    if s and not h and not e:
        yaos[1].ego = 1
        yaos[4].other = 1
    if not s and h and e:
        yaos[4].ego = 1
        yaos[1].other = 1
    if not s and not h and e:
        yaos[3].ego = 1
        yaos[0].other = 1
    if s and h and not e:
        yaos[0].ego = 1
        yaos[3].other = 1
    if not s and h and not e:
        yaos[3].ego = 1
        yaos[0].other = 1
    if s and not h and e:
        yaos[2].ego = 1
        yaos[5].other = 1


# 纳甲
def matchSkyandEarch(xiang:XIANG):
    base = xiang.base
    innerMatch(base)
    outerMatch(base)
    if xiang.flag == 1:
        change = xiang.change
        innerMatch(change)
        outerMatch(change)


def innerMatch(g:GUA):
    sum = 0
    yaos = g.yaos
    # 计算卦宫
    for i in range(3):
        if yaos[i].essence == 1:
            sum += 2**i
    starting_point = getStartingPoint(sum, 0)
    jia = sort(starting_point, sum)
    yaos[0].najia = jia[0]
    yaos[1].najia = jia[1]
    yaos[2].najia = jia[2]


def outerMatch(g:GUA):
    sum = 0
    yaos = g.yaos
    # 计算卦宫
    for i in range(3,6):
        if yaos[i].essence == 1:
            sum += 2**(i-3)
    starting_point = getStartingPoint(sum, 1)
    jia = sort(starting_point, sum)
    yaos[3].najia = jia[0]
    yaos[4].najia = jia[1]
    yaos[5].najia = jia[2]


def getStartingPoint(sum, in_or_out):
    # in=0, out=1
    # 乾7坎2艮4震1巽6离5坤0兑3
    match sum:
        case 0:
            if in_or_out == 0:
                return [Sky.YI, Earth.WEI]
            else:
                return [Sky.GUI, Earth.CHOU]
        case 1:
            if in_or_out == 0:
                return [Sky.GENG, Earth.ZI]
            else:
                return [Sky.GENG, Earth.WU]
        case 2:
            if in_or_out == 0:
                return [Sky.WU, Earth.YIN]
            else:
                return [Sky.WU, Earth.SHEN]
        case 3:
            if in_or_out == 0:
                return [Sky.DING, Earth.SI]
            else:
                return [Sky.DING, Earth.HAI]
        case 4:
            if in_or_out == 0:
                return [Sky.BING, Earth.CHEN]
            else:
                return [Sky.BING, Earth.XU]
        case 5:
            if in_or_out == 0:
                return [Sky.JI, Earth.MAO]
            else:
                return [Sky.JI, Earth.YOU]
        case 6:
            if in_or_out == 0:
                return [Sky.XIN, Earth.CHOU]
            else:
                return [Sky.XIN, Earth.WEI]
        case 7:
            if in_or_out == 0:
                return [Sky.JIA, Earth.ZI]
            else:
                return [Sky.REN, Earth.WU]
        case _:
            return [Sky.REN, Earth.WU]


def sort(starting_point, sum):
    jia1 = [starting_point[0]]
    jia2 = [starting_point[0]]
    jia = [starting_point, jia1, jia2]

    earth = starting_point[1]
    enum = list(Earth.__members__.values())
    starting_index = enum.index(earth)
    # 7\2\4\1顺序，6\5\0\3逆序
    if sum in [1,2,4,7]:
        e1 = (starting_index + 2)%12
        e2 = (starting_index + 4)%12
    else:
        e1 = (starting_index - 2)%12
        e2 = (starting_index - 4)%12
    earth1 = Earth(enum[e1])
    earth2 = Earth(enum[e2])
    jia1.append(earth1)
    jia2.append(earth2)
    return jia


# 寻六亲
def seekForReps(xiang:XIANG):
    xiang.origin = seekForOrigin(xiang.base)
    property = 5
    # 乾、兑属金
    if xiang.origin in [7, 3]:
        property = 0
    # 坎属水
    elif xiang.origin in [2]:
        property = 1
    # 艮、坤属土
    elif xiang.origin in [4, 0]:
        property = 2
    # 震、巽属木
    elif xiang.origin in [1, 6]:
        property = 3
    # 离属火
    else:
        property = 4
    for yao in xiang.base.yaos:
        findReps(property, yao)
    if xiang.flag == 1:
        for yao in xiang.change.yaos:
            findReps(property, yao)


def seekForOrigin(g:GUA):
    yaos = g.yaos
    s = (yaos[2].essence==yaos[5].essence)
    h = (yaos[1].essence==yaos[4].essence)
    e = (yaos[0].essence==yaos[3].essence)
    # 乾7坎2艮4震1巽6离5坤0兑3
    sum = 0
    if s and not h and e:
        for i in range(3):
            if yaos[i].essence == 1:
                sum += 2**i
    else:
        ego = 0
        for i in range(6):
            if yaos[i].ego == 1:
                ego = i
                break
        if ego <= 2:
            for i in range(3):
                if yaos[i+3].essence == 1:
                    sum += 2**i
        else:
            for i in range(3):
                if yaos[i].essence == 0:
                    sum += 2**i
    return sum


def findReps(property, yao:YAO):
    # 金0木3水1火4土2
    earth = yao.najia[1]
    pair = [property, earth]
    if pair in PropertyPair.GUAN.value:
        yao.representation = Reps.GUAN
    elif pair in PropertyPair.QI.value:
        yao.representation = Reps.QI
    elif pair in PropertyPair.XIONG.value:
        yao.representation = Reps.XIONG
    elif pair in PropertyPair.FU.value:
        yao.representation = Reps.FU
    else:
        yao.representation = Reps.ZI


# 缺六亲
def seekForDefects(xiang:XIANG):
    base = xiang.base
    reps_set = set()
    for y in base.yaos:
        reps_set.add(y.representation)
    all_reps = {Reps.FU, Reps.GUAN, Reps.XIONG, Reps.ZI, Reps.QI}
    xiang.defects = list(all_reps-reps_set)


# 寻六神
def seekForSouls(xiang:XIANG):
    sky = xiang.day[0]
    origin_soul = None
    if sky in [Sky.JIA, Sky.YI]:
        origin_soul = Soul.LONG
    elif sky in [Sky.BING, Sky.DING]:
        origin_soul = Soul.QUE
    elif sky in [Sky.WU]:
        origin_soul = Soul.CHEN
    elif sky in [Sky.JI]:
        origin_soul = Soul.SHE
    elif sky in [Sky.GENG, Sky.XIN]:
        origin_soul = Soul.HU
    else:
        origin_soul = Soul.WU
    
    
    enum = list(Soul.__members__.values())
    starting_index = enum.index(origin_soul)
    yaos = xiang.base.yaos
    yaos[0].soul = origin_soul
    for i in range(1, 6):
        yaos[i].soul = Soul(enum[(starting_index+i)%6])


# 输出
def show(xiang:XIANG):
    showDate(xiang)
    base = xiang.base
    showGUA(base)
    if xiang.flag == 1:
        print('')
        print('~~~~~~~~~~')
        print('')
        change = xiang.change
        showGUA(change)
    print('缺六亲：'+str([d.value for d in xiang.defects]))


def showDate(xiang:XIANG):
    year = xiang.year
    month = xiang.month
    day = xiang.day
    hour = xiang.hour
    lacks = xiang.lacks
    
    date = ''
    date += year[0].value + year[1].value + '年，'
    date += month[0].value + month[1].value + '月，'
    date += day[0].value + day[1].value + '日，'
    date += hour[0].value + hour[1].value + '时   '
    date += '空亡：' + lacks[0].value + lacks[1].value
    print(date)


def showGUA(g:GUA):
    for i in range(5, -1, -1):
        y = g.yaos[i]
        showYAO(y)


def showYAO(y:YAO):
    line = ''
    if y.soul != None:
        line += y.soul.value
        line += ' '
    line += y.representation.value
    line += y.najia[0].value
    line += y.najia[1].value
    if y.essence == 0:
        line += '----  ----'
    else:
        line += '----------'
    if y.feature == 0:
        line += '  变'
    if y.ego == 1:
        line += ' 世'
    if y.other == 1:
        line += ' 应'
    print(line)


================================================
FILE: XIANG.py
================================================
class XIANG:
    flag = None
    base = None
    change = None
    origin = None
    defects = None
    year = None
    month = None
    day = None
    hour = None
    lacks = None
    question = None
    sex = None


================================================
FILE: YAO.py
================================================
class YAO:
    essence = None
    feature = None
    ego = None
    other = None
    najia = None
    representation = None
    soul = None


