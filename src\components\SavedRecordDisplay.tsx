import React from 'react';
import { DivinationCard } from './DivinationCard';
import { HexagramDisplay } from './HexagramDisplay';
import { MarkdownRenderer } from './MarkdownRenderer';
import { DivinationRecord } from '@/lib/supabase';
import { generateHexagram, Hexagram } from './HexagramUtils';
import { YaoType } from './YaoLine';
import { format } from 'date-fns';
import { zhCN, enUS } from 'date-fns/locale';
import { useTranslation } from '@/hooks/useTranslation';

interface SavedRecordDisplayProps {
  record: DivinationRecord;
}

export const SavedRecordDisplay: React.FC<SavedRecordDisplayProps> = ({ record }) => {
  const { t, currentLanguage } = useTranslation();
  // 从保存的记录获取卦象数据，优先使用数据库中的hexagram_data
  const getHexagramData = (): Hexagram | null => {
    try {
      // 如果有保存的hexagram_data，直接使用
      if (record.hexagram_data) {
        console.log('使用数据库中保存的hexagram_data:', record.hexagram_data);

        // 将数据库中的数据转换为Hexagram接口格式
        const hexagramData = record.hexagram_data;

        // 构建lines数组，确保类型正确
        const lines: YaoType[] = hexagramData.lines.map((lineType: string) => {
          switch (lineType) {
            case 'yang': return 'yang';
            case 'yin': return 'yin';
            case 'yang_changing': return 'yang_changing';
            case 'yin_changing': return 'yin_changing';
            default: return 'yin';
          }
        });

        return {
          lines,
          name: hexagramData.name,
          number: hexagramData.number || 0,
          sixSpirits: hexagramData.sixSpirits || [],
          sixRelations: hexagramData.sixRelations || [],
          worldPosition: hexagramData.worldPosition || 0,
          responsePosition: hexagramData.responsePosition || 0,
          hexagramType: hexagramData.hexagramType || '',
          hiddenSpirits: hexagramData.hiddenSpirits || [],
          divinationInfo: hexagramData.divinationInfo,
          changedHexagram: hexagramData.changedHexagram
        };
      }

      // 如果没有hexagram_data，则从coin_results重建（兼容旧数据）
      console.log('没有hexagram_data，从coin_results重建');
      const yaoTypes: YaoType[] = record.coin_results.map(sum => {
        switch (sum) {
          case 6: return 'yin_changing';  // 老阴
          case 7: return 'yang';          // 少阳
          case 8: return 'yin';           // 少阴
          case 9: return 'yang_changing'; // 老阳
          default: return 'yin';
        }
      });

      // 使用 generateHexagram 重建完整的卦象
      return generateHexagram(yaoTypes);
    } catch (error) {
      console.error('Error getting hexagram data:', error);
      return null;
    }
  };

  // 重建摇卦过程数据
  const reconstructCoinResults = () => {
    return record.coin_results.map((sum, index) => {
      // 根据总和反推硬币组合（这里简化处理）
      let coins: number[] = [];
      switch (sum) {
        case 6: coins = [2, 2, 2]; break; // 3背 = 老阴
        case 7: coins = [3, 2, 2]; break; // 1字2背 = 少阳
        case 8: coins = [3, 3, 2]; break; // 2字1背 = 少阴
        case 9: coins = [3, 3, 3]; break; // 3字 = 老阳
        default: coins = [3, 2, 2]; break;
      }
      
      return {
        coins,
        sum,
        yaoType: sum === 6 ? '老阴' :
                sum === 9 ? '老阳' :
                sum === 7 ? '少阳' : '少阴'
      };
    });
  };

  const hexagram = getHexagramData();
  const coinResults = reconstructCoinResults();

  if (!hexagram) {
    return (
      <div className="text-center text-muted-foreground py-8">
        <p>{t('cannotGetHexagramData')}</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 问题显示 */}
      {record.question && (
        <DivinationCard title={t('divinationQuestion')}>
          <div className="p-4 bg-background/30 rounded-lg">
            <p className="text-foreground">{record.question}</p>
          </div>
        </DivinationCard>
      )}

      {/* 起卦结果显示 - 与首页完全相同的布局 */}
      <DivinationCard title={t('divinationResult')}>
        <div className="space-y-4">
          {/* 日期和基本信息 */}
          <div className="grid md:grid-cols-2 gap-4 p-4 bg-background/30 rounded-lg">
            <div className="space-y-2">
              <div className="text-sm">
                <span className="text-muted-foreground">{t('gregorianCalendar')}：</span>
                <span className="text-foreground">
                  {format(new Date(record.divination_time), 'yyyy/M/d', { locale: currentLanguage === 'zh' ? zhCN : enUS })}
                </span>
              </div>
              <div className="text-sm">
                <span className="text-muted-foreground">{t('lunarCalendar')}：</span>
                <span className="text-foreground">{hexagram.divinationInfo.stems.year}</span>
              </div>
              <div className="text-sm">
                <span className="text-muted-foreground">{t('solarTermDisplay')}：</span>
                <span className="text-divination-primary">{hexagram.divinationInfo.solarTerm}</span>
              </div>
            </div>
            <div className="space-y-2">
              <div className="text-sm">
                <span className="text-muted-foreground">{t('heavenlyStems')}：</span>
                <span className="text-foreground">
                  {hexagram.divinationInfo.stems.year} {hexagram.divinationInfo.stems.month} {hexagram.divinationInfo.stems.day} {hexagram.divinationInfo.stems.hour}
                </span>
              </div>
              <div className="text-sm">
                <span className="text-muted-foreground">{t('voicelessDisplay')}：</span>
                <span className="text-destructive">
                  {hexagram.divinationInfo.voidness.year.join('')} {hexagram.divinationInfo.voidness.month.join('')} {hexagram.divinationInfo.voidness.day.join('')} {hexagram.divinationInfo.voidness.hour.join('')}
                </span>
              </div>
              <div className="text-sm">
                <span className="text-muted-foreground">{t('spiritsDisplay')}：</span>
                <span className="text-divination-accent">{hexagram.divinationInfo.spirits.join(' ')}</span>
              </div>
            </div>
          </div>

          {/* 摇卦过程 */}
          <div className="space-y-3">
            <h4 className="text-sm font-medium text-muted-foreground">{t('coinTossProcess')}</h4>
            <div className="grid grid-cols-6 gap-4 text-center">
              {coinResults.map((result, index) => (
                <div key={index} className="space-y-2 p-2 bg-background/20 rounded">
                  <div className="text-sm text-muted-foreground">{t('lineNumberTemplate').replace('{number}', String(index + 1))}</div>
                  <div className="space-y-1">
                    {result.coins.map((coin, coinIndex) => (
                      <div key={coinIndex} className="text-xs">
                        {coin === 3 ? t('headsDisplay') : t('tailsDisplay')}
                      </div>
                    ))}
                  </div>
                  <div className="text-xs text-divination-primary font-medium">
                    {result.yaoType}
                  </div>
                </div>
              ))}
            </div>
            <div className="text-center text-sm text-muted-foreground">
              {t('tossResult')}：{coinResults.map((result) => 
                `${result.coins.filter(c => c === 3).length}${t('headsDisplay')}${result.coins.filter(c => c === 2).length}${t('tailsDisplay')}`
              ).join(' ')}
            </div>
          </div>
        </div>
      </DivinationCard>

      {/* 卦象显示区 - 与首页完全相同 */}
      <HexagramDisplay hexagram={hexagram} />

      {/* 解读显示 */}
      <DivinationCard title={t('aiAnalysisResult')}>
        <div className="p-4 bg-background/30 rounded-lg">
          {record.analysis_status === 'pending' && (
            <div className="flex items-center space-x-2 text-amber-600">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-amber-600"></div>
              <span>{t('analysisPending')}</span>
            </div>
          )}

          {record.analysis_status === 'analyzing' && (
            <div className="flex items-center space-x-2 text-blue-600">
              <div className="animate-pulse rounded-full h-4 w-4 bg-blue-600"></div>
              <span>{t('analysisInProgressMsg')}</span>
            </div>
          )}

          {record.analysis_status === 'completed' && record.interpretation && (
            <MarkdownRenderer
              content={record.interpretation}
              className="text-foreground"
            />
          )}

          {record.analysis_status === 'failed' && (
            <div className="flex items-center space-x-2 text-red-600">
              <span className="text-red-500">⚠️</span>
              <span>{t('analysisFailed')}</span>
            </div>
          )}

          {/* 兼容旧记录：没有status字段但有interpretation的记录 */}
          {!record.analysis_status && record.interpretation && (
            <MarkdownRenderer
              content={record.interpretation}
              className="text-foreground"
            />
          )}

          {/* 没有任何分析结果的情况 */}
          {!record.analysis_status && !record.interpretation && (
            <div className="text-muted-foreground">
              {t('noAnalysisResult')}
            </div>
          )}
        </div>
      </DivinationCard>

      {/* 记录信息 */}
      <DivinationCard title={t('recordInfo')}>
        <div className="grid md:grid-cols-2 gap-4 p-4 bg-background/30 rounded-lg text-sm">
          <div>
            <span className="text-muted-foreground">{t('saveTime')}：</span>
            <span className="text-foreground">
              {format(new Date(record.created_at!), currentLanguage === 'zh' ? 'yyyy年MM月dd日 HH:mm:ss' : 'MMM dd, yyyy HH:mm:ss', { locale: currentLanguage === 'zh' ? zhCN : enUS })}
            </span>
          </div>
          <div>
            <span className="text-muted-foreground">{t('recordId')}：</span>
            <span className="text-foreground font-mono text-xs">{record.id}</span>
          </div>
        </div>
      </DivinationCard>
    </div>
  );
};
