project_id = "tpdslcjznbffqnpbuiiy"

[api]
enabled = true
port = 54321
schemas = ["public", "graphql_public"]
extra_search_path = ["public", "extensions"]
max_rows = 1000

[db]
port = 54322
shadow_port = 54320
major_version = 15

[db.pooler]
enabled = false
port = 54329
pool_mode = "transaction"

[realtime]
enabled = true
ip_version = "IPv4"

[studio]
enabled = true
port = 54324
api_url = "http://127.0.0.1:54321"
openai_api_key = "env(OPENAI_API_KEY)"

[inbucket]
enabled = true
port = 54325
smtp_port = 54326
pop3_port = 54327

[storage]
enabled = true
file_size_limit = "50MiB"

[auth]
enabled = true
site_url = "http://127.0.0.1:3000"
additional_redirect_urls = ["https://127.0.0.1:3000"]
jwt_expiry = 3600
enable_signup = true

[auth.email]
enable_signup = true
double_confirm_changes = true
enable_confirmations = false
max_frequency = "1s"

[auth.sms]
enable_signup = false
enable_confirmations = false
max_frequency = "5s"



[functions.verify-payment]
verify_jwt = false

[functions.stripe-webhook]
verify_jwt = false

[functions.create-checkout-session]
verify_jwt = true

[functions.analysis-coordinator]
verify_jwt = true

[functions.deepseek-analysis]
verify_jwt = true

[analytics]
enabled = false
port = 54327
vector_port = 54328

[experimental]
orioledb_version = ""
s3_host = "env(S3_HOST)"
s3_region = "env(S3_REGION)"
s3_access_key = "env(S3_ACCESS_KEY)"
s3_secret_key = "env(S3_SECRET_KEY)"