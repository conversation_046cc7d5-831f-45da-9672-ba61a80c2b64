import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { useThemeStore } from '@/stores/themeStore';
import { Heart, Star, Sparkles } from 'lucide-react';

interface DivinationCardProps {
  title: string;
  children: React.ReactNode;
  className?: string;
  glowing?: boolean;
}

export const DivinationCard: React.FC<DivinationCardProps> = ({
  title,
  children,
  className,
  glowing = false
}) => {
  const { currentTheme } = useThemeStore();

  const getCuteDecorations = () => {
    if (currentTheme !== 'cute') return null;
    
    return (
      <div className="absolute -top-1 -right-1 flex space-x-1">
        <Star className="w-3 h-3 text-accent animate-pulse" />
        <Heart className="w-3 h-3 text-primary animate-bounce" />
      </div>
    );
  };

  if (currentTheme === 'cute') {
    return (
      <Card className={cn(
        "cute-card relative overflow-hidden transition-all duration-300 hover:scale-[1.02]",
        "bg-card border-2 border-primary/30 shadow-lg rounded-2xl",
        "hover:shadow-primary/20 hover:border-primary/50",
        glowing && "glow-border",
        className
      )}>
        {getCuteDecorations()}
        <CardHeader className="bg-gradient-to-r from-primary/15 to-secondary/15 rounded-t-2xl pb-3">
          <CardTitle className="text-primary font-bold text-lg flex items-center justify-center gap-2">
            <Sparkles className="w-4 h-4 text-primary" />
            {title}
            <Heart className="w-4 h-4 text-accent" />
          </CardTitle>
        </CardHeader>
        <CardContent className="bg-card/90 text-cardForeground p-4 rounded-b-2xl">
          {children}
        </CardContent>
      </Card>
    );
  }

  // Other themes keep original style
  return (
    <Card className={cn(
      "divination-card",
      glowing && "glow-border",
      className
    )}>
      <CardHeader className="pb-4">
        <CardTitle className="gradient-text text-xl font-bold">
          {title}
        </CardTitle>
      </CardHeader>
      <CardContent>
        {children}
      </CardContent>
    </Card>
  );
};