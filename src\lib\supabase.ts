import { createClient } from '@supabase/supabase-js'

// 从环境变量获取配置
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

// 验证环境变量
if (!supabaseUrl) {
  throw new Error('Missing VITE_SUPABASE_URL environment variable')
}

if (!supabaseAnonKey) {
  throw new Error('Missing VITE_SUPABASE_ANON_KEY environment variable')
}

// 创建Supabase客户端
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
    flowType: 'pkce'
  }
})

// 数据库类型定义
export interface DivinationRecord {
  id?: string
  user_id: string
  hexagram_name: string
  hexagram_number: number
  original_hexagram: string
  changed_hexagram?: string
  coin_results: number[]
  divination_time: string
  question?: string
  interpretation?: string
  analysis_status?: 'pending' | 'analyzing' | 'completed' | 'failed'
  analysis_started_at?: string
  analysis_completed_at?: string
  analysis_type?: 'basic' | 'detailed'
  hexagram_data?: any // 存储完整的卦象数据，包含divinationInfo等信息
  created_at?: string
  updated_at?: string
}

export interface UserProfile {
  id: string
  email?: string
  username?: string
  created_at?: string
  updated_at?: string
}

// 调试函数：检查认证状态
export const debugAuthState = async () => {
  try {
    console.log('=== 认证状态调试 ===')

    // 检查localStorage中的token
    const authKeys = Object.keys(localStorage).filter(key => key.includes('auth'))
    console.log('localStorage中的认证相关keys:', authKeys)

    authKeys.forEach(key => {
      const value = localStorage.getItem(key)
      console.log(`${key}:`, value ? '存在' : '不存在')
    })

    // 检查当前用户
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    console.log('当前用户:', user?.email || '无用户', userError ? `错误: ${userError.message}` : '')

    // 检查session（可能会卡住）
    console.log('准备检查session...')
    const sessionPromise = supabase.auth.getSession()
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('getSession超时')), 5000)
    })

    try {
      const { data: { session }, error: sessionError } = await Promise.race([sessionPromise, timeoutPromise]) as any
      console.log('Session状态:', session?.user?.email || '无session', sessionError ? `错误: ${sessionError.message}` : '')
    } catch (timeoutError) {
      console.error('getSession调用超时:', timeoutError.message)
    }

    console.log('=== 调试结束 ===')
  } catch (error) {
    console.error('调试过程出错:', error)
  }
}
