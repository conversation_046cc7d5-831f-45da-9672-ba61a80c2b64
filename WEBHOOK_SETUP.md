# Stripe Webhook 设置指南

## 概述

本项目现在支持两种支付验证模式：
1. **Webhook模式**（推荐）：Stripe主动通知支付状态变化
2. **客户端验证模式**（备用）：前端主动查询支付状态

## 环境变量配置

在Supabase项目设置中添加以下环境变量：

```bash
# Stripe配置
STRIPE_SECRET_KEY=sk_test_...  # 你的Stripe密钥
STRIPE_WEBHOOK_SECRET=whsec_... # Webhook签名密钥

# Supabase配置（已存在）
SUPABASE_URL=https://...
SUPABASE_SERVICE_ROLE_KEY=...
```

## Stripe Webhook配置

### 1. 在Stripe Dashboard中创建Webhook

1. 登录 [Stripe Dashboard](https://dashboard.stripe.com/)
2. 进入 **Developers** > **Webhooks**
3. 点击 **Add endpoint**
4. 设置Endpoint URL：
   ```
   https://tpdslcjznbffqnpbuiiy.supabase.co/functions/v1/stripe-webhook
   ```
5. 选择要监听的事件：
   - `checkout.session.completed` - 支付成功完成
   - `payment_intent.succeeded` - 支付意图成功（备用处理）
   - `payment_intent.payment_failed` - 支付失败

### 2. 获取Webhook签名密钥

1. 创建webhook后，点击webhook详情
2. 在 **Signing secret** 部分，点击 **Reveal**
3. 复制 `whsec_...` 开头的密钥
4. 将此密钥设置为 `STRIPE_WEBHOOK_SECRET` 环境变量

## 数据库结构

### 新增的payment_logs表

```sql
CREATE TABLE public.payment_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  event_type TEXT NOT NULL,
  payment_status TEXT NOT NULL,
  amount INTEGER NOT NULL,
  currency TEXT DEFAULT 'usd',
  plan_name TEXT,
  reports_purchased INTEGER,
  metadata JSONB,
  created_at TIMESTAMPTZ DEFAULT now()
);
```

## Edge Functions

### 1. stripe-webhook
- **路径**: `/functions/v1/stripe-webhook`
- **功能**: 处理Stripe webhook事件
- **支持的事件**:
  - `checkout.session.completed`: 支付成功时更新用户积分和订单状态
  - `payment_intent.succeeded`: 支付意图成功时的备用处理（直接支付场景）
  - `payment_intent.payment_failed`: 支付失败时更新订单状态并记录失败日志

### 2. create-checkout-session
- **路径**: `/functions/v1/create-checkout-session`
- **功能**: 创建Stripe checkout session
- **改进**: 在session metadata中包含用户ID，便于webhook处理

## 支付流程

### Webhook模式（主要流程）
1. 用户点击购买按钮
2. 调用 `create-checkout-session` 创建支付会话
3. 用户在新标签页完成支付
4. Stripe发送webhook事件：
   - `checkout.session.completed` (主要事件)
   - `payment_intent.succeeded` (备用事件)
5. `stripe-webhook` 函数处理webhook：
   - 验证签名
   - 更新订单状态为 `paid`
   - 更新用户积分
   - 创建支付日志
6. 用户被重定向到成功页面

### 支付失败处理
- 当支付失败时，Stripe发送 `payment_intent.payment_failed` webhook
- `stripe-webhook` 函数会：
  - 更新订单状态为 `failed`
  - 记录失败日志
  - 不更新用户积分

### 客户端验证模式（备用流程）
1. 用户完成支付后被重定向到 `/payment-success`
2. 页面首先检查订单状态
3. 如果webhook已处理，直接显示成功
4. 如果webhook未处理，调用 `verify-payment` 进行客户端验证

## 测试

### 1. 使用Stripe CLI测试Webhook

```bash
# 安装Stripe CLI
stripe listen --forward-to localhost:54321/functions/v1/stripe-webhook

# 在另一个终端触发测试事件
stripe trigger checkout.session.completed
```

### 2. 查看日志

在Supabase Dashboard中查看Edge Function日志：
1. 进入 **Edge Functions**
2. 选择 `stripe-webhook`
3. 查看 **Logs** 标签

## 事件处理逻辑

### checkout.session.completed
- **触发时机**: 用户完成Stripe Checkout支付
- **处理逻辑**:
  1. 验证支付状态为 `paid`
  2. 从session metadata获取用户ID和积分信息
  3. 更新订单状态为 `paid`
  4. 更新用户积分
  5. 创建支付日志

### payment_intent.succeeded
- **触发时机**: 支付意图成功（直接支付或备用场景）
- **处理逻辑**:
  1. 从payment intent metadata获取用户信息
  2. 如果包含用户ID和积分信息，更新用户积分
  3. 创建支付日志
  4. 主要用于直接支付场景或作为checkout session的备用

### payment_intent.payment_failed
- **触发时机**: 支付失败
- **处理逻辑**:
  1. 更新相关订单状态为 `failed`
  2. 记录失败日志
  3. 不更新用户积分
  4. 可用于发送失败通知

## 故障排除

### 常见问题

1. **Webhook签名验证失败**
   - 检查 `STRIPE_WEBHOOK_SECRET` 是否正确
   - 确保webhook URL正确

2. **用户积分未更新**
   - 检查webhook是否收到 `checkout.session.completed` 事件
   - 查看Edge Function日志
   - 确认session metadata包含正确的 `user_id`

3. **订单状态未更新**
   - 检查数据库连接
   - 确认service role key有足够权限

### 调试步骤

1. 检查Supabase Edge Function日志
2. 验证Stripe webhook配置
3. 确认环境变量设置正确
4. 测试webhook端点是否可访问

## 安全考虑

1. **Webhook签名验证**: 所有webhook都经过签名验证
2. **环境变量**: 敏感信息存储在环境变量中
3. **数据库权限**: 使用service role key进行数据库操作
4. **CORS配置**: 适当的CORS头部设置

## 监控

建议监控以下指标：
- Webhook接收成功率
- 支付处理时间
- 用户积分更新成功率
- 错误日志频率 