import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { User, LogIn, LogOut, UserCircle } from 'lucide-react';
import { useAuthStore } from '@/stores/authStore';
import { AuthModal } from './AuthModal';
import { ThemeSwitcher } from './ThemeSwitcher';
import { LanguageSwitcher } from './LanguageSwitcher';
import { useTranslation } from '@/hooks/useTranslation';
import { useNavigate } from 'react-router-dom';

export const Header: React.FC = () => {
  const { user, loading, signOut } = useAuthStore();
  const [authModalOpen, setAuthModalOpen] = useState(false);
  const navigate = useNavigate();
  const { t } = useTranslation();

  const handleSignOut = async () => {
    await signOut();
  };

  const handleProfileClick = () => {
    navigate('/profile');
  };

  return (
    <>
      <header className="border-b border-border/50 bg-card/30 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto px-6 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h1 className="text-2xl font-bold gradient-text cursor-pointer" onClick={() => navigate('/')}>
              {t('appName')}
            </h1>
            <Badge variant="secondary" className="text-xs">
              {t('aiAnalysis')}
            </Badge>
          </div>

          <div className="flex items-center space-x-6">
            <LanguageSwitcher />
            <ThemeSwitcher />

            <Button 
              variant="ghost" 
              onClick={() => navigate('/pricing')}
              className="text-sm"
            >
              {t('pricing')}
            </Button>

            {loading ? (
              <Button variant="ghost" disabled>
                {t('loading')}
              </Button>
            ) : user ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="text-sm">
                    {t('profile')}
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={handleProfileClick}>
                    <UserCircle className="h-4 w-4 mr-2" />
                    {t('profilePage')}
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={handleSignOut}>
                    <LogOut className="h-4 w-4 mr-2" />
                    {t('logout')}
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <Button variant="default" onClick={() => setAuthModalOpen(true)}>
                {t('login')}
              </Button>
            )}
          </div>
        </div>
      </header>

      <AuthModal open={authModalOpen} onOpenChange={setAuthModalOpen} />
    </>
  );
};