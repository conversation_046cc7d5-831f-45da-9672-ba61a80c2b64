import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import <PERSON><PERSON> from "https://esm.sh/stripe@14.21.0";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.45.0";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

const logStep = (step: string, details?: any) => {
  const detailsStr = details ? ` - ${JSON.stringify(details)}` : '';
  console.log(`[VERIFY-PAYMENT] ${step}${detailsStr}`);
};

// Helper function to create payment log
async function createPaymentLog(supabaseClient: any, userId: string, session: any, amount: number, currency: string, planName: string, reportsPurchased: number) {
  try {
    console.log(`[${new Date().toISOString()}] Creating payment log for client verification - user: ${userId}`);
    
    const insertData = {
      user_id: userId,
      event_type: 'client_verification',
      payment_status: 'succeeded',
      amount: amount,
      currency: currency,
      plan_name: planName,
      reports_purchased: reportsPurchased,
      metadata: {
        session_id: session.id,
        payment_status: session.payment_status,
        customer_id: session.customer,
        client_verified: true
      }
    };
    
    const { data, error } = await supabaseClient
      .from('payment_logs')
      .insert(insertData)
      .select();
    
    if (error) {
      console.error('Payment log insert failed:', error);
      throw error;
    }
    
    console.log('Payment log created successfully for client verification:', data);
  } catch (error) {
    console.error('Payment log creation failed:', error);
    throw error;
  }
}

serve(async (req) => {
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    logStep("Function started");

    const { sessionId } = await req.json();
    if (!sessionId) throw new Error("Session ID is required");
    logStep("Session ID received", { sessionId });

    // Create Supabase client using the service role key
    const supabaseClient = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? "",
      { auth: { persistSession: false } }
    );

    // Initialize Stripe
    const stripeKey = Deno.env.get("STRIPE_SECRET_KEY");
    if (!stripeKey) throw new Error("STRIPE_SECRET_KEY is not set");
    const stripe = new Stripe(stripeKey, { apiVersion: "2023-10-16" });
    logStep("Stripe initialized");

    // Retrieve checkout session from Stripe
    const session = await stripe.checkout.sessions.retrieve(sessionId);
    logStep("Session retrieved", { status: session.payment_status, customerId: session.customer });

    if (session.payment_status === "paid") {
      // Update order status
      const { error: orderError } = await supabaseClient
        .from("orders")
        .update({ status: "paid" })
        .eq("stripe_session_id", sessionId);

      if (orderError) {
        logStep("Error updating order", { error: orderError });
        throw new Error(`Failed to update order: ${orderError.message}`);
      }
      logStep("Order updated to paid");

      // Get order details
      const { data: order, error: orderQueryError } = await supabaseClient
        .from("orders")
        .select("user_id, credits_purchased, amount, currency")
        .eq("stripe_session_id", sessionId)
        .single();

      if (orderQueryError) {
        logStep("Error querying order", { error: orderQueryError });
        throw new Error(`Failed to query order: ${orderQueryError.message}`);
      }

      logStep("Order found", { order });

      if (order) {
        // Update or create user credits
        const { data: existingCredits } = await supabaseClient
          .from("user_credits")
          .select("*")
          .eq("user_id", order.user_id)
          .single();

        if (existingCredits) {
          // Update existing credits
          const { error: creditsError } = await supabaseClient
            .from("user_credits")
            .update({
              credits_remaining: existingCredits.credits_remaining + order.credits_purchased,
              total_purchased: existingCredits.total_purchased + order.credits_purchased,
              last_purchase_at: new Date().toISOString(),
            })
            .eq("user_id", order.user_id);

          if (creditsError) {
            logStep("Error updating credits", { error: creditsError });
            throw new Error(`Failed to update credits: ${creditsError.message}`);
          }
          logStep("Credits updated", { newTotal: existingCredits.credits_remaining + order.credits_purchased });
        } else {
          // Create new credits record
          const { error: creditsError } = await supabaseClient
            .from("user_credits")
            .insert({
              user_id: order.user_id,
              credits_remaining: order.credits_purchased,
              total_purchased: order.credits_purchased,
              last_purchase_at: new Date().toISOString(),
            });

          if (creditsError) {
            logStep("Error creating credits", { error: creditsError });
            throw new Error(`Failed to create credits: ${creditsError.message}`);
          }
          logStep("Credits created", { credits: order.credits_purchased });
        }

        // Create payment log for client verification
        try {
          await createPaymentLog(
            supabaseClient,
            order.user_id,
            session,
            order.amount || 0,
            order.currency || 'usd',
            '六爻分析包', // 使用默认值，因为orders表中没有plan_name字段
            order.credits_purchased
          );
          logStep("Payment log created for client verification");
        } catch (logError) {
          logStep("Warning: Failed to create payment log", { error: logError });
          // Don't fail the entire operation if payment log creation fails
        }
      }

      return new Response(JSON.stringify({ 
        success: true, 
        payment_status: "paid",
        credits_added: order?.credits_purchased || 0
      }), {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 200,
      });
    } else {
      return new Response(JSON.stringify({ 
        success: false, 
        payment_status: session.payment_status 
      }), {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 200,
      });
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logStep("ERROR in verify-payment", { message: errorMessage });
    return new Response(JSON.stringify({ error: errorMessage }), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 500,
    });
  }
});