// 易经卦象常量定义

// 六神
export const SIX_SPIRITS = ['青龙', '朱雀', '勾陈', '螣蛇', '白虎', '玄武'] as const;

// 六亲  
export const SIX_RELATIONS = ['父母', '兄弟', '子孙', '妻财', '官鬼'] as const;

// 五行
export const FIVE_ELEMENTS = ['木', '火', '土', '金', '水'] as const;

// 八宫
export const EIGHT_PALACES = '乾兑离震巽坎艮坤';

// 八卦名称
export const EIGHT_TRIGRAMS = ['乾', '兑', '离', '震', '巽', '坎', '艮', '坤'] as const;

// 八卦对应五行索引（对应FIVE_ELEMENTS数组）
export const TRIGRAM_ELEMENTS = [3, 3, 1, 0, 0, 4, 2, 2] as const; // 金金火木木水土土

// 天干
export const HEAVENLY_STEMS = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'] as const;

// 地支
export const EARTHLY_BRANCHES = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'] as const;

// 地支五行索引
export const BRANCH_ELEMENTS = [4, 2, 0, 0, 2, 1, 1, 2, 3, 3, 2, 4] as const; // 水土木木土火火土金金土水

// 旬空
export const VOID_PAIRS = ['子丑', '寅卯', '辰巳', '午未', '申酉', '戌亥'] as const;

// 64卦二进制映射表（更精确的方法）
export const HEXAGRAM_BINARY_MAP: Record<string, string> = {
  '111111': '乾为天', '011111': '天风姤', '001111': '天山遁', '000111': '天地否', '000011': '风地观', '000001': '山地剥',
  '000101': '火地晋', '111101': '火天大有', '110110': '兑为泽', '010110': '泽水困', '000110': '泽地萃', '001110': '泽山咸',
  '001010': '水山蹇', '001000': '地山谦', '001100': '雷山小过', '110100': '雷泽归妹', '101101': '离为火', '001101': '火山旅',
  '011101': '火风鼎', '010101': '火水未济', '010001': '山水蒙', '010011': '风水涣', '010111': '天水讼', '101111': '天火同人',
  '100100': '震为雷', '000100': '雷地豫', '010100': '雷水解', '011100': '雷风恒', '011000': '地风升', '011010': '水风井',
  '011110': '泽风大过', '100110': '泽雷随', '011011': '巽为风', '111011': '风天小畜', '101011': '风火家人', '100011': '风雷益',
  '100111': '天雷无妄', '100101': '火雷噬嗑', '100001': '山雷颐', '011001': '山风蛊', '010010': '坎为水', '110010': '水泽节',
  '100010': '水雷屯', '101010': '水火既济', '101110': '泽火革', '101100': '雷火丰', '101000': '地火明夷', '010000': '地水师',
  '001001': '艮为山', '101001': '山火贲', '111001': '山天大畜', '110001': '山泽损', '110101': '火泽睽', '110111': '天泽履',
  '110011': '风泽中孚', '001011': '风山渐', '000000': '坤为地', '100000': '地雷复', '110000': '地泽临', '111000': '地天泰',
  '111100': '雷天大壮', '111110': '泽天夬', '111010': '水天需', '000010': '水地比'
};

// 从卦名反向查找编号
export const HEXAGRAM_NAME_TO_NUMBER: Record<string, number> = {};
Object.entries(HEXAGRAM_BINARY_MAP).forEach(([binary, name], index) => {
  HEXAGRAM_NAME_TO_NUMBER[name] = index + 1;
});

// 24节气
export const SOLAR_TERMS = [
  '冬至', '小寒', '大寒', '立春', '雨水', '惊蛰', '春分', '清明', '谷雨', '立夏', '小满',
  '芒种', '夏至', '小暑', '大暑', '立秋', '处暑', '白露', '秋分', '寒露', '霜降', '立冬',
  '小雪', '大雪'
] as const;